Metadata-Version: 2.1
Name: raiutils
Version: 0.4.2
Summary: Common basic utilities used across various RAI tools
Home-page: https://github.com/microsoft/responsible-ai-widgets
Author: <PERSON>, <PERSON><PERSON>, <PERSON>
Author-email: <EMAIL>
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Development Status :: 3 - Alpha
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: numpy
Requires-Dist: pandas
Requires-Dist: requests
Requires-Dist: scikit-learn
Requires-Dist: scipy

# Responsible AI Utilities for Python

### This package has been tested with Python 3.7, 3.8, 3.9, 3.10 and 3.11

The Responsible AI Utilities package contains common functions shared across various RAI tools, including fairlearn, interpret-community, responsibleai, raiwidgets and other packages, as well as notebook examples.

Please see the main documentation website:
https://responsibleaitoolbox.ai/

The open source code can be found here:
https://github.com/microsoft/responsible-ai-widgets
