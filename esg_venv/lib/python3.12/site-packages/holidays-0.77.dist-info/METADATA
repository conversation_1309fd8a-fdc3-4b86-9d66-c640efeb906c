Metadata-Version: 2.4
Name: holidays
Version: 0.77
Summary: Open World Holidays Framework
Author: Vacanza Team
Maintainer: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
License-Expression: MIT
Project-URL: Documentation, https://holidays.readthedocs.io/en/latest/
Project-URL: Repository, https://github.com/vacanza/holidays/
Project-URL: Changelog, https://github.com/vacanza/holidays/releases/
Keywords: holidays,calendar,l10n,worldwide,vacation
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: Financial and Insurance Industry
Classifier: Intended Audience :: Information Technology
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Topic :: File Formats :: JSON
Classifier: Topic :: Office/Business :: Financial :: Accounting
Classifier: Topic :: Office/Business :: Financial :: Investment
Classifier: Topic :: Office/Business :: Scheduling
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: CONTRIBUTORS
License-File: LICENSE
Requires-Dist: python-dateutil
Dynamic: license-file

# Holidays

A fast, efficient Python library for generating country- and subdivision- (e.g. state or province)
specific sets of government-designated holidays on the fly. It aims to make determining whether a
specific date is a holiday as fast and flexible as possible.

<!-- markdownlint-disable MD033 -->
<table>
  <tr>
    <td>PyPI</td>
    <td>
      <a href="https://pypi.org/project/holidays"><img src="https://img.shields.io/pypi/dm/holidays?color=41B5BE&style=flat" alt="PyPI downloads"></a>&nbsp;<a href="https://pypi.org/project/holidays"><img src="https://img.shields.io/pypi/v/holidays?color=41B5BE&label=version&style=flat" alt="PyPI version"></a>&nbsp;<a href="https://github.com/vacanza/holidays/releases"><img src="https://img.shields.io/github/release-date/vacanza/holidays?color=41B5BE&style=flat" alt="PyPI release date"></a>
    </td>
  </tr>
  <tr>
    <td>CI/CD</td>
    <td>
      <a href="https://github.com/vacanza/holidays/actions/workflows/ci-cd.yml?query=branch%3Adev"><img src="https://img.shields.io/github/actions/workflow/status/vacanza/holidays/ci-cd.yml?branch=dev&color=41BE4A&style=flat" alt="CI/CD status"></a>&nbsp;<a href="https://holidays.readthedocs.io/en/latest/?badge=latest"><img src="https://img.shields.io/readthedocs/holidays?color=41BE4A&style=flat" alt="Documentation status"></a>
    </td>
  </tr>
  <tr>
    <td>Code</td>
    <td>
      <a href="https://github.com/vacanza/holidays/blob/dev/LICENSE"><img src="https://img.shields.io/github/license/vacanza/holidays?color=41B5BE&style=flat" alt="License"></a>&nbsp;<a href="https://pypi.org/project/holidays"><img src="https://img.shields.io/pypi/pyversions/holidays?label=python&color=41B5BE&style=flat" alt="Python supported versions"></a>&nbsp;<a href="https://github.com/astral-sh/ruff"><img src="https://img.shields.io/badge/style-ruff-41B5BE?style=flat" alt="Code style"></a>&nbsp;<a href="https://app.codecov.io/gh/vacanza/holidays"><img src="https://img.shields.io/codecov/c/github/vacanza/holidays/dev?color=41B5BE&style=flat" alt="Code coverage"></a>
    </td>
  </tr>
  <tr>
    <td>GitHub</td>
    <td>
      <a href="https://github.com/vacanza/holidays/stargazers"><img src="https://img.shields.io/github/stars/vacanza/holidays?color=41BE4A&style=flat" alt="GitHub stars"></a>&nbsp;<a href="https://github.com/vacanza/holidays/forks"><img src="https://img.shields.io/github/forks/vacanza/holidays?color=41BE4A&style=flat" alt="GitHub forks"></a>&nbsp;<a href="https://github.com/vacanza/holidays/graphs/contributors"><img src="https://img.shields.io/github/contributors/vacanza/holidays?color=41BE4A&style=flat" alt="GitHub contributors"></a>&nbsp;<a href="https://github.com/vacanza/holidays/commits/dev"><img src="https://img.shields.io/github/last-commit/vacanza/holidays/dev?color=41BE4A&style=flat" alt="GitHub last commit"></a>
    </td>
  </tr>
  <tr>
    <td>Citation</td>
    <td>
      <a href="https://doi.org/10.5281/zenodo.14884702"><img src="https://img.shields.io/badge/DOI-10.5281/zenodo.14884702-41B5BE?style=flat" alt="Open World Holidays Framework DOI"></a>
    </td>
  </tr>
  <tr>
    <td>Snyk</td>
    <td>
      <a href="https://snyk.io/advisor/python/holidays/"><img src="https://snyk.io/advisor/python/holidays/badge.svg" alt="Open World Holidays Package Health Score"></a>
    </td>
  </tr>
</table>

## Install

The latest stable version can always be installed or updated via pip:

``` shell
pip install --upgrade holidays
```

The latest development (dev) version can be installed directly from GitHub:

``` shell
pip install --upgrade https://github.com/vacanza/holidays/tarball/dev
```

All new features are always first pushed to dev branch, then released on main branch upon official
version upgrades.

## Documentation

The documentation is hosted on [Read the Docs](https://holidays.readthedocs.io/).

## Quick Start

``` python
from datetime import date
import holidays

us_holidays = holidays.US()  # this is a dict-like object
# the below is the same, but takes a string:
us_holidays = holidays.country_holidays('US')  # this is a dict-like object

nyse_holidays = holidays.NYSE()  # this is a dict-like object
# the below is the same, but takes a string:
nyse_holidays = holidays.financial_holidays('NYSE')  # this is a dict-like object

date(2015, 1, 1) in us_holidays  # True
date(2015, 1, 2) in us_holidays  # False
us_holidays.get('2014-01-01')  # "New Year's Day"
```

The HolidayBase dict-like class will also recognize date strings and Unix timestamps:

``` python
'2014-01-01' in us_holidays  # True
'1/1/2014' in us_holidays    # True
1388597445 in us_holidays    # True
```

Some holidays may be only present in parts of a country:

``` python
us_pr_holidays = holidays.country_holidays('US', subdiv='PR')
'2018-01-06' in us_holidays     # False
'2018-01-06' in us_pr_holidays  # True
```

Please see the [holidays documentation](https://holidays.readthedocs.io/) for additional examples
and detailed information.

## Available Countries

We currently support 223 country codes. The standard way to refer to a country is by using its [ISO
3166-1 alpha-2 code](https://en.wikipedia.org/wiki/List_of_ISO_3166_country_codes), the same used
for domain names, and for a subdivision its [ISO 3166-2
code](https://en.wikipedia.org/wiki/ISO_3166-2). Some countries have common or foreign names or
abbreviations as aliases for their subdivisions. These are defined in the (optional)
`subdivisions_aliases` attribute. Some of the countries support more than one language for holiday
names output. A default language is defined by `default_language` (optional) attribute for each
entity and is used as a fallback when neither user specified language nor user locale language
available. The default language code is a [ISO 639-1
code](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes). A list of all languages supported by
country is defined by `supported_languages` (optional) attribute. If there is no designated [ISO
639-1 code](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) then [ISO 639-2
code](https://en.wikipedia.org/wiki/List_of_ISO_639-2_codes) can be used.

Many countries have other categories of holidays in addition to common (national-wide) holidays:
bank holidays, school holidays, additional (paid or non-paid) holidays, holidays of state or public
employees, religious holidays (valid only for these religions followers). A list of all categories
supported by country is defined by `supported_categories` (optional) attribute.

The following is a list of supported countries, their subdivisions followed by their aliases (if
any) in brackets, available languages and additional holiday categories. All countries support
**PUBLIC** holidays category by default. All other default values are highlighted with bold:

<table style="width: 100%">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 4.0%" />
<col style="width: 46.0%" />
<col style="width: 20.0%" />
<col style="width: 10.0%" />
</colgroup>
<thead>
<tr>
<th>Country</th>
<th>Code</th>
<th>Subdivisions</th>
<th>Supported Languages</th>
<th>Supported Categories</th>
</tr>
</thead>
<tbody>
<tr>
<td>Afghanistan</td>
<td>AF</td>
<td></td>
<td>en_US, <strong>fa_AF</strong>, ps_AF</td>
<td></td>
</tr>
<tr>
<td>Aland Islands</td>
<td>AX</td>
<td>Can also be loaded as country FI, subdivision 01</td>
<td>en_US, <strong>fi</strong>, sv_FI, th, uk</td>
<td>UNOFFICIAL, WORKDAY</td>
</tr>
<tr>
<td>Albania</td>
<td>AL</td>
<td></td>
<td>en_US, <strong>sq</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Algeria</td>
<td>DZ</td>
<td></td>
<td><strong>ar</strong>, en_US, fr</td>
<td></td>
</tr>
<tr>
<td>American Samoa</td>
<td>AS</td>
<td>Can also be loaded as country US, subdivision AS</td>
<td><strong>en_US</strong>, th</td>
<td>GOVERNMENT, UNOFFICIAL</td>
</tr>
<tr>
<td>Andorra</td>
<td>AD</td>
<td>Parishes: 02 (Canillo), 03 (Encamp), 04 (La Massana), 05 (Ordino), 06 (Sant Julià de Lòria), 07 (Andorra la Vella), 08 (Escaldes-Engordany)</td>
<td><strong>ca</strong>, en_US, uk</td>
<td>GOVERNMENT</td>
</tr>
<tr>
<td>Angola</td>
<td>AO</td>
<td></td>
<td>en_US, <strong>pt_AO</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Anguilla</td>
<td>AI</td>
<td></td>
<td><strong>en_AI</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Antigua and Barbuda</td>
<td>AG</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Argentina</td>
<td>AR</td>
<td>Provinces: A (Salta), B (Buenos Aires), C (Ciudad Autónoma de Buenos Aires), D (San Luis), E (Entre Ríos), F (La Rioja), G (Santiago del Estero), H (Chaco), J (San Juan), K (Catamarca), L (La Pampa), M (Mendoza), N (Misiones), P (Formosa), Q (Neuquén), R (Río Negro), S (Santa Fe), T (Tucumán), U (Chubut), V (Tierra del Fuego), W (Corrientes), X (Córdoba), Y (Jujuy), Z (Santa Cruz)</td>
<td>en_US, <strong>es</strong>, uk</td>
<td>ARMENIAN, BANK, GOVERNMENT, HEBREW, ISLAMIC</td>
</tr>
<tr>
<td>Armenia</td>
<td>AM</td>
<td></td>
<td>en_US, <strong>hy</strong></td>
<td></td>
</tr>
<tr>
<td>Aruba</td>
<td>AW</td>
<td></td>
<td>en_US, nl, <strong>pap_AW</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Australia</td>
<td>AU</td>
<td>States and territories: ACT (Australian Capital Territory), NSW (New South Wales), NT (Northern Territory), QLD (Queensland), SA (South Australia), TAS (Tasmania), VIC (Victoria), WA (Western Australia)</td>
<td><strong>en_AU</strong>, en_US, th</td>
<td>BANK, HALF_DAY</td>
</tr>
<tr>
<td>Austria</td>
<td>AT</td>
<td>States: 1 (Burgenland, Bgld, B), 2 (Kärnten, Ktn, K), 3 (Niederösterreich, NÖ, N), 4 (Oberösterreich, OÖ, O), 5 (Salzburg, Sbg, S), 6 (Steiermark, Stmk, St), 7 (Tirol, T), 8 (Vorarlberg, Vbg, V), 9 (Wien, W)</td>
<td><strong>de</strong>, en_US, uk</td>
<td>BANK</td>
</tr>
<tr>
<td>Azerbaijan</td>
<td>AZ</td>
<td></td>
<td><strong>az</strong>, en_US, uk</td>
<td>WORKDAY</td>
</tr>
<tr>
<td>Bahamas</td>
<td>BS</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Bahrain</td>
<td>BH</td>
<td></td>
<td><strong>ar</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Bangladesh</td>
<td>BD</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Barbados</td>
<td>BB</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Belarus</td>
<td>BY</td>
<td></td>
<td><strong>be</strong>, en_US, ru, th</td>
<td>WORKDAY</td>
</tr>
<tr>
<td>Belgium</td>
<td>BE</td>
<td></td>
<td>de, en_US, fr, <strong>nl</strong>, uk</td>
<td>BANK</td>
</tr>
<tr>
<td>Belize</td>
<td>BZ</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Benin</td>
<td>BJ</td>
<td></td>
<td>en_US, <strong>fr_BJ</strong></td>
<td>WORKDAY</td>
</tr>
<tr>
<td>Bermuda</td>
<td>BM</td>
<td></td>
<td><strong>en_BM</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Bolivia</td>
<td>BO</td>
<td>Departments: B (El Beni), C (Cochabamba), H (Chuquisaca), L (La Paz), N (Pando), O (Oruro), P (Potosí), S (Santa Cruz), T (Tarija)</td>
<td>en_US, <strong>es</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Bonaire, Sint Eustatius and Saba</td>
<td>BQ</td>
<td>Subdivisions: BON (Bonaire), SAB (Saba), STA (Sint Eustatius)</td>
<td>en_BQ, en_US, <strong>nl</strong>, pap_BQ</td>
<td></td>
</tr>
<tr>
<td>Bosnia and Herzegovina</td>
<td>BA</td>
<td>Entities and district: BIH (Federacija Bosne i Hercegovine, FBiH), BRC (Brčko distrikt, BD), SRP (Republika Srpska, RS)</td>
<td><strong>bs</strong>, en_US, sr, uk</td>
<td></td>
</tr>
<tr>
<td>Botswana</td>
<td>BW</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Brazil</td>
<td>BR</td>
<td>States: AC (Acre), AL (Alagoas), AM (Amazonas), AP (Amapá), BA (Bahia), CE (Ceará), DF (Distrito Federal), ES (Espírito Santo), GO (Goiás), MA (Maranhão), MG (Minas Gerais), MS (Mato Grosso do Sul), MT (Mato Grosso), PA (Pará), PB (Paraíba), PE (Pernambuco), PI (Piauí), PR (Paraná), RJ (Rio de Janeiro), RN (Rio Grande do Norte), RO (Rondônia), RR (Roraima), RS (Rio Grande do Sul), SC (Santa Catarina), SE (Sergipe), SP (São Paulo), TO (Tocantins)</td>
<td>en_US, <strong>pt_BR</strong>, uk</td>
<td>OPTIONAL</td>
</tr>
<tr>
<td>British Virgin Islands</td>
<td>VG</td>
<td></td>
<td>en_US, <strong>en_VG</strong></td>
<td></td>
</tr>
<tr>
<td>Brunei</td>
<td>BN</td>
<td></td>
<td>en_US, <strong>ms</strong>, th</td>
<td></td>
</tr>
<tr>
<td>Bulgaria</td>
<td>BG</td>
<td></td>
<td><strong>bg</strong>, en_US, uk</td>
<td>SCHOOL</td>
</tr>
<tr>
<td>Burkina Faso</td>
<td>BF</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Burundi</td>
<td>BI</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Cabo Verde</td>
<td>CV</td>
<td>Municipalities: BR (Brava), BV (Boa Vista), CA (Santa Catarina), CF (Santa Catarina do Fogo), CR (Santa Cruz), MA (Maio), MO (Mosteiros), PA (Paul), PN (Porto Novo), PR (Praia), RB (Ribeira Brava), RG (Ribeira Grande), RS (Ribeira Grande de Santiago), SD (São Domingos), SF (São Filipe), SL (Sal), SM (São Miguel), SO (São Lourenço dos Órgãos), SS (São Salvador do Mundo), SV (São Vicente), TA (Tarrafal), TS (Tarrafal de São Nicolau)</td>
<td>de, en_US, es, fr, <strong>pt_CV</strong></td>
<td>OPTIONAL</td>
</tr>
<tr>
<td>Cambodia</td>
<td>KH</td>
<td></td>
<td>en_US, <strong>km</strong>, th</td>
<td></td>
</tr>
<tr>
<td>Cameroon</td>
<td>CM</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Canada</td>
<td>CA</td>
<td>Provinces and territories: AB (Alberta), BC (British Columbia, Colombie-Britannique), MB (Manitoba), NB (New Brunswick, Nouveau-Brunswick), NL (Newfoundland and Labrador, Terre-Neuve-et-Labrador), NS (Nova Scotia, Nouvelle-Écosse), NT (Northwest Territories, Territoires du Nord-Ouest), NU (Nunavut), ON (Ontario), PE (Prince Edward Island, Île-du-Prince-Édouard), QC (Quebec, Québec), SK (Saskatchewan), YT (Yukon)</td>
<td>ar, <strong>en_CA</strong>, en_US, fr, th</td>
<td>GOVERNMENT, OPTIONAL</td>
</tr>
<tr>
<td>Cayman Islands</td>
<td>KY</td>
<td></td>
<td><strong>en_GB</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Central African Republic</td>
<td>CF</td>
<td></td>
<td>en_US, <strong>fr</strong></td>
<td></td>
</tr>
<tr>
<td>Chad</td>
<td>TD</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Chile</td>
<td>CL</td>
<td>Regions: AI (Aisén del General Carlos Ibañez del Campo), AN (Antofagasta), AP (Arica y Parinacota), AR (La Araucanía), AT (Atacama), BI (Biobío), CO (Coquimbo), LI (Libertador General Bernardo O'Higgins), LL (Los Lagos), LR (Los Ríos), MA (Magallanes), ML (Maule), NB (Ñuble), RM (Región Metropolitana de Santiago), TA (Tarapacá), VS (Valparaíso)</td>
<td>en_US, <strong>es</strong>, uk</td>
<td>BANK</td>
</tr>
<tr>
<td>China</td>
<td>CN</td>
<td></td>
<td>en_US, th, <strong>zh_CN</strong>, zh_TW</td>
<td>HALF_DAY</td>
</tr>
<tr>
<td>Christmas Island</td>
<td>CX</td>
<td></td>
<td><strong>en_CX</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Cocos Islands</td>
<td>CC</td>
<td></td>
<td>coa_CC, <strong>en_CC</strong>,  en_US</td>
<td></td>
</tr>
<tr>
<td>Colombia</td>
<td>CO</td>
<td></td>
<td>en_US, <strong>es</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Congo</td>
<td>CG</td>
<td></td>
<td>en_US, <strong>fr</strong></td>
<td></td>
</tr>
<tr>
<td>Cook Islands</td>
<td>CK</td>
<td></td>
<td><strong>en_CK</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Costa Rica</td>
<td>CR</td>
<td></td>
<td>en_US, <strong>es</strong>, uk</td>
<td>OPTIONAL</td>
</tr>
<tr>
<td>Croatia</td>
<td>HR</td>
<td></td>
<td>en_US, <strong>hr</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Cuba</td>
<td>CU</td>
<td></td>
<td>en_US, <strong>es</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Curacao</td>
<td>CW</td>
<td></td>
<td>en_US, nl, <strong>pap_CW</strong>, uk</td>
<td>HALF_DAY</td>
</tr>
<tr>
<td>Cyprus</td>
<td>CY</td>
<td></td>
<td><strong>el</strong>, en_CY, en_US, uk</td>
<td>BANK, OPTIONAL</td>
</tr>
<tr>
<td>Czechia</td>
<td>CZ</td>
<td></td>
<td><strong>cs</strong>, en_US, sk, uk</td>
<td></td>
</tr>
<tr>
<td>Denmark</td>
<td>DK</td>
<td></td>
<td><strong>da</strong>, en_US, uk</td>
<td>OPTIONAL</td>
</tr>
<tr>
<td>Djibouti</td>
<td>DJ</td>
<td></td>
<td>ar, en_US, <strong>fr</strong></td>
<td></td>
</tr>
<tr>
<td>Dominica</td>
<td>DM</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Dominican Republic</td>
<td>DO</td>
<td></td>
<td>en_US, <strong>es</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>DR Congo</td>
<td>CD</td>
<td></td>
<td>en_US, <strong>fr</strong></td>
<td></td>
</tr>
<tr>
<td>Ecuador</td>
<td>EC</td>
<td></td>
<td>en_US, <strong>es</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Egypt</td>
<td>EG</td>
<td></td>
<td><strong>ar</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>El Salvador</td>
<td>SV</td>
<td>Departments: AH (Ahuachapán), CA (Cabañas), CH (Chalatenango), CU (Cuscatlán), LI (La Libertad), MO (Morazán), PA (La Paz), SA (Santa Ana), SM (San Miguel), SO (Sonsonate), SS (San Salvador), SV (San Vicente), UN (La Unión), US (Usulután)</td>
<td>en_US, <strong>es</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Equatorial Guinea</td>
<td>GQ</td>
<td>Provinces: AN (Annobón, Annobon), BN (Bioko Norte, North Bioko), BS (Bioko Sur, South Bioko), CS (Centro Sur, South Center), DJ (Djibloho), KN (Kié-Ntem, Kie-Ntem), LI (Litoral, Coast), WN (Wele-Nzas)</td>
<td>en_US, <strong>es</strong></td>
<td></td>
</tr>
<tr>
<td>Estonia</td>
<td>EE</td>
<td></td>
<td>en_US, <strong>et</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Eswatini</td>
<td>SZ</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Ethiopia</td>
<td>ET</td>
<td></td>
<td><strong>am</strong>, ar, en_ET, en_US</td>
<td>WORKDAY</td>
</tr>
<tr>
<td>Falkland Islands</td>
<td>FK</td>
<td></td>
<td><strong>en_GB</strong>, en_US</td>
<td>GOVERNMENT, WORKDAY</td>
</tr>
<tr>
<td>Faroe Islands</td>
<td>FO</td>
<td></td>
<td>da, en_US, <strong>fo</strong>, is, no, sv</td>
<td>HALF_DAY</td>
</tr>
<tr>
<td>Fiji</td>
<td>FJ</td>
<td></td>
<td></td>
<td>WORKDAY</td>
</tr>
<tr>
<td>Finland</td>
<td>FI</td>
<td>Regions: 01 (Ahvenanmaan maakunta, Landskapet Åland), 02 (Etelä-Karjala, Södra Karelen), 03 (Etelä-Pohjanmaa, Södra Österbotten), 04 (Etelä-Savo, Södra Savolax), 05 (Kainuu, Kajanaland), 06 (Kanta-Häme, Egentliga Tavastland), 07 (Keski-Pohjanmaa, Mellersta Österbotten), 08 (Keski-Suomi, Mellersta Finland), 09 (Kymenlaakso, Kymmenedalen), 10 (Lappi, Lappland), 11 (Pirkanmaa, Birkaland), 12 (Pohjanmaa, Österbotten), 13 (Pohjois-Karjala, Norra Karelen), 14 (Pohjois-Pohjanmaa, Norra Österbotten), 15 (Pohjois-Savo, Norra Savolax), 16 (Päijät-Häme, Päijänne-Tavastland), 17 (Satakunta), 18 (Uusimaa, Nyland), 19 (Varsinais-Suomi, Egentliga Finland)</td>
<td>en_US, <strong>fi</strong>, sv_FI, th, uk</td>
<td>UNOFFICIAL, WORKDAY</td>
</tr>
<tr>
<td>France</td>
<td>FR</td>
<td>Departments/European Collectivity/DOM/ROM/TOM: 57 (Moselle), 6AE (Alsace), 971 (GP, GUA, Guadeloupe), 972 (MQ, Martinique), 973 (GY, Guyane), 974 (RE, LRE, La Réunion), 976 (YT, MAY, Mayotte), BL (Saint-Barthélemy), MF (Saint-Martin), NC (Nouvelle-Calédonie), PF (Polynésie Française), PM (Saint-Pierre-et-Miquelon), TF (Terres australes françaises), WF (Wallis-et-Futuna)</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>French Guiana</td>
<td>GF</td>
<td>Can also be loaded as country FR, subdivision 973</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>French Polynesia</td>
<td>PF</td>
<td>Can also be loaded as country FR, subdivision PF</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>French Southern Territories</td>
<td>TF</td>
<td>Can also be loaded as country FR, subdivision TF</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>Gabon</td>
<td>GA</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Georgia</td>
<td>GE</td>
<td></td>
<td>en_US, <strong>ka</strong>, uk</td>
<td>GOVERNMENT</td>
</tr>
<tr>
<td>Germany</td>
<td>DE</td>
<td>Lands: BB (Brandenburg), BE (Berlin), BW (Baden-Württemberg), BY (Bayern), HB (Bremen), HE (Hessen), HH (Hamburg), MV (Mecklenburg-Vorpommern), NI (Niedersachsen), NW (Nordrhein-Westfalen), RP (Rheinland-Pfalz), SH (Schleswig-Holstein), SL (Saarland), SN (Sachsen), ST (Sachsen-Anhalt), TH (Thüringen)</td>
<td><strong>de</strong>, en_US, th, uk</td>
<td>CATHOLIC</td>
</tr>
<tr>
<td>Ghana</td>
<td>GH</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Gibraltar</td>
<td>GI</td>
<td></td>
<td><strong>en_GB</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Greece</td>
<td>GR</td>
<td></td>
<td><strong>el</strong>, en_US, uk</td>
<td>HALF_DAY</td>
</tr>
<tr>
<td>Greenland</td>
<td>GL</td>
<td></td>
<td>da, en_US, fi, is, <strong>kl</strong>, no, sv, uk</td>
<td>OPTIONAL</td>
</tr>
<tr>
<td>Grenada</td>
<td>GD</td>
<td></td>
<td><strong>en_GD</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Guadeloupe</td>
<td>GP</td>
<td>Can also be loaded as country FR, subdivision 971</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>Guam</td>
<td>GU</td>
<td>Can also be loaded as country US, subdivision GU</td>
<td><strong>en_US</strong>, th</td>
<td>GOVERNMENT, UNOFFICIAL</td>
</tr>
<tr>
<td>Guatemala</td>
<td>GT</td>
<td></td>
<td>en_US, <strong>es</strong></td>
<td></td>
</tr>
<tr>
<td>Guernsey</td>
<td>GG</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Guinea</td>
<td>GN</td>
<td></td>
<td>en_US, <strong>fr</strong></td>
<td></td>
</tr>
<tr>
<td>Guyana</td>
<td>GY</td>
<td></td>
<td><strong>en_GY</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Haiti</td>
<td>HT</td>
<td></td>
<td>en_US, es, <strong>fr_HT</strong>, ht</td>
<td>OPTIONAL</td>
</tr>
<tr>
<td>Honduras</td>
<td>HN</td>
<td></td>
<td>en_US, <strong>es</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Hong Kong</td>
<td>HK</td>
<td></td>
<td>en_HK, en_US, th, zh_CN, <strong>zh_HK</strong></td>
<td>OPTIONAL</td>
</tr>
<tr>
<td>Hungary</td>
<td>HU</td>
<td></td>
<td>en_US, <strong>hu</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Iceland</td>
<td>IS</td>
<td></td>
<td>en_US, <strong>is</strong>, uk</td>
<td>HALF_DAY</td>
</tr>
<tr>
<td>India</td>
<td>IN</td>
<td>States: AN (Andaman and Nicobar Islands), AP (Andhra Pradesh), AR (Arunachal Pradesh, Arunāchal Pradesh), AS (Assam), BR (Bihar, Bihār), CG (Chhattisgarh, Chhattīsgarh), CH (Chandigarh, Chandīgarh), DH (Dadra and Nagar Haveli and Daman and Diu, Dādra and Nagar Haveli and Damān and Diu), DL (Delhi), GA (Goa), GJ (Gujarat, Gujarāt), HP (Himachal Pradesh, Himāchal Pradesh), HR (Haryana, Haryāna), JH (Jharkhand, Jhārkhand), JK (Jammu and Kashmir, Jammu and Kashmīr), KA (Karnataka, Karnātaka), KL (Kerala), LA (Ladakh, Ladākh), LD (Lakshadweep), MH (Maharashtra, Mahārāshtra), ML (Meghalaya, Meghālaya), MN (Manipur), MP (Madhya Pradesh), MZ (Mizoram), NL (Nagaland, Nāgāland), OD (Odisha), PB (Punjab), PY (Puducherry), RJ (Rajasthan, Rājasthān), SK (Sikkim), TN (Tamil Nadu, Tamil Nādu), TR (Tripura), TS (Telangana, Telangāna), UK (Uttarakhand, Uttarākhand), UP (Uttar Pradesh), WB (West Bengal)</td>
<td><strong>en_IN</strong>, en_US, hi</td>
<td>OPTIONAL</td>
</tr>
<tr>
<td>Indonesia</td>
<td>ID</td>
<td></td>
<td>en_US, <strong>id</strong>, th, uk</td>
<td>GOVERNMENT</td>
</tr>
<tr>
<td>Iran</td>
<td>IR</td>
<td></td>
<td>en_US, <strong>fa_IR</strong></td>
<td></td>
</tr>
<tr>
<td>Ireland</td>
<td>IE</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Isle of Man</td>
<td>IM</td>
<td></td>
<td><strong>en_GB</strong>, en_US, th</td>
<td></td>
</tr>
<tr>
<td>Israel</td>
<td>IL</td>
<td></td>
<td>en_US, <strong>he</strong>, th, uk</td>
<td>OPTIONAL, SCHOOL</td>
</tr>
<tr>
<td>Italy</td>
<td>IT</td>
<td>Provinces: AG (Agrigento), AL (Alessandria), AN (Ancona), AO (Aosta), AP (Ascoli Piceno), AQ (L'Aquila), AR (Arezzo), AT (Asti), AV (Avellino), BA (Bari), BG (Bergamo), BI (Biella), BL (Belluno), BN (Benevento), BO (Bologna), BR (Brindisi), BS (Brescia), BT (Barletta-Andria-Trani), BZ (Bolzano), CA (Cagliari), CB (Campobasso), CE (Caserta), CH (Chieti), CL (Caltanissetta), CN (Cuneo), CO (Como), CR (Cremona), CS (Cosenza), CT (Catania), CZ (Catanzaro), EN (Enna), FC (Forli-Cesena, Forlì-Cesena), FE (Ferrara), FG (Foggia), FI (Firenze), FM (Fermo), FR (Frosinone), GE (Genova), GO (Gorizia), GR (Grosseto), IM (Imperia), IS (Isernia), KR (Crotone), LC (Lecco), LE (Lecce), LI (Livorno), LO (Lodi), LT (Latina), LU (Lucca), MB (Monza e Brianza), MC (Macerata), ME (Messina), MI (Milano), MN (Mantova), MO (Modena), MS (Massa-Carrara), MT (Matera), NA (Napoli), NO (Novara), NU (Nuoro), OR (Oristano), PA (Palermo), PC (Piacenza), PD (Padova), PE (Pescara), PG (Perugia), PI (Pisa), PN (Pordenone), PO (Prato), PR (Parma), PT (Pistoia), PU (Pesaro e Urbino), PV (Pavia), PZ (Potenza), RA (Ravenna), RC (Reggio Calabria), RE (Reggio Emilia), RG (Ragusa), RI (Rieti), RM (Roma), RN (Rimini), RO (Rovigo), SA (Salerno), SI (Siena), SO (Sondrio), SP (La Spezia), SR (Siracusa), SS (Sassari), SU (Sud Sardegna), SV (Savona), TA (Taranto), TE (Teramo), TN (Trento), TO (Torino), TP (Trapani), TR (Terni), TS (Trieste), TV (Treviso), UD (Udine), VA (Varese), VB (Verbano-Cusio-Ossola), VC (Vercelli), VE (Venezia), VI (Vicenza), VR (Verona), VT (Viterbo), VV (Vibo Valentia); cities: Andria, Barletta, Cesena, Forli (Forlì), Pesaro, Trani, Urbino</td>
<td></td>
<td></td>
</tr>
<tr>
<td>Ivory Coast</td>
<td>CI</td>
<td></td>
<td>en_CI, en_US, <strong>fr</strong></td>
<td></td>
</tr>
<tr>
<td>Jamaica</td>
<td>JM</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Japan</td>
<td>JP</td>
<td></td>
<td>en_US, <strong>ja</strong>, th</td>
<td>BANK</td>
</tr>
<tr>
<td>Jersey</td>
<td>JE</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Jordan</td>
<td>JO</td>
<td></td>
<td><strong>ar</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Kazakhstan</td>
<td>KZ</td>
<td></td>
<td>en_US, <strong>kk</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Kenya</td>
<td>KE</td>
<td></td>
<td><strong>en_KE</strong>, en_US, sw</td>
<td>HINDU, ISLAMIC</td>
</tr>
<tr>
<td>Kuwait</td>
<td>KW</td>
<td></td>
<td><strong>ar</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Kyrgyzstan</td>
<td>KG</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Laos</td>
<td>LA</td>
<td></td>
<td>en_US, <strong>lo</strong>, th</td>
<td>BANK, SCHOOL, WORKDAY</td>
</tr>
<tr>
<td>Latvia</td>
<td>LV</td>
<td></td>
<td>en_US, <strong>lv</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Lebanon</td>
<td>LB</td>
<td></td>
<td><strong>ar</strong>, en_US</td>
<td>BANK, GOVERNMENT</td>
</tr>
<tr>
<td>Lesotho</td>
<td>LS</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Libya</td>
<td>LY</td>
<td></td>
<td><strong>ar</strong>, en_US</td>
<td>WORKDAY</td>
</tr>
<tr>
<td>Liechtenstein</td>
<td>LI</td>
<td></td>
<td><strong>de</strong>, en_US, uk</td>
<td>BANK</td>
</tr>
<tr>
<td>Lithuania</td>
<td>LT</td>
<td></td>
<td>en_US, <strong>lt</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Luxembourg</td>
<td>LU</td>
<td></td>
<td>de, en_US, fr, <strong>lb</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Macau</td>
<td>MO</td>
<td>Historical municipalities: I (Concelho das Ilhas, 海島市, 海岛市), M (Concelho de Macau, 澳門市, 澳门市)</td>
<td>en_MO, en_US, pt_MO, th, zh_CN, <strong>zh_MO</strong></td>
<td>GOVERNMENT, MANDATORY</td>
</tr>
<tr>
<td>Madagascar</td>
<td>MG</td>
<td></td>
<td>en_US, <strong>mg</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Malawi</td>
<td>MW</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Malaysia</td>
<td>MY</td>
<td>States and federal territories: 01 (Johor, JHR), 02 (Kedah, KDH), 03 (Kelantan, KTN), 04 (Melaka, MLK), 05 (Negeri Sembilan, NSN), 06 (Pahang, PHG), 07 (Pulau Pinang, PNG), 08 (Perak, PRK), 09 (Perlis, PLS), 10 (Selangor, SGR), 11 (Terengganu, TRG), 12 (Sabah, SBH), 13 (Sarawak, SWK), 14 (Wilayah Persekutuan Kuala Lumpur, KUL), 15 (Wilayah Persekutuan Labuan, LBN), 16 (Wilayah Persekutuan Putrajaya, PJY)</td>
<td>en_US, <strong>ms_MY</strong>, th</td>
<td></td>
</tr>
<tr>
<td>Maldives</td>
<td>MV</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Mali</td>
<td>ML</td>
<td></td>
<td>en_US, <strong>fr</strong></td>
<td></td>
</tr>
<tr>
<td>Malta</td>
<td>MT</td>
<td></td>
<td>en_US, <strong>mt</strong></td>
<td></td>
</tr>
<tr>
<td>Marshall Islands (the)</td>
<td>MH</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Martinique</td>
<td>MQ</td>
<td>Can also be loaded as country FR, subdivision 972</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>Mauritania</td>
<td>MR</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Mauritius</td>
<td>MU</td>
<td></td>
<td><strong>en_MU</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Mayotte</td>
<td>YT</td>
<td>Can also be loaded as country FR, subdivision 976</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>Mexico</td>
<td>MX</td>
<td></td>
<td>en_US, <strong>es</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Micronesia</td>
<td>FM</td>
<td>States: KSA (Kosrae, Kusaie), PNI (Pohnpei, Ponape), TRK (Chuuk, Truk), YAP (Yap)</td>
<td><strong>en_FM</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Moldova</td>
<td>MD</td>
<td></td>
<td>en_US, <strong>ro</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Monaco</td>
<td>MC</td>
<td></td>
<td>en_US, <strong>fr</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Mongolia</td>
<td>MN</td>
<td></td>
<td>en_US, <strong>mn</strong></td>
<td>WORKDAY</td>
</tr>
<tr>
<td>Montenegro</td>
<td>ME</td>
<td></td>
<td><strong>cnr</strong>, en_US, uk</td>
<td>CATHOLIC, HEBREW, ISLAMIC, ORTHODOX, WORKDAY</td>
</tr>
<tr>
<td>Montserrat</td>
<td>MS</td>
<td></td>
<td><strong>en_MS</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Morocco</td>
<td>MA</td>
<td></td>
<td><strong>ar</strong>, en_US, fr</td>
<td></td>
</tr>
<tr>
<td>Mozambique</td>
<td>MZ</td>
<td></td>
<td>en_US, <strong>pt_MZ</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Namibia</td>
<td>NA</td>
<td></td>
<td><strong>en_NA</strong>, en_US, uk</td>
<td></td>
</tr>
<tr>
<td>Nauru</td>
<td>NR</td>
<td></td>
<td><strong>en_NR</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Nepal</td>
<td>NP</td>
<td></td>
<td></td>
<td>WORKDAY</td>
</tr>
<tr>
<td>Netherlands</td>
<td>NL</td>
<td></td>
<td>en_US, fy, <strong>nl</strong>, uk</td>
<td>OPTIONAL</td>
</tr>
<tr>
<td>New Caledonia</td>
<td>NC</td>
<td>Can also be loaded as country FR, subdivision NC</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>New Zealand</td>
<td>NZ</td>
<td>Regions and Special Island Authorities: AUK (Auckland, Tāmaki-Makaurau, AU), BOP (Bay of Plenty, Toi Moana, BP), CAN (Canterbury, Waitaha, CA), CIT (Chatham Islands Territory, Chatham Islands, Wharekauri, CI), GIS (Gisborne, Te Tairāwhiti, GI), HKB (Hawke's Bay, Te Matau-a-Māui, HB), MBH (Marlborough, MA), MWT (Manawatū Whanganui, Manawatū-Whanganui, MW), NSN (Nelson, Whakatū, NE), NTL (Northland, Te Taitokerau, NO), OTA (Otago, Ō Tākou, OT), STL (Southland, Te Taiao Tonga, SO), TAS (Tasman, Te tai o Aorere, TS), TKI (Taranaki, TK), WGN (Greater Wellington, Te Pane Matua Taiao, Wellington, Te Whanganui-a-Tara, WG), WKO (Waikato, WK), WTC (West Coast, Te Tai o Poutini, WC); subregions: South Canterbury</td>
<td></td>
<td></td>
</tr>
<tr>
<td>Nicaragua</td>
<td>NI</td>
<td>Subdivisions: AN (Costa Caribe Norte), AS (Costa Caribe Sur), BO (Boaco), CA (Carazo), CI (Chinandega), CO (Chontales), ES (Estelí), GR (Granada), JI (Jinotega), LE (León), MD (Madriz), MN (Managua), MS (Masaya), MT (Matagalpa), NS (Nueva Segovia), RI (Rivas), SJ (Río San Juan)</td>
<td>en_US, <strong>es</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Niger</td>
<td>NE</td>
<td></td>
<td>en_US, <strong>fr_NE</strong></td>
<td>OPTIONAL</td>
</tr>
<tr>
<td>Nigeria</td>
<td>NG</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Niue</td>
<td>NU</td>
<td></td>
<td><strong>en_NU</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Norfolk Island</td>
<td>NF</td>
<td></td>
<td><strong>en_NF</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Northern Mariana Islands (the)</td>
<td>MP</td>
<td>Can also be loaded as country US, subdivision MP</td>
<td><strong>en_US</strong>, th</td>
<td>GOVERNMENT, UNOFFICIAL</td>
</tr>
<tr>
<td>North Macedonia</td>
<td>MK</td>
<td></td>
<td>en_US, <strong>mk</strong>, uk</td>
<td>ALBANIAN, BOSNIAN, CATHOLIC, HEBREW, ISLAMIC, ORTHODOX, ROMA, SERBIAN, TURKISH, VLACH</td>
</tr>
<tr>
<td>Norway</td>
<td>NO</td>
<td>Counties and Arctic Regions: 03 (Oslo), 11 (Rogaland), 15 (Møre og Romsdal), 18 (Nordland), 21 (Svalbard), 22 (Jan Mayen), 30 (Viken), 34 (Innlandet), 38 (Vestfold og Telemark), 42 (Agder), 46 (Vestland), 50 (Trööndelage, Trøndelag), 54 (Romssa ja Finnmárkku, Troms og Finnmark, Tromssan ja Finmarkun)</td>
<td>en_US, <strong>no</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>Oman</td>
<td>OM</td>
<td></td>
<td><strong>ar</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Pakistan</td>
<td>PK</td>
<td></td>
<td><strong>en_PK</strong>, en_US, ur_PK</td>
<td></td>
</tr>
<tr>
<td>Palau</td>
<td>PW</td>
<td></td>
<td></td>
<td>ARMED_FORCES, HALF_DAY</td>
</tr>
<tr>
<td>Palestine</td>
<td>PS</td>
<td></td>
<td><strong>ar</strong>, en_US</td>
<td>CATHOLIC, ORTHODOX</td>
</tr>
<tr>
<td>Panama</td>
<td>PA</td>
<td></td>
<td>en_US, <strong>es</strong>, uk</td>
<td>BANK</td>
</tr>
<tr>
<td>Papua New Guinea</td>
<td>PG</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Paraguay</td>
<td>PY</td>
<td></td>
<td>en_US, <strong>es</strong>, uk</td>
<td>GOVERNMENT</td>
</tr>
<tr>
<td>Peru</td>
<td>PE</td>
<td></td>
<td>en_US, <strong>es</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Philippines</td>
<td>PH</td>
<td></td>
<td><strong>en_PH</strong>, en_US, fil, th</td>
<td>WORKDAY</td>
</tr>
<tr>
<td>Poland</td>
<td>PL</td>
<td></td>
<td>de, en_US, <strong>pl</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Portugal</td>
<td>PT</td>
<td>Districts: 01 (Aveiro), 02 (Beja), 03 (Braga), 04 (Bragança), 05 (Castelo Branco), 06 (Coimbra), 07 (Évora), 08 (Faro), 09 (Guarda), 10 (Leiria), 11 (Lisboa), 12 (Portalegre), 13 (Porto), 14 (Santarém), 15 (Setúbal), 16 (Viana do Castelo), 17 (Vila Real), 18 (Viseu), 20 (Região Autónoma dos Açores), 30 (Região Autónoma da Madeira)</td>
<td>en_US, <strong>pt_PT</strong>, uk</td>
<td>OPTIONAL</td>
</tr>
<tr>
<td>Puerto Rico</td>
<td>PR</td>
<td>Can also be loaded as country US, subdivision PR</td>
<td><strong>en_US</strong>, th</td>
<td>GOVERNMENT, UNOFFICIAL</td>
</tr>
<tr>
<td>Qatar</td>
<td>QA</td>
<td></td>
<td><strong>ar_QA</strong>, en_US</td>
<td>BANK</td>
</tr>
<tr>
<td>Reunion</td>
<td>RE</td>
<td>Can also be loaded as country FR, subdivision 974</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>Romania</td>
<td>RO</td>
<td></td>
<td>en_US, <strong>ro</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Russia</td>
<td>RU</td>
<td></td>
<td>en_US, <strong>ru</strong>, th</td>
<td></td>
</tr>
<tr>
<td>Saint Barthélemy</td>
<td>BL</td>
<td>Can also be loaded as country FR, subdivision BL</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>Saint Kitts and Nevis</td>
<td>KN</td>
<td></td>
<td></td>
<td>HALF_DAY, WORKDAY</td>
</tr>
<tr>
<td>Saint Lucia</td>
<td>LC</td>
<td></td>
<td><strong>en_LC</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Saint Martin</td>
<td>MF</td>
<td>Can also be loaded as country FR, subdivision MF</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>Saint Pierre and Miquelon</td>
<td>PM</td>
<td>Can also be loaded as country FR, subdivision PM</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>Saint Vincent and the Grenadines</td>
<td>VC</td>
<td></td>
<td>en_US, <strong>en_VC</strong></td>
<td></td>
</tr>
<tr>
<td>Samoa</td>
<td>WS</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>San Marino</td>
<td>SM</td>
<td></td>
<td>en_US, <strong>it</strong>, uk</td>
<td>BANK</td>
</tr>
<tr>
<td>Sao Tome and Principe</td>
<td>ST</td>
<td>Districts and Autonomous Region: 01 (Água Grande), 02 (Cantagalo), 03 (Caué), 04 (Lembá), 05 (Lobata), 06 (Mé-Zóchi), P (Príncipe)</td>
<td>en_US, <strong>pt_ST</strong></td>
<td></td>
</tr>
<tr>
<td>Saudi Arabia</td>
<td>SA</td>
<td></td>
<td><strong>ar</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Senegal</td>
<td>SN</td>
<td></td>
<td>en_US, <strong>fr_SN</strong></td>
<td></td>
</tr>
<tr>
<td>Serbia</td>
<td>RS</td>
<td></td>
<td>en_US, <strong>sr</strong></td>
<td></td>
</tr>
<tr>
<td>Seychelles</td>
<td>SC</td>
<td></td>
<td><strong>en_SC</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Sierra Leone</td>
<td>SL</td>
<td></td>
<td><strong>en_SL</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Singapore</td>
<td>SG</td>
<td></td>
<td><strong>en_SG</strong>, en_US, th</td>
<td></td>
</tr>
<tr>
<td>Sint Maarten</td>
<td>SX</td>
<td></td>
<td>en_US, <strong>nl</strong></td>
<td></td>
</tr>
<tr>
<td>Slovakia</td>
<td>SK</td>
<td></td>
<td>en_US, <strong>sk</strong>, uk</td>
<td>WORKDAY</td>
</tr>
<tr>
<td>Slovenia</td>
<td>SI</td>
<td></td>
<td>en_US, <strong>sl</strong>, uk</td>
<td>WORKDAY</td>
</tr>
<tr>
<td>Solomon Islands</td>
<td>SB</td>
<td>Subdivisions: CE (Central), CH (Choiseul), CT (Capital Territory, Honiara), GU (Guadalcanal), IS (Isabel), MK (Makira-Ulawa), ML (Malaita), RB (Rennell and Bellona), TE (Temotu), WE (Western)</td>
<td></td>
<td></td>
</tr>
<tr>
<td>South Africa</td>
<td>ZA</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>South Korea</td>
<td>KR</td>
<td></td>
<td>en_US, <strong>ko</strong>, th</td>
<td>BANK</td>
</tr>
<tr>
<td>Spain</td>
<td>ES</td>
<td>Autonomous communities: AN (Andalucía), AR (Aragón), AS (Asturias), CB (Cantabria), CE (Ceuta), CL (Castilla y León), CM (Castilla-La Mancha), CN (Canarias), CT (Cataluña, Catalunya), EX (Extremadura), GA (Galicia), IB (Islas Baleares, Illes Balears), MC (Murcia), MD (Madrid), ML (Melilla), NC (Navarra), PV (País Vasco), RI (La Rioja), VC (Valenciana)</td>
<td>en_US, <strong>es</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Sri Lanka</td>
<td>LK</td>
<td></td>
<td>en_US, <strong>si_LK</strong>, ta_LK</td>
<td>BANK, GOVERNMENT, WORKDAY</td>
</tr>
<tr>
<td>Suriname</td>
<td>SR</td>
<td></td>
<td>en_US, <strong>nl</strong></td>
<td></td>
</tr>
<tr>
<td>Svalbard and Jan Mayen</td>
<td>SJ</td>
<td>Can also be loaded as country NO, subdivision 21 and 22</td>
<td>en_US, <strong>no</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>Sweden</td>
<td>SE</td>
<td></td>
<td>en_US, <strong>sv</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>Switzerland</td>
<td>CH</td>
<td>Cantons: AG (Aargau), AI (Appenzell Innerrhoden), AR (Appenzell Ausserrhoden), BE (Bern, Berne), BL (Basel-Landschaft), BS (Basel-Stadt), FR (Freiburg, Fribourg), GE (Genève), GL (Glarus), GR (Graubünden, Grigioni, Grischun), JU (Jura), LU (Luzern), NE (Neuchâtel), NW (Nidwalden), OW (Obwalden), SG (Sankt Gallen), SH (Schaffhausen), SO (Solothurn), SZ (Schwyz), TG (Thurgau), TI (Ticino), UR (Uri), VD (Vaud), VS (Valais, Wallis), ZG (Zug), ZH (Zürich)</td>
<td><strong>de</strong>, en_US, fr, it, uk</td>
<td>HALF_DAY, OPTIONAL</td>
</tr>
<tr>
<td>Taiwan</td>
<td>TW</td>
<td></td>
<td>en_US, th, zh_CN, <strong>zh_TW</strong></td>
<td>GOVERNMENT, OPTIONAL, SCHOOL, WORKDAY</td>
</tr>
<tr>
<td>Tanzania</td>
<td>TZ</td>
<td></td>
<td>en_US, <strong>sw</strong></td>
<td>BANK</td>
</tr>
<tr>
<td>Thailand</td>
<td>TH</td>
<td></td>
<td>en_US, <strong>th</strong>, uk</td>
<td>ARMED_FORCES, BANK, GOVERNMENT, SCHOOL, WORKDAY</td>
</tr>
<tr>
<td>Timor Leste</td>
<td>TL</td>
<td></td>
<td>en_TL, en_US, <strong>pt_TL</strong>, tet, th</td>
<td>GOVERNMENT, WORKDAY</td>
</tr>
<tr>
<td>Togo</td>
<td>TG</td>
<td></td>
<td>en_US, <strong>fr</strong></td>
<td>WORKDAY</td>
</tr>
<tr>
<td>Tokelau</td>
<td>TK</td>
<td></td>
<td><strong>en_TK</strong>, en_US, tkl</td>
<td></td>
</tr>
<tr>
<td>Tonga</td>
<td>TO</td>
<td></td>
<td>en_US, <strong>to</strong></td>
<td></td>
</tr>
<tr>
<td>Trinidad and Tobago</td>
<td>TT</td>
<td></td>
<td><strong>en_TT</strong>, en_US</td>
<td>OPTIONAL</td>
</tr>
<tr>
<td>Tunisia</td>
<td>TN</td>
<td></td>
<td><strong>ar</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Turkey</td>
<td>TR</td>
<td></td>
<td>en_US, <strong>tr</strong>, uk</td>
<td>HALF_DAY</td>
</tr>
<tr>
<td>Turks and Caicos Islands</td>
<td>TC</td>
<td></td>
<td><strong>en_TC</strong>, en_US</td>
<td></td>
</tr>
<tr>
<td>Tuvalu</td>
<td>TV</td>
<td>Town/Island Councils: FUN (Funafuti), NIT (Niutao), NKF (Nukufetau), NKL (Nukulaelae), NMA (Nanumea), NMG (Nanumaga, Nanumanga), NUI (Nui), VAI (Vaitupu)</td>
<td>en_GB, en_US, <strong>tvl</strong></td>
<td></td>
</tr>
<tr>
<td>Ukraine</td>
<td>UA</td>
<td></td>
<td>ar, en_US, th, <strong>uk</strong></td>
<td>WORKDAY</td>
</tr>
<tr>
<td>United Arab Emirates</td>
<td>AE</td>
<td></td>
<td><strong>ar</strong>, en_US, th</td>
<td>GOVERNMENT, OPTIONAL</td>
</tr>
<tr>
<td>United Kingdom</td>
<td>GB</td>
<td>Subdivisions: ENG (England), NIR (Northern Ireland), SCT (Scotland), WLS (Wales)</td>
<td><strong>en_GB</strong>, en_US, th</td>
<td></td>
</tr>
<tr>
<td>United States Minor Outlying Islands</td>
<td>UM</td>
<td>Can also be loaded as country US, subdivision UM</td>
<td><strong>en_US</strong>, th</td>
<td>GOVERNMENT, UNOFFICIAL</td>
</tr>
<tr>
<td>United States of America (the)</td>
<td>US</td>
<td>States and territories: AK (Alaska), AL (Alabama), AR (Arkansas), AS (American Samoa), AZ (Arizona), CA (California), CO (Colorado), CT (Connecticut), DC (District of Columbia), DE (Delaware), FL (Florida), GA (Georgia), GU (Guam), HI (Hawaii), IA (Iowa), ID (Idaho), IL (Illinois), IN (Indiana), KS (Kansas), KY (Kentucky), LA (Louisiana), MA (Massachusetts), MD (Maryland), ME (Maine), MI (Michigan), MN (Minnesota), MO (Missouri), MP (Northern Mariana Islands), MS (Mississippi), MT (Montana), NC (North Carolina), ND (North Dakota), NE (Nebraska), NH (New Hampshire), NJ (New Jersey), NM (New Mexico), NV (Nevada), NY (New York), OH (Ohio), OK (Oklahoma), OR (Oregon), PA (Pennsylvania), PR (Puerto Rico), RI (Rhode Island), SC (South Carolina), SD (South Dakota), TN (Tennessee), TX (Texas), UM (United States Minor Outlying Islands), UT (Utah), VA (Virginia), VI (Virgin Islands, U.S.), VT (Vermont), WA (Washington), WI (Wisconsin), WV (West Virginia), WY (Wyoming)</td>
<td><strong>en_US</strong>, th</td>
<td>GOVERNMENT, UNOFFICIAL</td>
</tr>
<tr>
<td>United States Virgin Islands (the)</td>
<td></td>
<td>See Virgin Islands (U.S.)</td>
<td></td>
<td>GOVERNMENT, UNOFFICIAL</td>
</tr>
<tr>
<td>Uruguay</td>
<td>UY</td>
<td></td>
<td>en_US, <strong>es</strong>, uk</td>
<td>BANK</td>
</tr>
<tr>
<td>Uzbekistan</td>
<td>UZ</td>
<td></td>
<td>en_US, uk, <strong>uz</strong></td>
<td></td>
</tr>
<tr>
<td>Vanuatu</td>
<td>VU</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Vatican City</td>
<td>VA</td>
<td></td>
<td>en_US, <strong>it</strong>, th</td>
<td></td>
</tr>
<tr>
<td>Venezuela</td>
<td>VE</td>
<td></td>
<td>en_US, <strong>es</strong>, uk</td>
<td></td>
</tr>
<tr>
<td>Vietnam</td>
<td>VN</td>
<td></td>
<td>en_US, th, <strong>vi</strong></td>
<td></td>
</tr>
<tr>
<td>Virgin Islands (U.S.)</td>
<td>VI</td>
<td>Can also be loaded as country US, subdivision VI</td>
<td><strong>en_US</strong>, th</td>
<td>GOVERNMENT, UNOFFICIAL</td>
</tr>
<tr>
<td>Wallis and Futuna</td>
<td>WF</td>
<td>Can also be loaded as country FR, subdivision WF</td>
<td>en_US, <strong>fr</strong>, th, uk</td>
<td></td>
</tr>
<tr>
<td>Yemen</td>
<td>YE</td>
<td></td>
<td><strong>ar</strong>, en_US</td>
<td>SCHOOL, WORKDAY</td>
</tr>
<tr>
<td>Zambia</td>
<td>ZM</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td>Zimbabwe</td>
<td>ZW</td>
<td></td>
<td></td>
<td></td>
</tr>
</tbody>
</table>

## Available Financial Markets

The standard way to refer to a financial market is to use its [ISO 10383
MIC](https://www.iso20022.org/market-identifier-codes) (Market Identifier Code) as a "market"
code when available. The following financial markets are available:

<table style="width: 100%">
<colgroup>
<col style="width: 20.0%" />
<col style="width: 4.0%" />
<col style="width: 65.0%" />
<col style="width: 15.0%" />
</colgroup>
<thead>
<tr>
<th>Entity</th>
<th>Code</th>
<th>Info</th>
<th>Supported Languages</th>
</tr>
</thead>
<tbody>
<tr>
<td>Brasil, Bolsa, Balcão</td>
<td>BVMF</td>
<td>Brazil Stock Exchange and Over-the-Counter Market holidays (same as ANBIMA holidays)</td>
<td>en_US, <strong>pt_BR</strong>, uk</td>
</tr>
<tr>
<td>European Central Bank</td>
<td>XECB</td>
<td>Trans-European Automated Real-time Gross Settlement (TARGET2)</td>
<td></td>
</tr>
<tr>
<td>ICE Futures Europe</td>
<td>IFEU</td>
<td>A London-based Investment Exchange holidays</td>
<td></td>
</tr>
<tr>
<td>New York Stock Exchange</td>
<td>XNYS</td>
<td>NYSE market holidays (used by all other US-exchanges, including NASDAQ, etc.)</td>
<td></td>
</tr>
</tbody>
</table>

## Contributions

[Issues](https://github.com/vacanza/holidays/issues) and [pull
requests](https://github.com/vacanza/holidays/pulls) are always welcome. Please see
[here](https://github.com/vacanza/holidays/blob/dev/CONTRIBUTING.md) for more information.

## License

Code and documentation are available according to the MIT License (see
[LICENSE](https://github.com/vacanza/holidays/blob/dev/LICENSE)).
