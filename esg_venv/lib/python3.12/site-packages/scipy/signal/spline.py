# This file is not meant for public use and will be removed in the future
# versions of SciPy. Use the `scipy.signal` namespace for importing the
# functions included below.

from scipy._lib.deprecation import _sub_module_deprecation


__all__ = ['sepfir2d']  # noqa: F822


def __dir__():
    return __all__


def __getattr__(name):
    return _sub_module_deprecation(sub_package="signal", module="spline",
                                   private_modules=["_spline"], all=__all__,
                                   attribute=name)
