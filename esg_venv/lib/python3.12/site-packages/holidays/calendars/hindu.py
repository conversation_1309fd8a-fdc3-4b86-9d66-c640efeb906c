#  holidays
#  --------
#  A fast, efficient Python library for generating country, province and state
#  specific sets of holidays on the fly. It aims to make determining whether a
#  specific date is a holiday as fast and flexible as possible.
#
#  Authors: <AUTHORS>
# <AUTHOR> <EMAIL> (c) 2017-2023
# <AUTHOR> <EMAIL> (c) 2014-2017
#  Website: https://github.com/vacanza/holidays
#  License: MIT (see LICENSE file)

from collections.abc import Iterable
from datetime import date
from typing import Optional

from holidays.calendars.custom import _CustomCalendar
from holidays.calendars.gregorian import JAN, FEB, MAR, APR, MAY, AUG, SEP, OCT, NOV, DEC
from holidays.helpers import _normalize_tuple

BUDDHA_PURNIMA = "BUDDHA_PURNIMA"
CHHATH_PUJA = "CHHATH_PUJA"
DIWALI = "DIWALI"
DIWALI_INDIA = "DIWALI_INDIA"
DUSSEHRA = "DUSSEHRA"
GANESH_CHATURTHI = "GANESH_CHATURTHI"
GOVARDHAN_PUJA = "GOVARDHAN_PUJA"
GUDI_PADWA = "GUDI_PADWA"
GURU_GOBIND_SINGH_JAYANTI = "GURU_GOBIND_SINGH_JAYANTI"
GURU_NANAK_JAYANTI = "GURU_NANAK_JAYANTI"
GYALPO_LOSAR = "GYALPO_LOSAR"
HOLI = "HOLI"
JANMASHTAMI = "JANMASHTAMI"
MAHA_ASHTAMI = "MAHA_ASHTAMI"
MAHA_NAVAMI = "MAHA_NAVAMI"
MAHA_SHIVARATRI = "MAHA_SHIVARATRI"
MAHAVIR_JAYANTI = "MAHAVIR_JAYANTI"
MAKAR_SANKRANTI = "MAKAR_SANKRANTI"
ONAM = "ONAM"
PONGAL = "PONGAL"
RAKSHA_BANDHAN = "RAKSHA_BANDHAN"
RAM_NAVAMI = "RAM_NAVAMI"
SHARAD_NAVRATRI = "SHARAD_NAVRATRI"
SONAM_LOSAR = "SONAM_LOSAR"
TAMU_LOSAR = "TAMU_LOSAR"
THAIPUSAM = "THAIPUSAM"
VAISAKHI = "VAISAKHI"


class _HinduLunisolar:
    # https://web.archive.org/web/20240804044401/https://www.timeanddate.com/holidays/india/buddha-purnima
    BUDDHA_PURNIMA_DATES = {
        2001: (APR, 30),
        2002: (MAY, 19),
        2003: (MAY, 8),
        2004: (MAY, 26),
        2005: (MAY, 23),
        2006: (MAY, 13),
        2007: (MAY, 2),
        2008: (MAY, 20),
        2009: (MAY, 8),
        2010: (MAY, 27),
        2011: (MAY, 17),
        2012: (MAY, 6),
        2013: (MAY, 25),
        2014: (MAY, 14),
        2015: (MAY, 4),
        2016: (MAY, 21),
        2017: (MAY, 10),
        2018: (APR, 30),
        2019: (MAY, 18),
        2020: (MAY, 7),
        2021: (MAY, 26),
        2022: (MAY, 16),
        2023: (MAY, 5),
        2024: (MAY, 23),
        2025: (MAY, 12),
        2026: (MAY, 1),
        2027: (MAY, 20),
        2028: (MAY, 8),
        2029: (MAY, 27),
        2030: (MAY, 17),
        2031: (MAY, 7),
        2032: (MAY, 25),
        2033: (MAY, 14),
        2034: (MAY, 3),
        2035: (MAY, 22),
    }

    # https://web.archive.org/web/20250404174934/https://www.timeanddate.com/holidays/india/chhat-puja
    CHHATH_PUJA_DATES = {
        2001: (NOV, 21),
        2002: (NOV, 10),
        2003: (OCT, 30),
        2004: (NOV, 17),
        2005: (NOV, 7),
        2006: (OCT, 28),
        2007: (NOV, 16),
        2008: (NOV, 4),
        2009: (OCT, 24),
        2010: (NOV, 11),
        2011: (NOV, 1),
        2012: (NOV, 19),
        2013: (NOV, 8),
        2014: (OCT, 29),
        2015: (NOV, 17),
        2016: (NOV, 6),
        2017: (OCT, 26),
        2018: (NOV, 13),
        2019: (NOV, 2),
        2020: (NOV, 20),
        2021: (NOV, 10),
        2022: (OCT, 30),
        2023: (NOV, 19),
        2024: (NOV, 7),
        2025: (OCT, 28),
        2026: (NOV, 15),
        2027: (NOV, 4),
        2028: (OCT, 23),
        2029: (NOV, 11),
        2030: (NOV, 1),
        2031: (NOV, 20),
        2032: (NOV, 9),
        2033: (OCT, 29),
        2034: (NOV, 17),
        2035: (NOV, 6),
    }

    DIWALI_DATES = {
        1901: (NOV, 9),
        1902: (OCT, 29),
        1903: (NOV, 17),
        1904: (NOV, 5),
        1905: (OCT, 26),
        1906: (NOV, 14),
        1907: (NOV, 4),
        1908: (OCT, 23),
        1909: (NOV, 11),
        1910: (OCT, 31),
        1911: (NOV, 19),
        1912: (NOV, 7),
        1913: (OCT, 27),
        1914: (NOV, 16),
        1915: (NOV, 5),
        1916: (OCT, 25),
        1917: (NOV, 13),
        1918: (NOV, 2),
        1919: (NOV, 20),
        1920: (NOV, 9),
        1921: (OCT, 29),
        1922: (NOV, 17),
        1923: (NOV, 6),
        1924: (OCT, 26),
        1925: (NOV, 14),
        1926: (NOV, 3),
        1927: (OCT, 23),
        1928: (NOV, 10),
        1929: (OCT, 30),
        1930: (NOV, 18),
        1931: (NOV, 8),
        1932: (OCT, 27),
        1933: (NOV, 16),
        1934: (NOV, 5),
        1935: (OCT, 25),
        1936: (NOV, 12),
        1937: (NOV, 1),
        1938: (NOV, 20),
        1939: (NOV, 9),
        1940: (OCT, 29),
        1941: (NOV, 17),
        1942: (NOV, 6),
        1943: (OCT, 27),
        1944: (NOV, 14),
        1945: (NOV, 3),
        1946: (OCT, 23),
        1947: (NOV, 11),
        1948: (OCT, 30),
        1949: (NOV, 18),
        1950: (NOV, 8),
        1951: (OCT, 28),
        1952: (NOV, 15),
        1953: (NOV, 5),
        1954: (OCT, 25),
        1955: (NOV, 12),
        1956: (NOV, 1),
        1957: (NOV, 20),
        1958: (NOV, 9),
        1959: (OCT, 30),
        1960: (NOV, 17),
        1961: (NOV, 6),
        1962: (OCT, 26),
        1963: (NOV, 14),
        1964: (NOV, 2),
        1965: (OCT, 22),
        1966: (NOV, 10),
        1967: (OCT, 31),
        1968: (NOV, 18),
        1969: (NOV, 8),
        1970: (OCT, 28),
        1971: (NOV, 16),
        1972: (NOV, 4),
        1973: (OCT, 24),
        1974: (NOV, 12),
        1975: (NOV, 1),
        1976: (NOV, 19),
        1977: (NOV, 9),
        1978: (OCT, 30),
        1979: (NOV, 18),
        1980: (NOV, 6),
        1981: (OCT, 26),
        1982: (NOV, 13),
        1983: (NOV, 3),
        1984: (OCT, 22),
        1985: (NOV, 10),
        1986: (OCT, 31),
        1987: (NOV, 19),
        1988: (NOV, 7),
        1989: (OCT, 27),
        1990: (NOV, 15),
        1991: (NOV, 4),
        1992: (OCT, 24),
        1993: (NOV, 12),
        1994: (NOV, 1),
        1995: (NOV, 20),
        1996: (NOV, 9),
        1997: (OCT, 29),
        1998: (NOV, 17),
        1999: (NOV, 6),
        2000: (OCT, 25),
        2001: (NOV, 13),
        2002: (NOV, 3),
        2003: (OCT, 23),
        2004: (NOV, 10),
        2005: (OCT, 31),
        2006: (NOV, 19),
        2007: (NOV, 8),
        2008: (OCT, 27),
        2009: (NOV, 15),
        2010: (NOV, 4),
        2011: (OCT, 25),
        2012: (NOV, 12),
        2013: (NOV, 1),
        2014: (NOV, 20),
        2015: (NOV, 10),
        2016: (OCT, 29),
        2017: (NOV, 16),
        2018: (NOV, 6),
        2019: (OCT, 26),
        2020: (NOV, 13),
        2021: (NOV, 3),
        2022: (OCT, 23),
        2023: (NOV, 11),
        2024: (OCT, 30),
        2025: (NOV, 18),
        2026: (NOV, 7),
        2027: (OCT, 27),
        2028: (NOV, 14),
        2029: (NOV, 4),
        2030: (OCT, 25),
        2031: (NOV, 13),
        2032: (NOV, 1),
        2033: (OCT, 21),
        2034: (NOV, 9),
        2035: (OCT, 29),
        2036: (NOV, 16),
        2037: (NOV, 5),
        2038: (OCT, 26),
        2039: (NOV, 14),
        2040: (NOV, 3),
        2041: (OCT, 23),
        2042: (NOV, 11),
        2043: (OCT, 31),
        2044: (NOV, 17),
        2045: (NOV, 7),
        2046: (OCT, 27),
        2047: (NOV, 15),
        2048: (NOV, 4),
        2049: (OCT, 25),
        2050: (NOV, 12),
        2051: (NOV, 1),
        2052: (NOV, 19),
        2053: (NOV, 8),
        2054: (OCT, 29),
        2055: (NOV, 17),
        2056: (NOV, 5),
        2057: (OCT, 26),
        2058: (NOV, 14),
        2059: (NOV, 3),
        2060: (OCT, 22),
        2061: (NOV, 10),
        2062: (OCT, 30),
        2063: (NOV, 18),
        2064: (NOV, 7),
        2065: (OCT, 27),
        2066: (NOV, 15),
        2067: (NOV, 5),
        2068: (OCT, 24),
        2069: (NOV, 12),
        2070: (NOV, 1),
        2071: (NOV, 20),
        2072: (NOV, 8),
        2073: (OCT, 29),
        2074: (NOV, 17),
        2075: (NOV, 6),
        2076: (OCT, 26),
        2077: (NOV, 14),
        2078: (NOV, 3),
        2079: (OCT, 23),
        2080: (NOV, 9),
        2081: (OCT, 30),
        2082: (NOV, 18),
        2083: (NOV, 8),
        2084: (OCT, 27),
        2085: (NOV, 15),
        2086: (NOV, 4),
        2087: (OCT, 24),
        2088: (NOV, 11),
        2089: (OCT, 31),
        2090: (NOV, 19),
        2091: (NOV, 9),
        2092: (OCT, 29),
        2093: (NOV, 17),
        2094: (NOV, 6),
        2095: (OCT, 26),
        2096: (NOV, 13),
        2097: (NOV, 2),
        2098: (OCT, 22),
        2099: (NOV, 10),
        2100: (OCT, 31),
    }

    # https://web.archive.org/web/20250118190944/https://www.timeanddate.com/holidays/india/diwali
    DIWALI_INDIA_DATES = {
        2001: (NOV, 14),
        2002: (NOV, 4),
        2003: (OCT, 25),
        2004: (NOV, 12),
        2005: (NOV, 1),
        2006: (OCT, 21),
        2007: (NOV, 9),
        2008: (OCT, 28),
        2009: (OCT, 17),
        2010: (NOV, 5),
        2011: (OCT, 26),
        2012: (NOV, 13),
        2013: (NOV, 3),
        2014: (OCT, 23),
        2015: (NOV, 11),
        2016: (OCT, 30),
        2017: (OCT, 19),
        2018: (NOV, 7),
        2019: (OCT, 27),
        2020: (NOV, 14),
        2021: (NOV, 4),
        2022: (OCT, 24),
        2023: (NOV, 12),
        2024: (OCT, 31),
        2025: (OCT, 20),
        2026: (NOV, 8),
        2027: (OCT, 29),
        2028: (OCT, 17),
        2029: (NOV, 5),
        2030: (OCT, 26),
        2031: (NOV, 14),
        2032: (NOV, 2),
        2033: (OCT, 22),
        2034: (NOV, 10),
        2035: (OCT, 30),
    }

    # https://web.archive.org/web/20250118183534/https://www.timeanddate.com/holidays/india/dussehra
    DUSSEHRA_DATES = {
        2001: (OCT, 26),
        2002: (OCT, 15),
        2003: (OCT, 5),
        2004: (OCT, 22),
        2005: (OCT, 12),
        2006: (OCT, 2),
        2007: (OCT, 21),
        2008: (OCT, 9),
        2009: (SEP, 28),
        2010: (OCT, 17),
        2011: (OCT, 6),
        2012: (OCT, 24),
        2013: (OCT, 13),
        2014: (OCT, 3),
        2015: (OCT, 22),
        2016: (OCT, 11),
        2017: (SEP, 30),
        2018: (OCT, 19),
        2019: (OCT, 8),
        2020: (OCT, 25),
        2021: (OCT, 15),
        2022: (OCT, 5),
        2023: (OCT, 24),
        2024: (OCT, 12),
        2025: (OCT, 2),
        2026: (OCT, 20),
        2027: (OCT, 9),
        2028: (SEP, 27),
        2029: (OCT, 16),
        2030: (OCT, 6),
        2031: (OCT, 25),
        2032: (OCT, 14),
        2033: (OCT, 3),
        2034: (OCT, 22),
        2035: (OCT, 11),
    }

    # https://web.archive.org/web/20250219062212/https://www.timeanddate.com/holidays/india/ganesh-chaturthi
    GANESH_CHATURTHI_DATES = {
        2001: (AUG, 22),
        2002: (SEP, 10),
        2003: (AUG, 31),
        2004: (SEP, 18),
        2005: (SEP, 7),
        2006: (AUG, 27),
        2007: (SEP, 15),
        2008: (SEP, 3),
        2009: (AUG, 23),
        2010: (SEP, 11),
        2011: (SEP, 1),
        2012: (SEP, 19),
        2013: (SEP, 9),
        2014: (AUG, 29),
        2015: (SEP, 17),
        2016: (SEP, 5),
        2017: (AUG, 25),
        2018: (SEP, 13),
        2019: (SEP, 2),
        2020: (AUG, 22),
        2021: (SEP, 10),
        2022: (AUG, 31),
        2023: (SEP, 19),
        2024: (SEP, 7),
        2025: (AUG, 27),
        2026: (SEP, 14),
        2027: (SEP, 4),
        2028: (AUG, 23),
        2029: (SEP, 11),
        2030: (SEP, 1),
        2031: (SEP, 20),
        2032: (SEP, 8),
        2033: (AUG, 28),
        2034: (SEP, 16),
        2035: (SEP, 5),
    }

    # https://web.archive.org/web/20240917162551/https://www.timeanddate.com/holidays/india/govardhan-puja
    GOVARDHAN_PUJA_DATES = {
        2001: (NOV, 15),
        2002: (NOV, 5),
        2003: (OCT, 26),
        2004: (NOV, 13),
        2005: (NOV, 2),
        2006: (OCT, 22),
        2007: (NOV, 10),
        2008: (OCT, 29),
        2009: (OCT, 18),
        2010: (NOV, 6),
        2011: (OCT, 27),
        2012: (NOV, 14),
        2013: (NOV, 4),
        2014: (OCT, 24),
        2015: (NOV, 12),
        2016: (OCT, 31),
        2017: (OCT, 20),
        2018: (NOV, 8),
        2019: (OCT, 28),
        2020: (NOV, 15),
        2021: (NOV, 5),
        2022: (OCT, 25),
        2023: (NOV, 13),
        2024: (NOV, 2),
        2025: (OCT, 22),
        2026: (NOV, 10),
        2027: (OCT, 30),
        2028: (OCT, 18),
        2029: (NOV, 6),
        2030: (OCT, 27),
        2031: (NOV, 15),
        2032: (NOV, 3),
        2033: (OCT, 23),
        2034: (NOV, 11),
        2035: (OCT, 31),
    }

    # https://web.archive.org/web/20250331230057/https://www.timeanddate.com/holidays/india/gudi-padwa
    GUDI_PADWA_DATES = {
        2001: (MAR, 26),
        2002: (APR, 13),
        2003: (APR, 2),
        2004: (MAR, 21),
        2005: (APR, 9),
        2006: (MAR, 30),
        2007: (MAR, 19),
        2008: (APR, 6),
        2009: (MAR, 27),
        2010: (MAR, 16),
        2011: (APR, 4),
        2012: (MAR, 23),
        2013: (APR, 11),
        2014: (MAR, 31),
        2015: (MAR, 21),
        2016: (APR, 8),
        2017: (MAR, 28),
        2018: (MAR, 18),
        2019: (APR, 6),
        2020: (MAR, 25),
        2021: (APR, 13),
        2022: (APR, 2),
        2023: (MAR, 22),
        2024: (APR, 9),
        2025: (MAR, 30),
        2026: (MAR, 19),
        2027: (APR, 7),
        2028: (MAR, 27),
        2029: (APR, 14),
        2030: (APR, 3),
        2031: (MAR, 24),
        2032: (APR, 11),
        2033: (MAR, 31),
        2034: (MAR, 21),
        2035: (APR, 9),
    }

    # https://web.archive.org/web/20241231181629/https://www.timeanddate.com/holidays/india/guru-govind-singh-jayanti
    GURU_GOBIND_SINGH_JAYANTI_DATES = {
        2001: (JAN, 2),
        2002: (JAN, 21),
        2003: (DEC, 29),
        2004: (NOV, 20),
        2005: (JAN, 5),
        2006: (JAN, 5),
        2007: (JAN, 5),
        2008: (JAN, 5),
        2009: (JAN, 5),
        2010: (JAN, 5),
        2011: (JAN, 5),
        2012: (JAN, 5),
        2013: (JAN, 18),
        2014: (JAN, 7),
        2015: (JAN, 5),
        2016: (JAN, 16),
        2017: ((JAN, 5), (DEC, 25)),
        2019: (JAN, 13),
        2020: (JAN, 2),
        2021: (JAN, 20),
        2022: ((JAN, 9), (DEC, 29)),
        2024: (JAN, 17),
        2025: ((JAN, 6), (DEC, 27)),
        2027: (JAN, 15),
        2028: (JAN, 4),
        2029: (JAN, 15),
        2030: ((JAN, 10), (DEC, 31)),
        2032: (JAN, 18),
        2033: (JAN, 7),
        2034: (JAN, 17),
        2035: (JAN, 16),
    }

    # https://web.archive.org/web/20240521074207/https://www.timeanddate.com/holidays/india/guru-nanak-jayanti
    GURU_NANAK_JAYANTI_DATES = {
        2001: (NOV, 30),
        2002: (NOV, 19),
        2003: (NOV, 8),
        2004: (NOV, 26),
        2005: (NOV, 15),
        2006: (NOV, 5),
        2007: (NOV, 24),
        2008: (NOV, 13),
        2009: (NOV, 2),
        2010: (NOV, 21),
        2011: (NOV, 10),
        2012: (NOV, 28),
        2013: (NOV, 17),
        2014: (NOV, 6),
        2015: (NOV, 25),
        2016: (NOV, 14),
        2017: (NOV, 4),
        2018: (NOV, 23),
        2019: (NOV, 12),
        2020: (NOV, 30),
        2021: (NOV, 19),
        2022: (NOV, 8),
        2023: (NOV, 27),
        2024: (NOV, 15),
        2025: (NOV, 5),
        2027: (NOV, 14),
        2028: (NOV, 2),
        2029: (NOV, 21),
        2030: (NOV, 10),
        2031: (NOV, 28),
        2032: (NOV, 17),
        2033: (NOV, 6),
        2034: (NOV, 25),
        2035: (NOV, 15),
    }

    # https://web.archive.org/web/20240814073838/https://www.timeanddate.com/holidays/nepal/gyalpo-losar
    # https://web.archive.org/web/20250322000610/https://www.ashesh.com.np/nepali-calendar/
    GYALPO_LOSAR_DATES = {
        2010: (FEB, 13),
        2011: (MAR, 5),
        2012: (FEB, 22),
        2013: (MAR, 12),
        2014: (MAR, 2),
        2015: (FEB, 19),
        2016: (MAR, 9),
        2017: (FEB, 27),
        2018: (FEB, 16),
        2019: (MAR, 7),
        2020: (FEB, 24),
        2021: (MAR, 14),
        2022: (MAR, 3),
        2023: (FEB, 21),
        2024: (MAR, 11),
        2025: (FEB, 28),
        2026: (FEB, 18),
        2027: (FEB, 7),
        2028: (FEB, 26),
        2029: (FEB, 14),
        2030: (MAR, 5),
        2031: (FEB, 22),
        2032: (FEB, 12),
        2033: (MAR, 2),
        2034: (FEB, 19),
        2035: (FEB, 9),
    }

    # https://web.archive.org/web/20250410002117/https://www.timeanddate.com/holidays/india/holi
    HOLI_DATES = {
        2001: (MAR, 10),
        2002: (MAR, 29),
        2003: (MAR, 18),
        2004: (MAR, 7),
        2005: (MAR, 26),
        2006: (MAR, 15),
        2007: (MAR, 4),
        2008: (MAR, 22),
        2009: (MAR, 11),
        2010: (MAR, 1),
        2011: (MAR, 20),
        2012: (MAR, 8),
        2013: (MAR, 27),
        2014: (MAR, 17),
        2015: (MAR, 6),
        2016: (MAR, 24),
        2017: (MAR, 13),
        2018: (MAR, 2),
        2019: (MAR, 21),
        2020: (MAR, 10),
        2021: (MAR, 29),
        2022: (MAR, 18),
        2023: (MAR, 8),
        2024: (MAR, 25),
        2025: (MAR, 14),
        2026: (MAR, 4),
        2027: (MAR, 22),
        2028: (MAR, 11),
        2029: (MAR, 1),
        2030: (MAR, 20),
        2031: (MAR, 9),
        2032: (MAR, 27),
        2033: (MAR, 16),
        2034: (MAR, 5),
        2035: (MAR, 24),
    }

    # https://web.archive.org/web/20241205010833/https://www.timeanddate.com/holidays/india/janmashtami
    JANMASHTAMI_DATES = {
        2001: (AUG, 12),
        2002: (AUG, 31),
        2003: (AUG, 20),
        2004: (SEP, 7),
        2005: (AUG, 27),
        2006: (AUG, 16),
        2007: (SEP, 4),
        2008: (AUG, 24),
        2009: (AUG, 14),
        2010: (SEP, 2),
        2011: (AUG, 22),
        2012: (AUG, 10),
        2013: (AUG, 28),
        2014: (AUG, 18),
        2015: (SEP, 5),
        2016: (AUG, 25),
        2017: (AUG, 15),
        2018: (SEP, 3),
        2019: (AUG, 24),
        2020: (AUG, 12),
        2021: (AUG, 30),
        2022: (AUG, 19),
        2023: (SEP, 7),
        2024: (AUG, 26),
        2025: (AUG, 16),
        2026: (SEP, 4),
        2027: (AUG, 25),
        2028: (AUG, 13),
        2029: (SEP, 1),
        2030: (AUG, 21),
        2031: (AUG, 10),
        2032: (AUG, 28),
        2033: (AUG, 17),
        2034: (SEP, 6),
        2035: (AUG, 26),
    }

    # https://web.archive.org/web/20250113213218/https://www.timeanddate.com/holidays/india/maha-ashtami
    MAHA_ASHTAMI_DATES = {
        2001: (OCT, 24),
        2002: (OCT, 13),
        2003: (OCT, 3),
        2004: (OCT, 21),
        2005: (OCT, 11),
        2006: (SEP, 30),
        2007: (OCT, 19),
        2008: (OCT, 7),
        2009: (SEP, 26),
        2010: (OCT, 15),
        2011: (OCT, 4),
        2012: (OCT, 22),
        2013: (OCT, 12),
        2014: (OCT, 2),
        2015: (OCT, 21),
        2016: (OCT, 9),
        2017: (SEP, 28),
        2018: (OCT, 17),
        2019: (OCT, 6),
        2020: (OCT, 23),
        2021: (OCT, 13),
        2022: (OCT, 3),
        2023: (OCT, 22),
        2024: (OCT, 11),
        2025: (SEP, 30),
        2026: (OCT, 19),
        2027: (OCT, 7),
        2028: (SEP, 26),
        2029: (OCT, 14),
        2030: (OCT, 4),
        2031: (OCT, 23),
        2032: (OCT, 12),
        2033: (OCT, 2),
        2034: (OCT, 20),
        2035: (OCT, 9),
    }

    # https://web.archive.org/web/20241125173623/https://www.timeanddate.com/holidays/india/maha-navami
    MAHA_NAVAMI_DATES = {
        2001: (OCT, 25),
        2002: (OCT, 14),
        2003: (OCT, 3),
        2004: (OCT, 21),
        2005: (OCT, 11),
        2006: (OCT, 1),
        2007: (OCT, 20),
        2008: (OCT, 8),
        2009: (SEP, 27),
        2010: (OCT, 16),
        2011: (OCT, 5),
        2012: (OCT, 23),
        2013: (OCT, 12),
        2014: (OCT, 2),
        2015: (OCT, 21),
        2016: (OCT, 10),
        2017: (SEP, 29),
        2018: (OCT, 17),
        2019: (OCT, 6),
        2020: (OCT, 24),
        2021: (OCT, 14),
        2022: (OCT, 4),
        2023: (OCT, 23),
        2024: (OCT, 11),
        2025: (OCT, 1),
        2026: (OCT, 19),
        2027: (OCT, 8),
        2028: (SEP, 26),
        2029: (OCT, 15),
        2030: (OCT, 5),
        2031: (OCT, 24),
        2032: (OCT, 13),
        2033: (OCT, 2),
        2034: (OCT, 21),
        2035: (OCT, 10),
    }

    # https://web.archive.org/web/20250323040914/https://www.timeanddate.com/holidays/india/maha-shivaratri-shivaratri
    MAHA_SHIVARATRI_DATES = {
        2001: (FEB, 21),
        2002: (MAR, 12),
        2003: (MAR, 1),
        2004: (FEB, 18),
        2005: (MAR, 8),
        2006: (FEB, 26),
        2007: (FEB, 16),
        2008: (MAR, 6),
        2009: (FEB, 23),
        2010: (FEB, 12),
        2011: (MAR, 2),
        2012: (FEB, 20),
        2013: (MAR, 10),
        2014: (FEB, 27),
        2015: (FEB, 17),
        2016: (MAR, 7),
        2017: (FEB, 24),
        2018: (FEB, 13),
        2019: (MAR, 4),
        2020: (FEB, 21),
        2021: (MAR, 11),
        2022: (MAR, 1),
        2023: (FEB, 18),
        2024: (MAR, 8),
        2025: (FEB, 26),
        2026: (FEB, 15),
        2027: (MAR, 6),
        2028: (FEB, 23),
        2029: (FEB, 11),
        2030: (MAR, 2),
        2031: (FEB, 20),
        2032: (MAR, 10),
        2033: (FEB, 27),
        2034: (FEB, 17),
        2035: (MAR, 8),
    }

    # https://web.archive.org/web/20250121194712/https://www.timeanddate.com/holidays/india/mahavir-jayanti
    MAHAVIR_JAYANTI_DATES = {
        2001: (APR, 6),
        2002: (APR, 25),
        2003: (APR, 15),
        2004: (APR, 3),
        2005: (APR, 22),
        2006: (APR, 11),
        2007: (MAR, 31),
        2008: (APR, 18),
        2009: (APR, 7),
        2010: (APR, 28),
        2011: (APR, 16),
        2012: (APR, 5),
        2013: (APR, 24),
        2014: (APR, 13),
        2015: (APR, 2),
        2016: (APR, 20),
        2017: (APR, 9),
        2018: (MAR, 29),
        2019: (APR, 17),
        2020: (APR, 6),
        2021: (APR, 25),
        2022: (APR, 14),
        2023: (APR, 4),
        2024: (APR, 21),
        2025: (APR, 10),
        2026: (MAR, 31),
        2027: (APR, 18),
        2028: (APR, 7),
        2029: (APR, 26),
        2030: (APR, 16),
        2031: (APR, 5),
        2032: (APR, 23),
        2033: (APR, 12),
        2034: (APR, 1),
        2035: (APR, 20),
    }

    # https://web.archive.org/web/20250119043432/https://www.timeanddate.com/holidays/india/makar-sankranti
    MAKAR_SANKRANTI_DATES = {
        2001: (JAN, 14),
        2002: (JAN, 14),
        2003: (JAN, 14),
        2004: (JAN, 15),
        2005: (JAN, 14),
        2006: (JAN, 14),
        2007: (JAN, 15),
        2008: (JAN, 15),
        2009: (JAN, 14),
        2010: (JAN, 14),
        2011: (JAN, 15),
        2012: (JAN, 15),
        2013: (JAN, 14),
        2014: (JAN, 14),
        2015: (JAN, 15),
        2016: (JAN, 15),
        2017: (JAN, 14),
        2018: (JAN, 14),
        2019: (JAN, 15),
        2020: (JAN, 15),
        2021: (JAN, 14),
        2022: (JAN, 14),
        2023: (JAN, 14),
        2024: (JAN, 14),
        2025: (JAN, 14),
        2026: (JAN, 14),
        2027: (JAN, 15),
        2028: (JAN, 15),
        2029: (JAN, 14),
        2030: (JAN, 14),
        2031: (JAN, 15),
        2032: (JAN, 15),
        2033: (JAN, 14),
        2034: (JAN, 14),
        2035: (JAN, 15),
    }

    # https://web.archive.org/web/20241205101551/https://www.timeanddate.com/holidays/india/onam
    ONAM_DATES = {
        2001: (AUG, 31),
        2002: (AUG, 21),
        2003: (SEP, 8),
        2004: (AUG, 28),
        2005: (SEP, 15),
        2006: (SEP, 5),
        2007: (AUG, 26),
        2008: (SEP, 12),
        2009: (SEP, 2),
        2010: (AUG, 23),
        2011: (SEP, 9),
        2012: (AUG, 29),
        2013: (AUG, 20),
        2014: (SEP, 6),
        2015: (AUG, 28),
        2016: (SEP, 13),
        2017: (SEP, 4),
        2018: (AUG, 24),
        2019: (SEP, 11),
        2020: (AUG, 31),
        2021: (AUG, 21),
        2022: (SEP, 8),
        2023: (AUG, 29),
        2024: (SEP, 15),
        2025: (SEP, 5),
        2026: (AUG, 26),
        2027: (SEP, 12),
        2028: (SEP, 1),
        2029: (AUG, 22),
        2030: (SEP, 9),
        2031: (AUG, 30),
        2032: (AUG, 20),
        2033: (SEP, 6),
        2034: (AUG, 28),
        2035: (SEP, 14),
    }

    PONGAL_DATES = {
        2001: (JAN, 14),
        2002: (JAN, 14),
        2003: (JAN, 15),
        2004: (JAN, 15),
        2005: (JAN, 14),
        2006: (JAN, 14),
        2007: (JAN, 15),
        2008: (JAN, 15),
        2009: (JAN, 14),
        2010: (JAN, 14),
        2011: (JAN, 15),
        2012: (JAN, 15),
        2013: (JAN, 14),
        2014: (JAN, 14),
        2015: (JAN, 15),
        2016: (JAN, 15),
        2017: (JAN, 14),
        2018: (JAN, 14),
        2019: (JAN, 15),
        2020: (JAN, 15),
        2021: (JAN, 14),
        2022: (JAN, 14),
        2023: (JAN, 15),
        2024: (JAN, 15),
        2025: (JAN, 14),
        2026: (JAN, 14),
        2027: (JAN, 15),
        2028: (JAN, 15),
        2029: (JAN, 14),
        2030: (JAN, 14),
        2031: (JAN, 15),
        2032: (JAN, 15),
        2033: (JAN, 14),
        2034: (JAN, 14),
        2035: (JAN, 15),
    }

    # https://web.archive.org/web/20240720191148/https://www.timeanddate.com/holidays/india/raksha-bandhan
    RAKSHA_BANDHAN_DATES = {
        2001: (AUG, 4),
        2002: (AUG, 22),
        2003: (AUG, 12),
        2004: (AUG, 29),
        2005: (AUG, 19),
        2006: (AUG, 9),
        2007: (AUG, 28),
        2008: (AUG, 16),
        2009: (AUG, 5),
        2010: (AUG, 24),
        2011: (AUG, 13),
        2012: (AUG, 2),
        2013: (AUG, 20),
        2014: (AUG, 10),
        2015: (AUG, 29),
        2016: (AUG, 18),
        2017: (AUG, 7),
        2018: (AUG, 26),
        2019: (AUG, 15),
        2020: (AUG, 3),
        2021: (AUG, 22),
        2022: (AUG, 11),
        2023: (AUG, 30),
        2024: (AUG, 19),
        2025: (AUG, 9),
        2026: (AUG, 28),
        2027: (AUG, 17),
        2028: (AUG, 5),
        2029: (AUG, 23),
        2030: (AUG, 13),
        2031: (AUG, 2),
        2032: (AUG, 20),
        2033: (AUG, 10),
        2034: (AUG, 29),
        2035: (AUG, 18),
    }

    # https://web.archive.org/web/20250403054153/https://www.timeanddate.com/holidays/india/rama-navami
    RAM_NAVAMI_DATES = {
        2001: (APR, 2),
        2002: (APR, 21),
        2003: (APR, 11),
        2004: (MAR, 30),
        2005: (APR, 18),
        2006: (APR, 6),
        2007: (MAR, 26),
        2008: (APR, 13),
        2009: (APR, 3),
        2010: (MAR, 24),
        2011: (APR, 12),
        2012: (APR, 1),
        2013: (APR, 19),
        2014: (APR, 8),
        2015: (MAR, 28),
        2016: (APR, 15),
        2017: (APR, 4),
        2018: (MAR, 25),
        2019: (APR, 13),
        2020: (APR, 2),
        2021: (APR, 21),
        2022: (APR, 10),
        2023: (MAR, 30),
        2024: (APR, 17),
        2025: (APR, 6),
        2026: (MAR, 26),
        2027: (APR, 15),
        2028: (APR, 3),
        2029: (APR, 22),
        2030: (APR, 12),
        2031: (APR, 1),
        2032: (APR, 19),
        2033: (APR, 7),
        2034: (MAR, 28),
        2035: (APR, 16),
    }

    # https://web.archive.org/web/20241202103625/https://www.timeanddate.com/holidays/india/navratri
    SHARAD_NAVRATRI_DATES = {
        2001: (OCT, 17),
        2002: (OCT, 7),
        2003: (SEP, 26),
        2004: (OCT, 14),
        2005: (OCT, 4),
        2006: (SEP, 23),
        2007: (OCT, 12),
        2008: (SEP, 30),
        2009: (SEP, 19),
        2010: (OCT, 8),
        2011: (SEP, 28),
        2012: (OCT, 16),
        2013: (OCT, 5),
        2014: (SEP, 25),
        2015: (OCT, 13),
        2016: (OCT, 1),
        2017: (SEP, 21),
        2018: (OCT, 10),
        2019: (SEP, 29),
        2020: (OCT, 17),
        2021: (OCT, 7),
        2022: (SEP, 26),
        2023: (OCT, 15),
        2024: (OCT, 3),
        2025: (SEP, 22),
        2026: (OCT, 11),
        2027: (SEP, 30),
        2028: (SEP, 19),
        2029: (OCT, 8),
        2030: (SEP, 28),
        2031: (OCT, 17),
        2032: (OCT, 5),
        2033: (SEP, 24),
        2034: (OCT, 13),
        2035: (OCT, 2),
    }

    # https://web.archive.org/web/20241007171215/https://www.timeanddate.com/holidays/nepal/sonam-losar
    # https://web.archive.org/web/20250322000610/https://www.ashesh.com.np/nepali-calendar/
    SONAM_LOSAR_DATES = {
        2010: (JAN, 16),
        2011: (FEB, 4),
        2012: (JAN, 24),
        2013: (FEB, 11),
        2014: (JAN, 31),
        2015: (JAN, 21),
        2016: (FEB, 9),
        2017: (JAN, 28),
        2018: (JAN, 18),
        2019: (FEB, 5),
        2020: (JAN, 25),
        2021: (FEB, 12),
        2022: (FEB, 2),
        2023: (JAN, 22),
        2024: (FEB, 10),
        2025: (JAN, 30),
        2026: (JAN, 19),
        2027: (FEB, 7),
        2028: (FEB, 26),
        2029: (JAN, 15),
        2030: (FEB, 3),
        2031: (JAN, 24),
        2032: (FEB, 12),
        2033: (JAN, 31),
        2034: (JAN, 21),
        2035: (FEB, 9),
    }

    # https://web.archive.org/web/20241207045124/https://www.hamropatro.com/posts/articles-Bishesh-Dinharu/articles-Bishesh-Dinharu-english-tamu-lhosar
    TAMU_LOSAR_DATES = {
        2010: (DEC, 30),
        2011: (DEC, 30),
        2012: (DEC, 30),
        2013: (DEC, 30),
        2014: (DEC, 30),
        2015: (DEC, 30),
        2016: (DEC, 30),
        2017: (DEC, 30),
        2018: (DEC, 30),
        2019: (DEC, 31),
        2020: (DEC, 30),
        2021: (DEC, 30),
        2022: (DEC, 30),
        2023: (DEC, 31),
        2024: (DEC, 30),
        2025: (DEC, 30),
        2026: (DEC, 30),
        2027: (DEC, 30),
        2028: (DEC, 30),
        2029: (DEC, 30),
        2030: (DEC, 31),
        2031: (DEC, 31),
        2032: (DEC, 30),
    }

    THAIPUSAM_DATES = {
        1901: (MAR, 5),
        1902: (FEB, 23),
        1903: (JAN, 14),
        1904: (MAR, 2),
        1905: (FEB, 19),
        1906: (JAN, 10),
        1907: (FEB, 27),
        1908: (FEB, 17),
        1909: (JAN, 7),
        1910: (FEB, 24),
        1911: (JAN, 15),
        1912: (MAR, 4),
        1913: (FEB, 21),
        1914: (JAN, 11),
        1915: (MAR, 1),
        1916: (FEB, 18),
        1917: (JAN, 8),
        1918: (FEB, 26),
        1919: (FEB, 15),
        1920: (MAR, 5),
        1921: (FEB, 23),
        1922: (JAN, 13),
        1923: (MAR, 2),
        1924: (FEB, 19),
        1925: (JAN, 9),
        1926: (FEB, 27),
        1927: (FEB, 17),
        1928: (JAN, 8),
        1929: (FEB, 24),
        1930: (JAN, 15),
        1931: (MAR, 4),
        1932: (FEB, 21),
        1933: (JAN, 11),
        1934: (FEB, 28),
        1935: (FEB, 18),
        1936: (JAN, 9),
        1937: (FEB, 26),
        1938: (FEB, 15),
        1939: (MAR, 6),
        1940: (FEB, 23),
        1941: (JAN, 12),
        1942: (MAR, 2),
        1943: (FEB, 19),
        1944: (JAN, 10),
        1945: (FEB, 27),
        1946: (FEB, 17),
        1947: (JAN, 7),
        1948: (FEB, 25),
        1949: (FEB, 13),
        1950: (MAR, 3),
        1951: (FEB, 21),
        1952: (JAN, 12),
        1953: (FEB, 28),
        1954: (FEB, 18),
        1955: (JAN, 9),
        1956: (FEB, 26),
        1957: (FEB, 15),
        1958: (MAR, 5),
        1959: (FEB, 22),
        1960: (JAN, 13),
        1961: (MAR, 2),
        1962: (FEB, 19),
        1963: (JAN, 10),
        1964: (FEB, 28),
        1965: (FEB, 16),
        1966: (JAN, 6),
        1967: (FEB, 24),
        1968: (FEB, 13),
        1969: (MAR, 3),
        1970: (FEB, 21),
        1971: (JAN, 12),
        1972: (FEB, 29),
        1973: (FEB, 18),
        1974: (JAN, 8),
        1975: (FEB, 26),
        1976: (FEB, 15),
        1977: (MAR, 5),
        1978: (FEB, 22),
        1979: (JAN, 13),
        1980: (MAR, 2),
        1981: (FEB, 19),
        1982: (JAN, 10),
        1983: (FEB, 28),
        1984: (FEB, 17),
        1985: (MAR, 6),
        1986: (FEB, 23),
        1987: (JAN, 14),
        1988: (MAR, 3),
        1989: (FEB, 21),
        1990: (JAN, 12),
        1991: (MAR, 1),
        1992: (FEB, 18),
        1993: (JAN, 8),
        1994: (FEB, 25),
        1995: (FEB, 14),
        1996: (MAR, 4),
        1997: (FEB, 22),
        1998: (JAN, 13),
        1999: (MAR, 3),
        2000: (FEB, 20),
        2001: (JAN, 9),
        2002: (FEB, 27),
        2003: (FEB, 16),
        2004: (JAN, 7),
        2005: (FEB, 23),
        2006: (FEB, 13),
        2007: (MAR, 4),
        2008: (FEB, 22),
        2009: (JAN, 11),
        2010: (MAR, 1),
        2011: (FEB, 18),
        2012: (JAN, 8),
        2013: (FEB, 25),
        2014: (FEB, 14),
        2015: (MAR, 5),
        2016: (FEB, 23),
        2017: (JAN, 13),
        2018: (MAR, 2),
        2019: (FEB, 20),
        2020: (JAN, 10),
        2021: (FEB, 26),
        2022: (FEB, 16),
        2023: (JAN, 7),
        2024: (FEB, 24),
        2025: (JAN, 14),
        2026: (MAR, 4),
        2027: (FEB, 21),
        2028: (JAN, 11),
        2029: (FEB, 28),
        2030: (FEB, 17),
        2031: (JAN, 8),
        2032: (FEB, 26),
        2033: (FEB, 14),
        2034: (MAR, 5),
        2035: (FEB, 23),
        2036: (JAN, 13),
        2037: (MAR, 2),
        2038: (FEB, 19),
        2039: (JAN, 9),
        2040: (FEB, 27),
        2041: (FEB, 15),
        2042: (JAN, 7),
        2043: (FEB, 24),
        2044: (FEB, 14),
        2045: (MAR, 4),
        2046: (FEB, 21),
        2047: (JAN, 11),
        2048: (FEB, 28),
        2049: (FEB, 17),
        2050: (JAN, 8),
        2051: (FEB, 26),
        2052: (FEB, 15),
        2053: (MAR, 5),
        2054: (FEB, 22),
        2055: (JAN, 13),
        2056: (MAR, 1),
        2057: (FEB, 18),
        2058: (JAN, 9),
        2059: (FEB, 27),
        2060: (FEB, 17),
        2061: (JAN, 6),
        2062: (FEB, 24),
        2063: (FEB, 13),
        2064: (MAR, 3),
        2065: (FEB, 20),
        2066: (JAN, 11),
        2067: (FEB, 28),
        2068: (FEB, 18),
        2069: (JAN, 8),
        2070: (FEB, 25),
        2071: (FEB, 15),
        2072: (MAR, 5),
        2073: (FEB, 22),
        2074: (JAN, 12),
        2075: (MAR, 2),
        2076: (FEB, 19),
        2077: (JAN, 9),
        2078: (FEB, 27),
        2079: (FEB, 16),
        2080: (JAN, 7),
        2081: (FEB, 23),
        2082: (FEB, 12),
        2083: (MAR, 3),
        2084: (FEB, 21),
        2085: (JAN, 11),
        2086: (FEB, 28),
        2087: (FEB, 18),
        2088: (JAN, 9),
        2089: (FEB, 25),
        2090: (FEB, 14),
        2091: (MAR, 5),
        2092: (FEB, 22),
        2093: (JAN, 12),
        2094: (MAR, 1),
        2095: (FEB, 19),
        2096: (JAN, 10),
        2097: (FEB, 27),
        2098: (FEB, 16),
        2099: (JAN, 6),
        2100: (FEB, 24),
    }

    # https://web.archive.org/web/20250121194712/https://www.timeanddate.com/holidays/india/vaisakhi
    VAISAKHI_DATES = {
        2001: (APR, 13),
        2002: (APR, 14),
        2003: (APR, 14),
        2004: (APR, 13),
        2005: (APR, 14),
        2006: (APR, 14),
        2007: (APR, 14),
        2008: (APR, 13),
        2009: (APR, 14),
        2010: (APR, 14),
        2011: (APR, 14),
        2012: (APR, 13),
        2013: (APR, 13),
        2014: (APR, 14),
        2015: (APR, 14),
        2016: (APR, 13),
        2017: (APR, 14),
        2018: (APR, 14),
        2019: (APR, 14),
        2020: (APR, 13),
        2021: (APR, 14),
        2022: (APR, 14),
        2023: (APR, 14),
        2024: (APR, 13),
        2025: (APR, 13),
        2026: (APR, 14),
        2027: (APR, 14),
        2028: (APR, 13),
        2029: (APR, 14),
        2030: (APR, 14),
        2031: (APR, 14),
        2032: (APR, 13),
        2033: (APR, 14),
        2034: (APR, 14),
        2035: (APR, 14),
    }

    def _get_holiday(self, holiday: str, year: int) -> tuple[Optional[date], bool]:
        estimated_dates = getattr(self, f"{holiday}_DATES", {})
        exact_dates = getattr(self, f"{holiday}_DATES_{_CustomCalendar.CUSTOM_ATTR_POSTFIX}", {})
        dt = exact_dates.get(year, estimated_dates.get(year, ()))
        return date(year, *dt) if dt else None, year not in exact_dates

    def _get_holiday_set(self, holiday: str, year: int) -> Iterable[tuple[date, bool]]:
        estimated_dates = getattr(self, f"{holiday}_DATES", {})
        exact_dates = getattr(self, f"{holiday}_DATES_{_CustomCalendar.CUSTOM_ATTR_POSTFIX}", {})
        for year in (year - 1, year):
            for dt in _normalize_tuple(exact_dates.get(year, estimated_dates.get(year, ()))):
                yield date(year, *dt), year not in exact_dates

    def buddha_purnima_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(BUDDHA_PURNIMA, year)

    def chhath_puja_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(CHHATH_PUJA, year)

    def diwali_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(DIWALI, year)

    def diwali_india_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(DIWALI_INDIA, year)

    def dussehra_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(DUSSEHRA, year)

    def ganesh_chaturthi_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(GANESH_CHATURTHI, year)

    def govardhan_puja_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(GOVARDHAN_PUJA, year)

    def gudi_padwa_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(GUDI_PADWA, year)

    def guru_gobind_singh_jayanti_date(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday_set(GURU_GOBIND_SINGH_JAYANTI, year)

    def guru_nanak_jayanti_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(GURU_NANAK_JAYANTI, year)

    def gyalpo_losar_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(GYALPO_LOSAR, year)

    def holi_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(HOLI, year)

    def pongal_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(PONGAL, year)

    def janmashtami_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(JANMASHTAMI, year)

    def maha_ashtami_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(MAHA_ASHTAMI, year)

    def maha_navami_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(MAHA_NAVAMI, year)

    def maha_shivaratri_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(MAHA_SHIVARATRI, year)

    def mahavir_jayanti_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(MAHAVIR_JAYANTI, year)

    def makar_sankranti_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(MAKAR_SANKRANTI, year)

    def onam_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(ONAM, year)

    def raksha_bandhan_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(RAKSHA_BANDHAN, year)

    def ram_navami_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(RAM_NAVAMI, year)

    def sharad_navratri_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(SHARAD_NAVRATRI, year)

    def sonam_losar_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(SONAM_LOSAR, year)

    def tamu_losar_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(TAMU_LOSAR, year)

    def thaipusam_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(THAIPUSAM, year)

    def vaisakhi_date(self, year: int) -> tuple[Optional[date], bool]:
        return self._get_holiday(VAISAKHI, year)


class _CustomHinduHolidays(_CustomCalendar, _HinduLunisolar):
    pass
