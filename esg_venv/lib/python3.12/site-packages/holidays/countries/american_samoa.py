#  holidays
#  --------
#  A fast, efficient Python library for generating country, province and state
#  specific sets of holidays on the fly. It aims to make determining whether a
#  specific date is a holiday as fast and flexible as possible.
#
#  Authors: <AUTHORS>
# <AUTHOR> <EMAIL> (c) 2017-2023
# <AUTHOR> <EMAIL> (c) 2014-2017
#  Website: https://github.com/vacanza/holidays
#  License: MIT (see LICENSE file)

from holidays.countries.united_states import UnitedStates
from holidays.mixins.child_entity import ChildEntity


class HolidaysAS(ChildEntity, UnitedStates):
    """American Samoa holidays.

    Alias of a US subdivision that is also officially assigned its own country code in ISO 3166-1.
    See <https://en.wikipedia.org/wiki/ISO_3166-2:US#Subdivisions_included_in_ISO_3166-1>
    """

    country = "AS"
    parent_entity = UnitedStates
    # Became a U.S. Territory since April 17th, 1900.
    start_year = 1901


class AmericanSamoa(HolidaysAS):
    pass


class AS(HolidaysAS):
    pass


class ASM(HolidaysAS):
    pass
