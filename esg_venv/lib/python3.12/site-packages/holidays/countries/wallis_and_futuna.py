#  holidays
#  --------
#  A fast, efficient Python library for generating country, province and state
#  specific sets of holidays on the fly. It aims to make determining whether a
#  specific date is a holiday as fast and flexible as possible.
#
#  Authors: <AUTHORS>
# <AUTHOR> <EMAIL> (c) 2017-2023
# <AUTHOR> <EMAIL> (c) 2014-2017
#  Website: https://github.com/vacanza/holidays
#  License: MIT (see LICENSE file)

from holidays.countries.france import France
from holidays.mixins.child_entity import ChildEntity


class HolidaysWF(ChildEntity, France):
    """Wallis and Futuna holidays.

    Alias of a French subdivision that is also officially assigned
    its own country code in ISO 3166-1.

    References:
        * <https://en.wikipedia.org/wiki/Wallis_and_Futuna>
        * <https://en.wikipedia.org/wiki/Public_holidays_in_France>
    """

    country = "WF"
    parent_entity = France
    # Separation from French Caledonia on July 29th, 1961.
    start_year = 1962


class WallisAndFutuna(HolidaysWF):
    pass


class WF(HolidaysWF):
    pass


class WLF(HolidaysWF):
    pass
