/*! For license information please see bundle.js.LICENSE.txt */
(()=>{var e,t,n={221:(e,t,n)=>{"use strict";var r=n(540);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(){}var o={d:{f:i,r:function(){throw Error(a(522))},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null},u=Symbol.for("react.portal"),l=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function s(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:u,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=l.T,n=o.p;try{if(l.T=null,o.p=2,e)return e()}finally{l.T=t,o.p=n,o.d.f()}},t.preconnect=function(e,t){"string"==typeof e&&(t=t?"string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:null,o.d.C(e,t))},t.prefetchDNS=function(e){"string"==typeof e&&o.d.D(e)},t.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var n=t.as,r=s(n,t.crossOrigin),a="string"==typeof t.integrity?t.integrity:void 0,i="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?o.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:i}):"script"===n&&o.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:i,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=s(t.as,t.crossOrigin);o.d.M(e,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&o.d.M(e)},t.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var n=t.as,r=s(n,t.crossOrigin);o.d.L(e,n,{crossOrigin:r,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"==typeof e)if(t){var n=s(t.as,t.crossOrigin);o.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else o.d.m(e)},t.requestFormReset=function(e){o.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return l.H.useFormState(e,t,n)},t.useFormStatus=function(){return l.H.useHostTransitionStatus()},t.version="19.1.0"},247:(e,t,n)=>{"use strict";var r=n(982),a=n(540),i=n(961);function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function s(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&null!==(e=e.alternate)&&(t=e.memoizedState),null!==t)return t.dehydrated}return null}function c(e){if(l(e)!==e)throw Error(o(188))}function f(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=f(e)))return t;e=e.sibling}return null}var d=Object.assign,p=Symbol.for("react.element"),h=Symbol.for("react.transitional.element"),g=Symbol.for("react.portal"),v=Symbol.for("react.fragment"),m=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),w=Symbol.for("react.consumer"),_=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),x=Symbol.for("react.suspense"),S=Symbol.for("react.suspense_list"),E=Symbol.for("react.memo"),C=Symbol.for("react.lazy");Symbol.for("react.scope");var T=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var P=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var z=Symbol.iterator;function M(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=z&&e[z]||e["@@iterator"])?e:null}var N=Symbol.for("react.client.reference");function A(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===N?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case v:return"Fragment";case y:return"Profiler";case m:return"StrictMode";case x:return"Suspense";case S:return"SuspenseList";case T:return"Activity"}if("object"==typeof e)switch(e.$$typeof){case g:return"Portal";case _:return(e.displayName||"Context")+".Provider";case w:return(e._context.displayName||"Context")+".Consumer";case k:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case E:return null!==(t=e.displayName||null)?t:A(e.type)||"Memo";case C:t=e._payload,e=e._init;try{return A(e(t))}catch(e){}}return null}var O=Array.isArray,L=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F=i.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D={pending:!1,data:null,method:null,action:null},R=[],j=-1;function U(e){return{current:e}}function I(e){0>j||(e.current=R[j],R[j]=null,j--)}function $(e,t){j++,R[j]=e.current,e.current=t}var B=U(null),H=U(null),W=U(null),V=U(null);function q(e,t){switch($(W,t),$(H,e),$(B,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?af(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=of(t=af(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}I(B),$(B,e)}function Q(){I(B),I(H),I(W)}function Y(e){null!==e.memoizedState&&$(V,e);var t=B.current,n=of(t,e.type);t!==n&&($(H,e),$(B,n))}function G(e){H.current===e&&(I(B),I(H)),V.current===e&&(I(V),Gf._currentValue=D)}var K=Object.prototype.hasOwnProperty,X=r.unstable_scheduleCallback,Z=r.unstable_cancelCallback,J=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,ie=r.unstable_NormalPriority,oe=r.unstable_LowPriority,ue=r.unstable_IdlePriority,le=r.log,se=r.unstable_setDisableYieldValue,ce=null,fe=null;function de(e){if("function"==typeof le&&se(e),fe&&"function"==typeof fe.setStrictMode)try{fe.setStrictMode(ce,e)}catch(e){}}var pe=Math.clz32?Math.clz32:function(e){return 0==(e>>>=0)?32:31-(he(e)/ge|0)|0},he=Math.log,ge=Math.LN2,ve=256,me=4194304;function ye(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,i=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var u=134217727&r;return 0!==u?0!=(r=u&~i)?a=ye(r):0!=(o&=u)?a=ye(o):n||0!=(n=u&~e)&&(a=ye(n)):0!=(u=r&~i)?a=ye(u):0!==o?a=ye(o):n||0!=(n=r&~e)&&(a=ye(n)),0===a?0:0===t||t===a||t&i||!((i=a&-a)>=(n=t&-t)||32===i&&4194048&n)?a:t}function we(e,t){return!(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function _e(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ke(){var e=ve;return!(4194048&(ve<<=1))&&(ve=256),e}function xe(){var e=me;return!(62914560&(me<<=1))&&(me=4194304),e}function Se(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ee(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ce(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-pe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Te(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-pe(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function Pe(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function ze(e){return 2<(e&=-e)?8<e?134217727&e?32:268435456:8:2}function Me(){var e=F.p;return 0!==e?e:void 0===(e=window.event)?32:cd(e.type)}var Ne=Math.random().toString(36).slice(2),Ae="__reactFiber$"+Ne,Oe="__reactProps$"+Ne,Le="__reactContainer$"+Ne,Fe="__reactEvents$"+Ne,De="__reactListeners$"+Ne,Re="__reactHandles$"+Ne,je="__reactResources$"+Ne,Ue="__reactMarker$"+Ne;function Ie(e){delete e[Ae],delete e[Oe],delete e[Fe],delete e[De],delete e[Re]}function $e(e){var t=e[Ae];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Le]||n[Ae]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=wf(e);null!==e;){if(n=e[Ae])return n;e=wf(e)}return t}n=(e=n).parentNode}return null}function Be(e){if(e=e[Ae]||e[Le]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function He(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(o(33))}function We(e){var t=e[je];return t||(t=e[je]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ve(e){e[Ue]=!0}var qe=new Set,Qe={};function Ye(e,t){Ge(e,t),Ge(e+"Capture",t)}function Ge(e,t){for(Qe[e]=t,e=0;e<t.length;e++)qe.add(t[e])}var Ke,Xe,Ze=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Je={},et={};function tt(e,t,n){if(a=t,K.call(et,a)||!K.call(Je,a)&&(Ze.test(a)?et[a]=!0:(Je[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function at(e){if(void 0===Ke)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);Ke=t&&t[1]||"",Xe=-1<e.stack.indexOf("\n    at")?" (<anonymous>)":-1<e.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Ke+e+Xe}var it=!1;function ot(e,t){if(!e||it)return"";it=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(e){var r=e}Reflect.construct(e,[],n)}else{try{n.call()}catch(e){r=e}e.call(n.prototype)}}else{try{throw Error()}catch(e){r=e}(n=e())&&"function"==typeof n.catch&&n.catch((function(){}))}}catch(e){if(e&&r&&"string"==typeof e.stack)return[e.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=r.DetermineComponentFrameRoot(),o=i[0],u=i[1];if(o&&u){var l=o.split("\n"),s=u.split("\n");for(a=r=0;r<l.length&&!l[r].includes("DetermineComponentFrameRoot");)r++;for(;a<s.length&&!s[a].includes("DetermineComponentFrameRoot");)a++;if(r===l.length||a===s.length)for(r=l.length-1,a=s.length-1;1<=r&&0<=a&&l[r]!==s[a];)a--;for(;1<=r&&0<=a;r--,a--)if(l[r]!==s[a]){if(1!==r||1!==a)do{if(r--,0>--a||l[r]!==s[a]){var c="\n"+l[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=a);break}}}finally{it=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?at(n):""}function ut(e){switch(e.tag){case 26:case 27:case 5:return at(e.type);case 16:return at("Lazy");case 13:return at("Suspense");case 19:return at("SuspenseList");case 0:case 15:return ot(e.type,!1);case 11:return ot(e.type.render,!1);case 1:return ot(e.type,!0);case 31:return at("Activity");default:return""}}function lt(e){try{var t="";do{t+=ut(e),e=e.return}while(e);return t}catch(e){return"\nError generating stack: "+e.message+"\n"+e.stack}}function st(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ct(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function ft(e){e._valueTracker||(e._valueTracker=function(e){var t=ct(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function dt(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ct(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function pt(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var ht=/[\n"\\]/g;function gt(e){return e.replace(ht,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function vt(e,t,n,r,a,i,o,u){e.name="",null!=o&&"function"!=typeof o&&"symbol"!=typeof o&&"boolean"!=typeof o?e.type=o:e.removeAttribute("type"),null!=t?"number"===o?(0===t&&""===e.value||e.value!=t)&&(e.value=""+st(t)):e.value!==""+st(t)&&(e.value=""+st(t)):"submit"!==o&&"reset"!==o||e.removeAttribute("value"),null!=t?yt(e,o,st(t)):null!=n?yt(e,o,st(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=i&&(e.defaultChecked=!!i),null!=a&&(e.checked=a&&"function"!=typeof a&&"symbol"!=typeof a),null!=u&&"function"!=typeof u&&"symbol"!=typeof u&&"boolean"!=typeof u?e.name=""+st(u):e.removeAttribute("name")}function mt(e,t,n,r,a,i,o,u){if(null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&"boolean"!=typeof i&&(e.type=i),null!=t||null!=n){if(("submit"===i||"reset"===i)&&null==t)return;n=null!=n?""+st(n):"",t=null!=t?""+st(t):n,u||t===e.value||(e.value=t),e.defaultValue=t}r="function"!=typeof(r=null!=r?r:a)&&"symbol"!=typeof r&&!!r,e.checked=u?e.checked:!!r,e.defaultChecked=!!r,null!=o&&"function"!=typeof o&&"symbol"!=typeof o&&"boolean"!=typeof o&&(e.name=o)}function yt(e,t,n){"number"===t&&pt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+st(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function wt(e,t,n){null==t||((t=""+st(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+st(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function _t(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(o(92));if(O(r)){if(1<r.length)throw Error(o(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=st(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function kt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var xt=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function St(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"==typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!=typeof n||0===n||xt.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Et(e,t,n){if(null!=t&&"object"!=typeof t)throw Error(o(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&St(e,a,r)}else for(var i in t)t.hasOwnProperty(i)&&St(e,i,t[i])}function Ct(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Pt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function zt(e){return Pt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Mt=null;function Nt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var At=null,Ot=null;function Lt(e){var t=Be(e);if(t&&(e=t.stateNode)){var n=e[Oe]||null;e:switch(e=t.stateNode,t.type){case"input":if(vt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+gt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[Oe]||null;if(!a)throw Error(o(90));vt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&dt(r)}break e;case"textarea":wt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&bt(e,!!n.multiple,t,!1)}}}var Ft=!1;function Dt(e,t,n){if(Ft)return e(t,n);Ft=!0;try{return e(t)}finally{if(Ft=!1,(null!==At||null!==Ot)&&($s(),At&&(t=At,e=Ot,Ot=At=null,Lt(t),e)))for(t=0;t<e.length;t++)Lt(e[t])}}function Rt(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Oe]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(o(231,t,typeof n));return n}var jt=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),Ut=!1;if(jt)try{var It={};Object.defineProperty(It,"passive",{get:function(){Ut=!0}}),window.addEventListener("test",It,It),window.removeEventListener("test",It,It)}catch(e){Ut=!1}var $t=null,Bt=null,Ht=null;function Wt(){if(Ht)return Ht;var e,t,n=Bt,r=n.length,a="value"in $t?$t.value:$t.textContent,i=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[i-t];t++);return Ht=a.slice(e,1<t?1-t:void 0)}function Vt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function qt(){return!0}function Qt(){return!1}function Yt(e){function t(t,n,r,a,i){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?qt:Qt,this.isPropagationStopped=Qt,this}return d(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=qt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=qt)},persist:function(){},isPersistent:qt}),t}var Gt,Kt,Xt,Zt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Jt=Yt(Zt),en=d({},Zt,{view:0,detail:0}),tn=Yt(en),nn=d({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xt&&(Xt&&"mousemove"===e.type?(Gt=e.screenX-Xt.screenX,Kt=e.screenY-Xt.screenY):Kt=Gt=0,Xt=e),Gt)},movementY:function(e){return"movementY"in e?e.movementY:Kt}}),rn=Yt(nn),an=Yt(d({},nn,{dataTransfer:0})),on=Yt(d({},en,{relatedTarget:0})),un=Yt(d({},Zt,{animationName:0,elapsedTime:0,pseudoElement:0})),ln=Yt(d({},Zt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),sn=Yt(d({},Zt,{data:0})),cn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},fn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},dn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=dn[e])&&!!t[e]}function hn(){return pn}var gn=Yt(d({},en,{key:function(e){if(e.key){var t=cn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Vt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?fn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hn,charCode:function(e){return"keypress"===e.type?Vt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Vt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),vn=Yt(d({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),mn=Yt(d({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hn})),yn=Yt(d({},Zt,{propertyName:0,elapsedTime:0,pseudoElement:0})),bn=Yt(d({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),wn=Yt(d({},Zt,{newState:0,oldState:0})),_n=[9,13,27,32],kn=jt&&"CompositionEvent"in window,xn=null;jt&&"documentMode"in document&&(xn=document.documentMode);var Sn=jt&&"TextEvent"in window&&!xn,En=jt&&(!kn||xn&&8<xn&&11>=xn),Cn=String.fromCharCode(32),Tn=!1;function Pn(e,t){switch(e){case"keyup":return-1!==_n.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function zn(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Mn=!1,Nn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function An(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Nn[e.type]:"textarea"===t}function On(e,t,n,r){At?Ot?Ot.push(r):Ot=[r]:At=r,0<(t=Wc(t,"onChange")).length&&(n=new Jt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ln=null,Fn=null;function Dn(e){Dc(e,0)}function Rn(e){if(dt(He(e)))return e}function jn(e,t){if("change"===e)return t}var Un=!1;if(jt){var In;if(jt){var $n="oninput"in document;if(!$n){var Bn=document.createElement("div");Bn.setAttribute("oninput","return;"),$n="function"==typeof Bn.oninput}In=$n}else In=!1;Un=In&&(!document.documentMode||9<document.documentMode)}function Hn(){Ln&&(Ln.detachEvent("onpropertychange",Wn),Fn=Ln=null)}function Wn(e){if("value"===e.propertyName&&Rn(Fn)){var t=[];On(t,Fn,e,Nt(e)),Dt(Dn,t)}}function Vn(e,t,n){"focusin"===e?(Hn(),Fn=n,(Ln=t).attachEvent("onpropertychange",Wn)):"focusout"===e&&Hn()}function qn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Rn(Fn)}function Qn(e,t){if("click"===e)return Rn(t)}function Yn(e,t){if("input"===e||"change"===e)return Rn(t)}var Gn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function Kn(e,t){if(Gn(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!K.call(t,a)||!Gn(e[a],t[a]))return!1}return!0}function Xn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Zn(e,t){var n,r=Xn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Xn(r)}}function Jn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Jn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=pt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(e){n=!1}if(!n)break;t=pt((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=jt&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,ir=null,or=!1;function ur(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;or||null==rr||rr!==pt(r)||(r="selectionStart"in(r=rr)&&tr(r)?{start:r.selectionStart,end:r.selectionEnd}:{anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ir&&Kn(ir,r)||(ir=r,0<(r=Wc(ar,"onSelect")).length&&(t=new Jt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function lr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var sr={animationend:lr("Animation","AnimationEnd"),animationiteration:lr("Animation","AnimationIteration"),animationstart:lr("Animation","AnimationStart"),transitionrun:lr("Transition","TransitionRun"),transitionstart:lr("Transition","TransitionStart"),transitioncancel:lr("Transition","TransitionCancel"),transitionend:lr("Transition","TransitionEnd")},cr={},fr={};function dr(e){if(cr[e])return cr[e];if(!sr[e])return e;var t,n=sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in fr)return cr[e]=n[t];return e}jt&&(fr=document.createElement("div").style,"AnimationEvent"in window||(delete sr.animationend.animation,delete sr.animationiteration.animation,delete sr.animationstart.animation),"TransitionEvent"in window||delete sr.transitionend.transition);var pr=dr("animationend"),hr=dr("animationiteration"),gr=dr("animationstart"),vr=dr("transitionrun"),mr=dr("transitionstart"),yr=dr("transitioncancel"),br=dr("transitionend"),wr=new Map,_r="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function kr(e,t){wr.set(e,t),Ye(t,[e])}_r.push("scrollEnd");var xr=new WeakMap;function Sr(e,t){if("object"==typeof e&&null!==e){var n=xr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:lt(t)},xr.set(e,t),t)}return{value:e,source:t,stack:lt(t)}}var Er=[],Cr=0,Tr=0;function Pr(){for(var e=Cr,t=Tr=Cr=0;t<e;){var n=Er[t];Er[t++]=null;var r=Er[t];Er[t++]=null;var a=Er[t];Er[t++]=null;var i=Er[t];if(Er[t++]=null,null!==r&&null!==a){var o=r.pending;null===o?a.next=a:(a.next=o.next,o.next=a),r.pending=a}0!==i&&Ar(n,a,i)}}function zr(e,t,n,r){Er[Cr++]=e,Er[Cr++]=t,Er[Cr++]=n,Er[Cr++]=r,Tr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Mr(e,t,n,r){return zr(e,t,n,r),Or(e)}function Nr(e,t){return zr(e,null,null,t),Or(e)}function Ar(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,i=e.return;null!==i;)i.childLanes|=n,null!==(r=i.alternate)&&(r.childLanes|=n),22===i.tag&&(null===(e=i.stateNode)||1&e._visibility||(a=!0)),e=i,i=i.return;return 3===e.tag?(i=e.stateNode,a&&null!==t&&(a=31-pe(n),null===(r=(e=i.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),i):null}function Or(e){if(50<As)throw As=0,Os=null,Error(o(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Lr={};function Fr(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Dr(e,t,n,r){return new Fr(e,t,n,r)}function Rr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function jr(e,t){var n=e.alternate;return null===n?((n=Dr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ur(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ir(e,t,n,r,a,i){var u=0;if(r=e,"function"==typeof e)Rr(e)&&(u=1);else if("string"==typeof e)u=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!=typeof t.precedence||"string"!=typeof t.href||""===t.href)break;return!0;case"link":if("string"!=typeof t.rel||"string"!=typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"==typeof t.precedence&&null==e);case"script":if(t.async&&"function"!=typeof t.async&&"symbol"!=typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"==typeof t.src)return!0}return!1}(e,n,B.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case T:return(e=Dr(31,n,t,a)).elementType=T,e.lanes=i,e;case v:return $r(n.children,a,i,t);case m:u=8,a|=24;break;case y:return(e=Dr(12,n,t,2|a)).elementType=y,e.lanes=i,e;case x:return(e=Dr(13,n,t,a)).elementType=x,e.lanes=i,e;case S:return(e=Dr(19,n,t,a)).elementType=S,e.lanes=i,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case b:case _:u=10;break e;case w:u=9;break e;case k:u=11;break e;case E:u=14;break e;case C:u=16,r=null;break e}u=29,n=Error(o(130,null===e?"null":typeof e,"")),r=null}return(t=Dr(u,n,t,a)).elementType=e,t.type=r,t.lanes=i,t}function $r(e,t,n,r){return(e=Dr(7,e,r,t)).lanes=n,e}function Br(e,t,n){return(e=Dr(6,e,null,t)).lanes=n,e}function Hr(e,t,n){return(t=Dr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Wr=[],Vr=0,qr=null,Qr=0,Yr=[],Gr=0,Kr=null,Xr=1,Zr="";function Jr(e,t){Wr[Vr++]=Qr,Wr[Vr++]=qr,qr=e,Qr=t}function ea(e,t,n){Yr[Gr++]=Xr,Yr[Gr++]=Zr,Yr[Gr++]=Kr,Kr=e;var r=Xr;e=Zr;var a=32-pe(r)-1;r&=~(1<<a),n+=1;var i=32-pe(t)+a;if(30<i){var o=a-a%5;i=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Xr=1<<32-pe(t)+a|n<<a|r,Zr=i+e}else Xr=1<<i|n<<a|r,Zr=e}function ta(e){null!==e.return&&(Jr(e,1),ea(e,1,0))}function na(e){for(;e===qr;)qr=Wr[--Vr],Wr[Vr]=null,Qr=Wr[--Vr],Wr[Vr]=null;for(;e===Kr;)Kr=Yr[--Gr],Yr[Gr]=null,Zr=Yr[--Gr],Yr[Gr]=null,Xr=Yr[--Gr],Yr[Gr]=null}var ra=null,aa=null,ia=!1,oa=null,ua=!1,la=Error(o(519));function sa(e){throw ga(Sr(Error(o(418,"")),e)),la}function ca(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Ae]=e,t[Oe]=r,n){case"dialog":Rc("cancel",t),Rc("close",t);break;case"iframe":case"object":case"embed":Rc("load",t);break;case"video":case"audio":for(n=0;n<Lc.length;n++)Rc(Lc[n],t);break;case"source":Rc("error",t);break;case"img":case"image":case"link":Rc("error",t),Rc("load",t);break;case"details":Rc("toggle",t);break;case"input":Rc("invalid",t),mt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),ft(t);break;case"select":Rc("invalid",t);break;case"textarea":Rc("invalid",t),_t(t,r.value,r.defaultValue,r.children),ft(t)}"string"!=typeof(n=r.children)&&"number"!=typeof n&&"bigint"!=typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Kc(t.textContent,n)?(null!=r.popover&&(Rc("beforetoggle",t),Rc("toggle",t)),null!=r.onScroll&&Rc("scroll",t),null!=r.onScrollEnd&&Rc("scrollend",t),null!=r.onClick&&(t.onclick=Xc),t=!0):t=!1,t||sa(e)}function fa(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(ua=!1);case 27:case 3:return void(ua=!0);default:ra=ra.return}}function da(e){if(e!==ra)return!1;if(!ia)return fa(e),ia=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||uf(e.type,e.memoizedProps)),t=!t),t&&aa&&sa(e),fa(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){aa=yf(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}aa=null}}else 27===n?(n=aa,hf(e.type)?(e=bf,bf=null,aa=e):aa=n):aa=ra?yf(e.stateNode.nextSibling):null;return!0}function pa(){aa=ra=null,ia=!1}function ha(){var e=oa;return null!==e&&(null===bs?bs=e:bs.push.apply(bs,e),oa=null),e}function ga(e){null===oa?oa=[e]:oa.push(e)}var va=U(null),ma=null,ya=null;function ba(e,t,n){$(va,t._currentValue),t._currentValue=n}function wa(e){e._currentValue=va.current,I(va)}function _a(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ka(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var i=a.dependencies;if(null!==i){var u=a.child;i=i.firstContext;e:for(;null!==i;){var l=i;i=a;for(var s=0;s<t.length;s++)if(l.context===t[s]){i.lanes|=n,null!==(l=i.alternate)&&(l.lanes|=n),_a(i.return,n,e),r||(u=null);break e}i=l.next}}else if(18===a.tag){if(null===(u=a.return))throw Error(o(341));u.lanes|=n,null!==(i=u.alternate)&&(i.lanes|=n),_a(u,n,e),u=null}else u=a.child;if(null!==u)u.return=a;else for(u=a;null!==u;){if(u===e){u=null;break}if(null!==(a=u.sibling)){a.return=u.return,u=a;break}u=u.return}a=u}}function xa(e,t,n,r){e=null;for(var a=t,i=!1;null!==a;){if(!i)if(524288&a.flags)i=!0;else if(262144&a.flags)break;if(10===a.tag){var u=a.alternate;if(null===u)throw Error(o(387));if(null!==(u=u.memoizedProps)){var l=a.type;Gn(a.pendingProps.value,u.value)||(null!==e?e.push(l):e=[l])}}else if(a===V.current){if(null===(u=a.alternate))throw Error(o(387));u.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Gf):e=[Gf])}a=a.return}null!==e&&ka(t,e,n,r),t.flags|=262144}function Sa(e){for(e=e.firstContext;null!==e;){if(!Gn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ea(e){ma=e,ya=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ca(e){return Pa(ma,e)}function Ta(e,t){return null===ma&&Ea(e),Pa(e,t)}function Pa(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===ya){if(null===e)throw Error(o(308));ya=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ya=ya.next=t;return n}var za="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},Ma=r.unstable_scheduleCallback,Na=r.unstable_NormalPriority,Aa={$$typeof:_,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Oa(){return{controller:new za,data:new Map,refCount:0}}function La(e){e.refCount--,0===e.refCount&&Ma(Na,(function(){e.controller.abort()}))}var Fa=null,Da=0,Ra=0,ja=null;function Ua(){if(0==--Da&&null!==Fa){null!==ja&&(ja.status="fulfilled");var e=Fa;Fa=null,Ra=0,ja=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ia=L.S;L.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===Fa){var n=Fa=[];Da=0,Ra=zc(),ja={status:"pending",value:void 0,then:function(e){n.push(e)}}}Da++,t.then(Ua,Ua)}(0,t),null!==Ia&&Ia(e,t)};var $a=U(null);function Ba(){var e=$a.current;return null!==e?e:rs.pooledCache}function Ha(e,t){$($a,null===t?$a.current:t.pool)}function Wa(){var e=Ba();return null===e?null:{parent:Aa._currentValue,pool:e}}var Va=Error(o(460)),qa=Error(o(474)),Qa=Error(o(542)),Ya={then:function(){}};function Ga(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Ka(){}function Xa(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Ka,Ka),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw ei(e=t.reason),e;default:if("string"==typeof t.status)t.then(Ka,Ka);else{if(null!==(e=rs)&&100<e.shellSuspendCounter)throw Error(o(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":throw ei(e=t.reason),e}throw Za=t,Va}}var Za=null;function Ja(){if(null===Za)throw Error(o(459));var e=Za;return Za=null,e}function ei(e){if(e===Va||e===Qa)throw Error(o(483))}var ti=!1;function ni(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ri(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ai(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ii(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&ns){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Or(e),Ar(e,null,n),t}return zr(e,r,t,n),Or(e)}function oi(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Te(e,n)}}function ui(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var o={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===i?a=i=o:i=i.next=o,n=n.next}while(null!==n);null===i?a=i=t:i=i.next=t}else a=i=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:i,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var li=!1;function si(){if(li&&null!==ja)throw ja}function ci(e,t,n,r){li=!1;var a=e.updateQueue;ti=!1;var i=a.firstBaseUpdate,o=a.lastBaseUpdate,u=a.shared.pending;if(null!==u){a.shared.pending=null;var l=u,s=l.next;l.next=null,null===o?i=s:o.next=s,o=l;var c=e.alternate;null!==c&&(u=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===u?c.firstBaseUpdate=s:u.next=s,c.lastBaseUpdate=l)}if(null!==i){var f=a.baseState;for(o=0,c=s=l=null,u=i;;){var p=-536870913&u.lane,h=p!==u.lane;if(h?(is&p)===p:(r&p)===p){0!==p&&p===Ra&&(li=!0),null!==c&&(c=c.next={lane:0,tag:u.tag,payload:u.payload,callback:null,next:null});e:{var g=e,v=u;p=t;var m=n;switch(v.tag){case 1:if("function"==typeof(g=v.payload)){f=g.call(m,f,p);break e}f=g;break e;case 3:g.flags=-65537&g.flags|128;case 0:if(null==(p="function"==typeof(g=v.payload)?g.call(m,f,p):g))break e;f=d({},f,p);break e;case 2:ti=!0}}null!==(p=u.callback)&&(e.flags|=64,h&&(e.flags|=8192),null===(h=a.callbacks)?a.callbacks=[p]:h.push(p))}else h={lane:p,tag:u.tag,payload:u.payload,callback:u.callback,next:null},null===c?(s=c=h,l=f):c=c.next=h,o|=p;if(null===(u=u.next)){if(null===(u=a.shared.pending))break;u=(h=u).next,h.next=null,a.lastBaseUpdate=h,a.shared.pending=null}}null===c&&(l=f),a.baseState=l,a.firstBaseUpdate=s,a.lastBaseUpdate=c,null===i&&(a.shared.lanes=0),ps|=o,e.lanes=o,e.memoizedState=f}}function fi(e,t){if("function"!=typeof e)throw Error(o(191,e));e.call(t)}function di(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)fi(n[e],t)}var pi=U(null),hi=U(0);function gi(e,t){$(hi,e=fs),$(pi,t),fs=e|t.baseLanes}function vi(){$(hi,fs),$(pi,pi.current)}function mi(){fs=hi.current,I(pi),I(hi)}var yi=0,bi=null,wi=null,_i=null,ki=!1,xi=!1,Si=!1,Ei=0,Ci=0,Ti=null,Pi=0;function zi(){throw Error(o(321))}function Mi(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Gn(e[n],t[n]))return!1;return!0}function Ni(e,t,n,r,a,i){return yi=i,bi=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,L.H=null===e||null===e.memoizedState?qo:Qo,Si=!1,i=n(r,a),Si=!1,xi&&(i=Oi(t,n,r,a)),Ai(e),i}function Ai(e){L.H=Vo;var t=null!==wi&&null!==wi.next;if(yi=0,_i=wi=bi=null,ki=!1,Ci=0,Ti=null,t)throw Error(o(300));null===e||Tu||null!==(e=e.dependencies)&&Sa(e)&&(Tu=!0)}function Oi(e,t,n,r){bi=e;var a=0;do{if(xi&&(Ti=null),Ci=0,xi=!1,25<=a)throw Error(o(301));if(a+=1,_i=wi=null,null!=e.updateQueue){var i=e.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,null!=i.memoCache&&(i.memoCache.index=0)}L.H=Yo,i=t(n,r)}while(xi);return i}function Li(){var e=L.H,t=e.useState()[0];return t="function"==typeof t.then?Ii(t):t,e=e.useState()[0],(null!==wi?wi.memoizedState:null)!==e&&(bi.flags|=1024),t}function Fi(){var e=0!==Ei;return Ei=0,e}function Di(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Ri(e){if(ki){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}ki=!1}yi=0,_i=wi=bi=null,xi=!1,Ci=Ei=0,Ti=null}function ji(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===_i?bi.memoizedState=_i=e:_i=_i.next=e,_i}function Ui(){if(null===wi){var e=bi.alternate;e=null!==e?e.memoizedState:null}else e=wi.next;var t=null===_i?bi.memoizedState:_i.next;if(null!==t)_i=t,wi=e;else{if(null===e){if(null===bi.alternate)throw Error(o(467));throw Error(o(310))}e={memoizedState:(wi=e).memoizedState,baseState:wi.baseState,baseQueue:wi.baseQueue,queue:wi.queue,next:null},null===_i?bi.memoizedState=_i=e:_i=_i.next=e}return _i}function Ii(e){var t=Ci;return Ci+=1,null===Ti&&(Ti=[]),e=Xa(Ti,e,t),t=bi,null===(null===_i?t.memoizedState:_i.next)&&(t=t.alternate,L.H=null===t||null===t.memoizedState?qo:Qo),e}function $i(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return Ii(e);if(e.$$typeof===_)return Ca(e)}throw Error(o(438,String(e)))}function Bi(e){var t=null,n=bi.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=bi.alternate;null!==r&&null!==(r=r.updateQueue)&&null!=(r=r.memoCache)&&(t={data:r.data.map((function(e){return e.slice()})),index:0})}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},bi.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=P;return t.index++,n}function Hi(e,t){return"function"==typeof t?t(e):t}function Wi(e){return Vi(Ui(),wi,e)}function Vi(e,t,n){var r=e.queue;if(null===r)throw Error(o(311));r.lastRenderedReducer=n;var a=e.baseQueue,i=r.pending;if(null!==i){if(null!==a){var u=a.next;a.next=i.next,i.next=u}t.baseQueue=a=i,r.pending=null}if(i=e.baseState,null===a)e.memoizedState=i;else{var l=u=null,s=null,c=t=a.next,f=!1;do{var d=-536870913&c.lane;if(d!==c.lane?(is&d)===d:(yi&d)===d){var p=c.revertLane;if(0===p)null!==s&&(s=s.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),d===Ra&&(f=!0);else{if((yi&p)===p){c=c.next,p===Ra&&(f=!0);continue}d={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===s?(l=s=d,u=i):s=s.next=d,bi.lanes|=p,ps|=p}d=c.action,Si&&n(i,d),i=c.hasEagerState?c.eagerState:n(i,d)}else p={lane:d,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===s?(l=s=p,u=i):s=s.next=p,bi.lanes|=d,ps|=d;c=c.next}while(null!==c&&c!==t);if(null===s?u=i:s.next=l,!Gn(i,e.memoizedState)&&(Tu=!0,f&&null!==(n=ja)))throw n;e.memoizedState=i,e.baseState=u,e.baseQueue=s,r.lastRenderedState=i}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function qi(e){var t=Ui(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,i=t.memoizedState;if(null!==a){n.pending=null;var u=a=a.next;do{i=e(i,u.action),u=u.next}while(u!==a);Gn(i,t.memoizedState)||(Tu=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Qi(e,t,n){var r=bi,a=Ui(),i=ia;if(i){if(void 0===n)throw Error(o(407));n=n()}else n=t();var u=!Gn((wi||a).memoizedState,n);if(u&&(a.memoizedState=n,Tu=!0),a=a.queue,mo(2048,8,Ki.bind(null,r,a,e),[e]),a.getSnapshot!==t||u||null!==_i&&1&_i.memoizedState.tag){if(r.flags|=2048,ho(9,{destroy:void 0,resource:void 0},Gi.bind(null,r,a,n,t),null),null===rs)throw Error(o(349));i||124&yi||Yi(r,t,n)}return n}function Yi(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=bi.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},bi.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Gi(e,t,n,r){t.value=n,t.getSnapshot=r,Xi(t)&&Zi(e)}function Ki(e,t,n){return n((function(){Xi(t)&&Zi(e)}))}function Xi(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Gn(e,n)}catch(e){return!0}}function Zi(e){var t=Nr(e,2);null!==t&&Ds(t,0,2)}function Ji(e){var t=ji();if("function"==typeof e){var n=e;if(e=n(),Si){de(!0);try{n()}finally{de(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hi,lastRenderedState:e},t}function eo(e,t,n,r){return e.baseState=n,Vi(e,wi,"function"==typeof r?r:Hi)}function to(e,t,n,r,a){if(Bo(e))throw Error(o(485));if(null!==(e=t.action)){var i={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){i.listeners.push(e)}};null!==L.T?n(!0):i.isTransition=!1,r(i),null===(n=t.pending)?(i.next=t.pending=i,no(t,i)):(i.next=n.next,t.pending=n.next=i)}}function no(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var i=L.T,o={};L.T=o;try{var u=n(a,r),l=L.S;null!==l&&l(o,u),ro(e,t,u)}catch(n){io(e,t,n)}finally{L.T=i}}else try{ro(e,t,i=n(a,r))}catch(n){io(e,t,n)}}function ro(e,t,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then((function(n){ao(e,t,n)}),(function(n){return io(e,t,n)})):ao(e,t,n)}function ao(e,t,n){t.status="fulfilled",t.value=n,oo(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,no(e,n)))}function io(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,oo(t),t=t.next}while(t!==r)}e.action=null}function oo(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function uo(e,t){return t}function lo(e,t){if(ia){var n=rs.formState;if(null!==n){e:{var r=bi;if(ia){if(aa){t:{for(var a=aa,i=ua;8!==a.nodeType;){if(!i){a=null;break t}if(null===(a=yf(a.nextSibling))){a=null;break t}}a="F!"===(i=a.data)||"F"===i?a:null}if(a){aa=yf(a.nextSibling),r="F!"===a.data;break e}}sa(r)}r=!1}r&&(t=n[0])}}return(n=ji()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:uo,lastRenderedState:t},n.queue=r,n=Uo.bind(null,bi,r),r.dispatch=n,r=Ji(!1),i=$o.bind(null,bi,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=ji()).queue=a,n=to.bind(null,bi,a,i,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function so(e){return co(Ui(),wi,e)}function co(e,t,n){if(t=Vi(e,t,uo)[0],e=Wi(Hi)[0],"object"==typeof t&&null!==t&&"function"==typeof t.then)try{var r=Ii(t)}catch(e){if(e===Va)throw Qa;throw e}else r=t;var a=(t=Ui()).queue,i=a.dispatch;return n!==t.memoizedState&&(bi.flags|=2048,ho(9,{destroy:void 0,resource:void 0},fo.bind(null,a,n),null)),[r,i,e]}function fo(e,t){e.action=t}function po(e){var t=Ui(),n=wi;if(null!==n)return co(t,n,e);Ui(),t=t.memoizedState;var r=(n=Ui()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function ho(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=bi.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},bi.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function go(){return Ui().memoizedState}function vo(e,t,n,r){var a=ji();r=void 0===r?null:r,bi.flags|=e,a.memoizedState=ho(1|t,{destroy:void 0,resource:void 0},n,r)}function mo(e,t,n,r){var a=Ui();r=void 0===r?null:r;var i=a.memoizedState.inst;null!==wi&&null!==r&&Mi(r,wi.memoizedState.deps)?a.memoizedState=ho(t,i,n,r):(bi.flags|=e,a.memoizedState=ho(1|t,i,n,r))}function yo(e,t){vo(8390656,8,e,t)}function bo(e,t){mo(2048,8,e,t)}function wo(e,t){return mo(4,2,e,t)}function _o(e,t){return mo(4,4,e,t)}function ko(e,t){if("function"==typeof t){e=e();var n=t(e);return function(){"function"==typeof n?n():t(null)}}if(null!=t)return e=e(),t.current=e,function(){t.current=null}}function xo(e,t,n){n=null!=n?n.concat([e]):null,mo(4,4,ko.bind(null,t,e),n)}function So(){}function Eo(e,t){var n=Ui();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Mi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Co(e,t){var n=Ui();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Mi(t,r[1]))return r[0];if(r=e(),Si){de(!0);try{e()}finally{de(!1)}}return n.memoizedState=[r,t],r}function To(e,t,n){return void 0===n||1073741824&yi?e.memoizedState=t:(e.memoizedState=n,e=Fs(),bi.lanes|=e,ps|=e,n)}function Po(e,t,n,r){return Gn(n,t)?n:null!==pi.current?(e=To(e,n,r),Gn(e,t)||(Tu=!0),e):42&yi?(e=Fs(),bi.lanes|=e,ps|=e,t):(Tu=!0,e.memoizedState=n)}function zo(e,t,n,r,a){var i=F.p;F.p=0!==i&&8>i?i:8;var o,u,l,s=L.T,c={};L.T=c,$o(e,!1,t,n);try{var f=a(),d=L.S;null!==d&&d(c,f),null!==f&&"object"==typeof f&&"function"==typeof f.then?Io(e,t,(o=r,u=[],l={status:"pending",value:null,reason:null,then:function(e){u.push(e)}},f.then((function(){l.status="fulfilled",l.value=o;for(var e=0;e<u.length;e++)(0,u[e])(o)}),(function(e){for(l.status="rejected",l.reason=e,e=0;e<u.length;e++)(0,u[e])(void 0)})),l),Ls()):Io(e,t,r,Ls())}catch(n){Io(e,t,{then:function(){},status:"rejected",reason:n},Ls())}finally{F.p=i,L.T=s}}function Mo(){}function No(e,t,n,r){if(5!==e.tag)throw Error(o(476));var a=Ao(e).queue;zo(e,a,t,D,null===n?Mo:function(){return Oo(e),n(r)})}function Ao(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:D,baseState:D,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hi,lastRenderedState:D},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hi,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Oo(e){Io(e,Ao(e).next.queue,{},Ls())}function Lo(){return Ca(Gf)}function Fo(){return Ui().memoizedState}function Do(){return Ui().memoizedState}function Ro(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Ls(),r=ii(t,e=ai(n),n);return null!==r&&(Ds(r,0,n),oi(r,t,n)),t={cache:Oa()},void(e.payload=t)}t=t.return}}function jo(e,t,n){var r=Ls();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Bo(e)?Ho(t,n):null!==(n=Mr(e,t,n,r))&&(Ds(n,0,r),Wo(n,t,r))}function Uo(e,t,n){Io(e,t,n,Ls())}function Io(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Bo(e))Ho(t,a);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var o=t.lastRenderedState,u=i(o,n);if(a.hasEagerState=!0,a.eagerState=u,Gn(u,o))return zr(e,t,a,0),null===rs&&Pr(),!1}catch(e){}if(null!==(n=Mr(e,t,a,r)))return Ds(n,0,r),Wo(n,t,r),!0}return!1}function $o(e,t,n,r){if(r={lane:2,revertLane:zc(),action:r,hasEagerState:!1,eagerState:null,next:null},Bo(e)){if(t)throw Error(o(479))}else null!==(t=Mr(e,n,r,2))&&Ds(t,0,2)}function Bo(e){var t=e.alternate;return e===bi||null!==t&&t===bi}function Ho(e,t){xi=ki=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Wo(e,t,n){if(4194048&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Te(e,n)}}var Vo={readContext:Ca,use:$i,useCallback:zi,useContext:zi,useEffect:zi,useImperativeHandle:zi,useLayoutEffect:zi,useInsertionEffect:zi,useMemo:zi,useReducer:zi,useRef:zi,useState:zi,useDebugValue:zi,useDeferredValue:zi,useTransition:zi,useSyncExternalStore:zi,useId:zi,useHostTransitionStatus:zi,useFormState:zi,useActionState:zi,useOptimistic:zi,useMemoCache:zi,useCacheRefresh:zi},qo={readContext:Ca,use:$i,useCallback:function(e,t){return ji().memoizedState=[e,void 0===t?null:t],e},useContext:Ca,useEffect:yo,useImperativeHandle:function(e,t,n){n=null!=n?n.concat([e]):null,vo(4194308,4,ko.bind(null,t,e),n)},useLayoutEffect:function(e,t){return vo(4194308,4,e,t)},useInsertionEffect:function(e,t){vo(4,2,e,t)},useMemo:function(e,t){var n=ji();t=void 0===t?null:t;var r=e();if(Si){de(!0);try{e()}finally{de(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=ji();if(void 0!==n){var a=n(t);if(Si){de(!0);try{n(t)}finally{de(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=jo.bind(null,bi,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},ji().memoizedState=e},useState:function(e){var t=(e=Ji(e)).queue,n=Uo.bind(null,bi,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:So,useDeferredValue:function(e,t){return To(ji(),e,t)},useTransition:function(){var e=Ji(!1);return e=zo.bind(null,bi,e.queue,!0,!1),ji().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=bi,a=ji();if(ia){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===rs)throw Error(o(349));124&is||Yi(r,t,n)}a.memoizedState=n;var i={value:n,getSnapshot:t};return a.queue=i,yo(Ki.bind(null,r,i,e),[e]),r.flags|=2048,ho(9,{destroy:void 0,resource:void 0},Gi.bind(null,r,i,n,t),null),n},useId:function(){var e=ji(),t=rs.identifierPrefix;if(ia){var n=Zr;t="«"+t+"R"+(n=(Xr&~(1<<32-pe(Xr)-1)).toString(32)+n),0<(n=Ei++)&&(t+="H"+n.toString(32)),t+="»"}else t="«"+t+"r"+(n=Pi++).toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Lo,useFormState:lo,useActionState:lo,useOptimistic:function(e){var t=ji();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=$o.bind(null,bi,!0,n),n.dispatch=t,[e,t]},useMemoCache:Bi,useCacheRefresh:function(){return ji().memoizedState=Ro.bind(null,bi)}},Qo={readContext:Ca,use:$i,useCallback:Eo,useContext:Ca,useEffect:bo,useImperativeHandle:xo,useInsertionEffect:wo,useLayoutEffect:_o,useMemo:Co,useReducer:Wi,useRef:go,useState:function(){return Wi(Hi)},useDebugValue:So,useDeferredValue:function(e,t){return Po(Ui(),wi.memoizedState,e,t)},useTransition:function(){var e=Wi(Hi)[0],t=Ui().memoizedState;return["boolean"==typeof e?e:Ii(e),t]},useSyncExternalStore:Qi,useId:Fo,useHostTransitionStatus:Lo,useFormState:so,useActionState:so,useOptimistic:function(e,t){return eo(Ui(),0,e,t)},useMemoCache:Bi,useCacheRefresh:Do},Yo={readContext:Ca,use:$i,useCallback:Eo,useContext:Ca,useEffect:bo,useImperativeHandle:xo,useInsertionEffect:wo,useLayoutEffect:_o,useMemo:Co,useReducer:qi,useRef:go,useState:function(){return qi(Hi)},useDebugValue:So,useDeferredValue:function(e,t){var n=Ui();return null===wi?To(n,e,t):Po(n,wi.memoizedState,e,t)},useTransition:function(){var e=qi(Hi)[0],t=Ui().memoizedState;return["boolean"==typeof e?e:Ii(e),t]},useSyncExternalStore:Qi,useId:Fo,useHostTransitionStatus:Lo,useFormState:po,useActionState:po,useOptimistic:function(e,t){var n=Ui();return null!==wi?eo(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Bi,useCacheRefresh:Do},Go=null,Ko=0;function Xo(e){var t=Ko;return Ko+=1,null===Go&&(Go=[]),Xa(Go,e,t)}function Zo(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Jo(e,t){if(t.$$typeof===p)throw Error(o(525));throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function eu(e){return(0,e._init)(e._payload)}function tu(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=jr(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function u(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Br(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function s(e,t,n,r){var i=n.type;return i===v?f(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===i||"object"==typeof i&&null!==i&&i.$$typeof===C&&eu(i)===t.type)?(Zo(t=a(t,n.props),n),t.return=e,t):(Zo(t=Ir(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Hr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function f(e,t,n,r,i){return null===t||7!==t.tag?((t=$r(n,e.mode,r,i)).return=e,t):((t=a(t,n)).return=e,t)}function d(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=Br(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case h:return Zo(n=Ir(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case g:return(t=Hr(t,e.mode,n)).return=e,t;case C:return d(e,t=(0,t._init)(t._payload),n)}if(O(t)||M(t))return(t=$r(t,e.mode,n,null)).return=e,t;if("function"==typeof t.then)return d(e,Xo(t),n);if(t.$$typeof===_)return d(e,Ta(e,t),n);Jo(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==a?null:l(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case h:return n.key===a?s(e,t,n,r):null;case g:return n.key===a?c(e,t,n,r):null;case C:return p(e,t,n=(a=n._init)(n._payload),r)}if(O(n)||M(n))return null!==a?null:f(e,t,n,r,null);if("function"==typeof n.then)return p(e,t,Xo(n),r);if(n.$$typeof===_)return p(e,t,Ta(e,n),r);Jo(e,n)}return null}function m(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return l(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case h:return s(t,e=e.get(null===r.key?n:r.key)||null,r,a);case g:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case C:return m(e,t,n,r=(0,r._init)(r._payload),a)}if(O(r)||M(r))return f(t,e=e.get(n)||null,r,a,null);if("function"==typeof r.then)return m(e,t,n,Xo(r),a);if(r.$$typeof===_)return m(e,t,n,Ta(t,r),a);Jo(t,r)}return null}function y(l,s,c,f){if("object"==typeof c&&null!==c&&c.type===v&&null===c.key&&(c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case h:e:{for(var b=c.key;null!==s;){if(s.key===b){if((b=c.type)===v){if(7===s.tag){n(l,s.sibling),(f=a(s,c.props.children)).return=l,l=f;break e}}else if(s.elementType===b||"object"==typeof b&&null!==b&&b.$$typeof===C&&eu(b)===s.type){n(l,s.sibling),Zo(f=a(s,c.props),c),f.return=l,l=f;break e}n(l,s);break}t(l,s),s=s.sibling}c.type===v?((f=$r(c.props.children,l.mode,f,c.key)).return=l,l=f):(Zo(f=Ir(c.type,c.key,c.props,null,l.mode,f),c),f.return=l,l=f)}return u(l);case g:e:{for(b=c.key;null!==s;){if(s.key===b){if(4===s.tag&&s.stateNode.containerInfo===c.containerInfo&&s.stateNode.implementation===c.implementation){n(l,s.sibling),(f=a(s,c.children||[])).return=l,l=f;break e}n(l,s);break}t(l,s),s=s.sibling}(f=Hr(c,l.mode,f)).return=l,l=f}return u(l);case C:return y(l,s,c=(b=c._init)(c._payload),f)}if(O(c))return function(a,o,u,l){for(var s=null,c=null,f=o,h=o=0,g=null;null!==f&&h<u.length;h++){f.index>h?(g=f,f=null):g=f.sibling;var v=p(a,f,u[h],l);if(null===v){null===f&&(f=g);break}e&&f&&null===v.alternate&&t(a,f),o=i(v,o,h),null===c?s=v:c.sibling=v,c=v,f=g}if(h===u.length)return n(a,f),ia&&Jr(a,h),s;if(null===f){for(;h<u.length;h++)null!==(f=d(a,u[h],l))&&(o=i(f,o,h),null===c?s=f:c.sibling=f,c=f);return ia&&Jr(a,h),s}for(f=r(f);h<u.length;h++)null!==(g=m(f,a,h,u[h],l))&&(e&&null!==g.alternate&&f.delete(null===g.key?h:g.key),o=i(g,o,h),null===c?s=g:c.sibling=g,c=g);return e&&f.forEach((function(e){return t(a,e)})),ia&&Jr(a,h),s}(l,s,c,f);if(M(c)){if("function"!=typeof(b=M(c)))throw Error(o(150));return function(a,u,l,s){if(null==l)throw Error(o(151));for(var c=null,f=null,h=u,g=u=0,v=null,y=l.next();null!==h&&!y.done;g++,y=l.next()){h.index>g?(v=h,h=null):v=h.sibling;var b=p(a,h,y.value,s);if(null===b){null===h&&(h=v);break}e&&h&&null===b.alternate&&t(a,h),u=i(b,u,g),null===f?c=b:f.sibling=b,f=b,h=v}if(y.done)return n(a,h),ia&&Jr(a,g),c;if(null===h){for(;!y.done;g++,y=l.next())null!==(y=d(a,y.value,s))&&(u=i(y,u,g),null===f?c=y:f.sibling=y,f=y);return ia&&Jr(a,g),c}for(h=r(h);!y.done;g++,y=l.next())null!==(y=m(h,a,g,y.value,s))&&(e&&null!==y.alternate&&h.delete(null===y.key?g:y.key),u=i(y,u,g),null===f?c=y:f.sibling=y,f=y);return e&&h.forEach((function(e){return t(a,e)})),ia&&Jr(a,g),c}(l,s,c=b.call(c),f)}if("function"==typeof c.then)return y(l,s,Xo(c),f);if(c.$$typeof===_)return y(l,s,Ta(l,c),f);Jo(l,c)}return"string"==typeof c&&""!==c||"number"==typeof c||"bigint"==typeof c?(c=""+c,null!==s&&6===s.tag?(n(l,s.sibling),(f=a(s,c)).return=l,l=f):(n(l,s),(f=Br(c,l.mode,f)).return=l,l=f),u(l)):n(l,s)}return function(e,t,n,r){try{Ko=0;var a=y(e,t,n,r);return Go=null,a}catch(t){if(t===Va||t===Qa)throw t;var i=Dr(29,t,null,e.mode);return i.lanes=r,i.return=e,i}}}var nu=tu(!0),ru=tu(!1),au=U(null),iu=null;function ou(e){var t=e.alternate;$(cu,1&cu.current),$(au,e),null===iu&&(null===t||null!==pi.current||null!==t.memoizedState)&&(iu=e)}function uu(e){if(22===e.tag){if($(cu,cu.current),$(au,e),null===iu){var t=e.alternate;null!==t&&null!==t.memoizedState&&(iu=e)}}else lu()}function lu(){$(cu,cu.current),$(au,au.current)}function su(e){I(au),iu===e&&(iu=null),I(cu)}var cu=U(0);function fu(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||mf(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function du(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:d({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var pu={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ls(),a=ai(r);a.payload=t,null!=n&&(a.callback=n),null!==(t=ii(e,a,r))&&(Ds(t,0,r),oi(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ls(),a=ai(r);a.tag=1,a.payload=t,null!=n&&(a.callback=n),null!==(t=ii(e,a,r))&&(Ds(t,0,r),oi(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ls(),r=ai(n);r.tag=2,null!=t&&(r.callback=t),null!==(t=ii(e,r,n))&&(Ds(t,0,n),oi(t,e,n))}};function hu(e,t,n,r,a,i,o){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,i,o):!(t.prototype&&t.prototype.isPureReactComponent&&Kn(n,r)&&Kn(a,i))}function gu(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&pu.enqueueReplaceState(t,t.state,null)}function vu(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=d({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var mu="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function yu(e){mu(e)}function bu(e){console.error(e)}function wu(e){mu(e)}function _u(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(e){setTimeout((function(){throw e}))}}function ku(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(e){setTimeout((function(){throw e}))}}function xu(e,t,n){return(n=ai(n)).tag=3,n.payload={element:null},n.callback=function(){_u(e,t)},n}function Su(e){return(e=ai(e)).tag=3,e}function Eu(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"==typeof a){var i=r.value;e.payload=function(){return a(i)},e.callback=function(){ku(t,n,r)}}var o=n.stateNode;null!==o&&"function"==typeof o.componentDidCatch&&(e.callback=function(){ku(t,n,r),"function"!=typeof a&&(null===Ss?Ss=new Set([this]):Ss.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Cu=Error(o(461)),Tu=!1;function Pu(e,t,n,r){t.child=null===e?ru(t,null,n,r):nu(t,e.child,n,r)}function zu(e,t,n,r,a){n=n.render;var i=t.ref;if("ref"in r){var o={};for(var u in r)"ref"!==u&&(o[u]=r[u])}else o=r;return Ea(t),r=Ni(e,t,n,o,i,a),u=Fi(),null===e||Tu?(ia&&u&&ta(t),t.flags|=1,Pu(e,t,r,a),t.child):(Di(e,t,a),Gu(e,t,a))}function Mu(e,t,n,r,a){if(null===e){var i=n.type;return"function"!=typeof i||Rr(i)||void 0!==i.defaultProps||null!==n.compare?((e=Ir(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,Nu(e,t,i,r,a))}if(i=e.child,!Ku(e,a)){var o=i.memoizedProps;if((n=null!==(n=n.compare)?n:Kn)(o,r)&&e.ref===t.ref)return Gu(e,t,a)}return t.flags|=1,(e=jr(i,r)).ref=t.ref,e.return=t,t.child=e}function Nu(e,t,n,r,a){if(null!==e){var i=e.memoizedProps;if(Kn(i,r)&&e.ref===t.ref){if(Tu=!1,t.pendingProps=r=i,!Ku(e,a))return t.lanes=e.lanes,Gu(e,t,a);131072&e.flags&&(Tu=!0)}}return Fu(e,t,n,r,a)}function Au(e,t,n){var r=t.pendingProps,a=r.children,i=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(128&t.flags){if(r=null!==i?i.baseLanes|n:n,null!==e){for(a=t.child=e.child,i=0;null!==a;)i=i|a.lanes|a.childLanes,a=a.sibling;t.childLanes=i&~r}else t.childLanes=0,t.child=null;return Ou(e,t,r,n)}if(!(536870912&n))return t.lanes=t.childLanes=536870912,Ou(e,t,null!==i?i.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Ha(0,null!==i?i.cachePool:null),null!==i?gi(t,i):vi(),uu(t)}else null!==i?(Ha(0,i.cachePool),gi(t,i),lu(),t.memoizedState=null):(null!==e&&Ha(0,null),vi(),lu());return Pu(e,t,a,n),t.child}function Ou(e,t,n,r){var a=Ba();return a=null===a?null:{parent:Aa._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&Ha(0,null),vi(),uu(t),null!==e&&xa(e,t,r,!0),null}function Lu(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(o(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Fu(e,t,n,r,a){return Ea(t),n=Ni(e,t,n,r,void 0,a),r=Fi(),null===e||Tu?(ia&&r&&ta(t),t.flags|=1,Pu(e,t,n,a),t.child):(Di(e,t,a),Gu(e,t,a))}function Du(e,t,n,r,a,i){return Ea(t),t.updateQueue=null,n=Oi(t,r,n,a),Ai(e),r=Fi(),null===e||Tu?(ia&&r&&ta(t),t.flags|=1,Pu(e,t,n,i),t.child):(Di(e,t,i),Gu(e,t,i))}function Ru(e,t,n,r,a){if(Ea(t),null===t.stateNode){var i=Lr,o=n.contextType;"object"==typeof o&&null!==o&&(i=Ca(o)),i=new n(r,i),t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,i.updater=pu,t.stateNode=i,i._reactInternals=t,(i=t.stateNode).props=r,i.state=t.memoizedState,i.refs={},ni(t),o=n.contextType,i.context="object"==typeof o&&null!==o?Ca(o):Lr,i.state=t.memoizedState,"function"==typeof(o=n.getDerivedStateFromProps)&&(du(t,n,o,r),i.state=t.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof i.getSnapshotBeforeUpdate||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||(o=i.state,"function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),o!==i.state&&pu.enqueueReplaceState(i,i.state,null),ci(t,r,i,a),si(),i.state=t.memoizedState),"function"==typeof i.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){i=t.stateNode;var u=t.memoizedProps,l=vu(n,u);i.props=l;var s=i.context,c=n.contextType;o=Lr,"object"==typeof c&&null!==c&&(o=Ca(c));var f=n.getDerivedStateFromProps;c="function"==typeof f||"function"==typeof i.getSnapshotBeforeUpdate,u=t.pendingProps!==u,c||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(u||s!==o)&&gu(t,i,r,o),ti=!1;var d=t.memoizedState;i.state=d,ci(t,r,i,a),si(),s=t.memoizedState,u||d!==s||ti?("function"==typeof f&&(du(t,n,f,r),s=t.memoizedState),(l=ti||hu(t,n,l,r,d,s,o))?(c||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||("function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"==typeof i.componentDidMount&&(t.flags|=4194308)):("function"==typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),i.props=r,i.state=s,i.context=o,r=l):("function"==typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,ri(e,t),c=vu(n,o=t.memoizedProps),i.props=c,f=t.pendingProps,d=i.context,s=n.contextType,l=Lr,"object"==typeof s&&null!==s&&(l=Ca(s)),(s="function"==typeof(u=n.getDerivedStateFromProps)||"function"==typeof i.getSnapshotBeforeUpdate)||"function"!=typeof i.UNSAFE_componentWillReceiveProps&&"function"!=typeof i.componentWillReceiveProps||(o!==f||d!==l)&&gu(t,i,r,l),ti=!1,d=t.memoizedState,i.state=d,ci(t,r,i,a),si();var p=t.memoizedState;o!==f||d!==p||ti||null!==e&&null!==e.dependencies&&Sa(e.dependencies)?("function"==typeof u&&(du(t,n,u,r),p=t.memoizedState),(c=ti||hu(t,n,c,r,d,p,l)||null!==e&&null!==e.dependencies&&Sa(e.dependencies))?(s||"function"!=typeof i.UNSAFE_componentWillUpdate&&"function"!=typeof i.componentWillUpdate||("function"==typeof i.componentWillUpdate&&i.componentWillUpdate(r,p,l),"function"==typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,p,l)),"function"==typeof i.componentDidUpdate&&(t.flags|=4),"function"==typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof i.componentDidUpdate||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),i.props=r,i.state=p,i.context=l,r=c):("function"!=typeof i.componentDidUpdate||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof i.getSnapshotBeforeUpdate||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return i=r,Lu(e,t),r=!!(128&t.flags),i||r?(i=t.stateNode,n=r&&"function"!=typeof n.getDerivedStateFromError?null:i.render(),t.flags|=1,null!==e&&r?(t.child=nu(t,e.child,null,a),t.child=nu(t,null,n,a)):Pu(e,t,n,a),t.memoizedState=i.state,e=t.child):e=Gu(e,t,a),e}function ju(e,t,n,r){return pa(),t.flags|=256,Pu(e,t,n,r),t.child}var Uu={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Iu(e){return{baseLanes:e,cachePool:Wa()}}function $u(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=vs),e}function Bu(e,t,n){var r,a=t.pendingProps,i=!1,u=!!(128&t.flags);if((r=u)||(r=(null===e||null!==e.memoizedState)&&!!(2&cu.current)),r&&(i=!0,t.flags&=-129),r=!!(32&t.flags),t.flags&=-33,null===e){if(ia){if(i?ou(t):lu(),ia){var l,s=aa;if(l=s){e:{for(l=s,s=ua;8!==l.nodeType;){if(!s){s=null;break e}if(null===(l=yf(l.nextSibling))){s=null;break e}}s=l}null!==s?(t.memoizedState={dehydrated:s,treeContext:null!==Kr?{id:Xr,overflow:Zr}:null,retryLane:536870912,hydrationErrors:null},(l=Dr(18,null,null,0)).stateNode=s,l.return=t,t.child=l,ra=t,aa=null,l=!0):l=!1}l||sa(t)}if(null!==(s=t.memoizedState)&&null!==(s=s.dehydrated))return mf(s)?t.lanes=32:t.lanes=536870912,null;su(t)}return s=a.children,a=a.fallback,i?(lu(),s=Wu({mode:"hidden",children:s},i=t.mode),a=$r(a,i,n,null),s.return=t,a.return=t,s.sibling=a,t.child=s,(i=t.child).memoizedState=Iu(n),i.childLanes=$u(e,r,n),t.memoizedState=Uu,a):(ou(t),Hu(t,s))}if(null!==(l=e.memoizedState)&&null!==(s=l.dehydrated)){if(u)256&t.flags?(ou(t),t.flags&=-257,t=Vu(e,t,n)):null!==t.memoizedState?(lu(),t.child=e.child,t.flags|=128,t=null):(lu(),i=a.fallback,s=t.mode,a=Wu({mode:"visible",children:a.children},s),(i=$r(i,s,n,null)).flags|=2,a.return=t,i.return=t,a.sibling=i,t.child=a,nu(t,e.child,null,n),(a=t.child).memoizedState=Iu(n),a.childLanes=$u(e,r,n),t.memoizedState=Uu,t=i);else if(ou(t),mf(s)){if(r=s.nextSibling&&s.nextSibling.dataset)var c=r.dgst;r=c,(a=Error(o(419))).stack="",a.digest=r,ga({value:a,source:null,stack:null}),t=Vu(e,t,n)}else if(Tu||xa(e,t,n,!1),r=!!(n&e.childLanes),Tu||r){if(null!==(r=rs)&&0!==(a=(a=42&(a=n&-n)?1:Pe(a))&(r.suspendedLanes|n)?0:a)&&a!==l.retryLane)throw l.retryLane=a,Nr(e,a),Ds(r,0,a),Cu;"$?"===s.data||Qs(),t=Vu(e,t,n)}else"$?"===s.data?(t.flags|=192,t.child=e.child,t=null):(e=l.treeContext,aa=yf(s.nextSibling),ra=t,ia=!0,oa=null,ua=!1,null!==e&&(Yr[Gr++]=Xr,Yr[Gr++]=Zr,Yr[Gr++]=Kr,Xr=e.id,Zr=e.overflow,Kr=t),(t=Hu(t,a.children)).flags|=4096);return t}return i?(lu(),i=a.fallback,s=t.mode,c=(l=e.child).sibling,(a=jr(l,{mode:"hidden",children:a.children})).subtreeFlags=65011712&l.subtreeFlags,null!==c?i=jr(c,i):(i=$r(i,s,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,null===(s=e.child.memoizedState)?s=Iu(n):(null!==(l=s.cachePool)?(c=Aa._currentValue,l=l.parent!==c?{parent:c,pool:c}:l):l=Wa(),s={baseLanes:s.baseLanes|n,cachePool:l}),i.memoizedState=s,i.childLanes=$u(e,r,n),t.memoizedState=Uu,a):(ou(t),e=(n=e.child).sibling,(n=jr(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Hu(e,t){return(t=Wu({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Wu(e,t){return(e=Dr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Vu(e,t,n){return nu(t,e.child,null,n),(e=Hu(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function qu(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),_a(e.return,t,n)}function Qu(e,t,n,r,a){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=a)}function Yu(e,t,n){var r=t.pendingProps,a=r.revealOrder,i=r.tail;if(Pu(e,t,r.children,n),2&(r=cu.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&qu(e,n,t);else if(19===e.tag)qu(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch($(cu,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===fu(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Qu(t,!1,a,n,i);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===fu(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Qu(t,!0,n,null,i);break;case"together":Qu(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Gu(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),ps|=t.lanes,!(n&t.childLanes)){if(null===e)return null;if(xa(e,t,n,!1),!(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=jr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=jr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ku(e,t){return!!(e.lanes&t)||!(null===(e=e.dependencies)||!Sa(e))}function Xu(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Tu=!0;else{if(!(Ku(e,n)||128&t.flags))return Tu=!1,function(e,t,n){switch(t.tag){case 3:q(t,t.stateNode.containerInfo),ba(0,Aa,e.memoizedState.cache),pa();break;case 27:case 5:Y(t);break;case 4:q(t,t.stateNode.containerInfo);break;case 10:ba(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(ou(t),t.flags|=128,null):n&t.child.childLanes?Bu(e,t,n):(ou(t),null!==(e=Gu(e,t,n))?e.sibling:null);ou(t);break;case 19:var a=!!(128&e.flags);if((r=!!(n&t.childLanes))||(xa(e,t,n,!1),r=!!(n&t.childLanes)),a){if(r)return Yu(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),$(cu,cu.current),r)break;return null;case 22:case 23:return t.lanes=0,Au(e,t,n);case 24:ba(0,Aa,e.memoizedState.cache)}return Gu(e,t,n)}(e,t,n);Tu=!!(131072&e.flags)}else Tu=!1,ia&&1048576&t.flags&&ea(t,Qr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!=typeof r){if(null!=r){if((a=r.$$typeof)===k){t.tag=11,t=zu(null,t,r,e,n);break e}if(a===E){t.tag=14,t=Mu(null,t,r,e,n);break e}}throw t=A(r)||r,Error(o(306,t,""))}Rr(r)?(e=vu(r,e),t.tag=1,t=Ru(null,t,r,e,n)):(t.tag=0,t=Fu(null,t,r,e,n))}return t;case 0:return Fu(e,t,t.type,t.pendingProps,n);case 1:return Ru(e,t,r=t.type,a=vu(r,t.pendingProps),n);case 3:e:{if(q(t,t.stateNode.containerInfo),null===e)throw Error(o(387));r=t.pendingProps;var i=t.memoizedState;a=i.element,ri(e,t),ci(t,r,null,n);var u=t.memoizedState;if(r=u.cache,ba(0,Aa,r),r!==i.cache&&ka(t,[Aa],n,!0),si(),r=u.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:u.cache},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=ju(e,t,r,n);break e}if(r!==a){ga(a=Sr(Error(o(424)),t)),t=ju(e,t,r,n);break e}for(e=9===(e=t.stateNode.containerInfo).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,aa=yf(e.firstChild),ra=t,ia=!0,oa=null,ua=!0,n=ru(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pa(),r===a){t=Gu(e,t,n);break e}Pu(e,t,r,n)}t=t.child}return t;case 26:return Lu(e,t),null===e?(n=zf(t.type,null,t.pendingProps,null))?t.memoizedState=n:ia||(n=t.type,e=t.pendingProps,(r=rf(W.current).createElement(n))[Ae]=t,r[Oe]=e,ef(r,n,e),Ve(r),t.stateNode=r):t.memoizedState=zf(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Y(t),null===e&&ia&&(r=t.stateNode=_f(t.type,t.pendingProps,W.current),ra=t,ua=!0,a=aa,hf(t.type)?(bf=a,aa=yf(r.firstChild)):aa=a),Pu(e,t,t.pendingProps.children,n),Lu(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&ia&&((a=r=aa)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Ue])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(i=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(i!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((i=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&i&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var i=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===i)return e}if(null===(e=yf(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,ua))?(t.stateNode=r,ra=t,aa=yf(r.firstChild),ua=!1,a=!0):a=!1),a||sa(t)),Y(t),a=t.type,i=t.pendingProps,u=null!==e?e.memoizedProps:null,r=i.children,uf(a,i)?r=null:null!==u&&uf(a,u)&&(t.flags|=32),null!==t.memoizedState&&(a=Ni(e,t,Li,null,null,n),Gf._currentValue=a),Lu(e,t),Pu(e,t,r,n),t.child;case 6:return null===e&&ia&&((e=n=aa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yf(e.nextSibling)))return null}return e}(n,t.pendingProps,ua))?(t.stateNode=n,ra=t,aa=null,e=!0):e=!1),e||sa(t)),null;case 13:return Bu(e,t,n);case 4:return q(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=nu(t,null,r,n):Pu(e,t,r,n),t.child;case 11:return zu(e,t,t.type,t.pendingProps,n);case 7:return Pu(e,t,t.pendingProps,n),t.child;case 8:case 12:return Pu(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,ba(0,t.type,r.value),Pu(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,Ea(t),r=r(a=Ca(a)),t.flags|=1,Pu(e,t,r,n),t.child;case 14:return Mu(e,t,t.type,t.pendingProps,n);case 15:return Nu(e,t,t.type,t.pendingProps,n);case 19:return Yu(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Wu(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=jr(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Au(e,t,n);case 24:return Ea(t),r=Ca(Aa),null===e?(null===(a=Ba())&&(a=rs,i=Oa(),a.pooledCache=i,i.refCount++,null!==i&&(a.pooledCacheLanes|=n),a=i),t.memoizedState={parent:r,cache:a},ni(t),ba(0,Aa,a)):(!!(e.lanes&n)&&(ri(e,t),ci(t,null,null,n),si()),a=e.memoizedState,i=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),ba(0,Aa,r)):(r=i.cache,ba(0,Aa,r),r!==a.cache&&ka(t,[Aa],n,!0))),Pu(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function Zu(e){e.flags|=4}function Ju(e,t){if("stylesheet"!==t.type||4&t.state.loading)e.flags&=-16777217;else if(e.flags|=16777216,!Bf(t)){if(null!==(t=au.current)&&((4194048&is)===is?null!==iu:(62914560&is)!==is&&!(536870912&is)||t!==iu))throw Za=Ya,qa;e.flags|=8192}}function el(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?xe():536870912,e.lanes|=t,ms|=t)}function tl(e,t){if(!ia)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function nl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rl(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return nl(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),wa(Aa),Q(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(da(t)?Zu(t):null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,ha())),nl(t),null;case 26:return n=t.memoizedState,null===e?(Zu(t),null!==n?(nl(t),Ju(t,n)):(nl(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Zu(t),nl(t),Ju(t,n)):(nl(t),t.flags&=-16777217):(e.memoizedProps!==r&&Zu(t),nl(t),t.flags&=-16777217),null;case 27:G(t),n=W.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Zu(t);else{if(!r){if(null===t.stateNode)throw Error(o(166));return nl(t),null}e=B.current,da(t)?ca(t):(e=_f(a,r,n),t.stateNode=e,Zu(t))}return nl(t),null;case 5:if(G(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Zu(t);else{if(!r){if(null===t.stateNode)throw Error(o(166));return nl(t),null}if(e=B.current,da(t))ca(t);else{switch(a=rf(W.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"==typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"==typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[Ae]=t,e[Oe]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(ef(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Zu(t)}}return nl(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Zu(t);else{if("string"!=typeof r&&null===t.stateNode)throw Error(o(166));if(e=W.current,da(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[Ae]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Kc(e.nodeValue,n)))||sa(t)}else(e=rf(e).createTextNode(r))[Ae]=t,t.stateNode=e}return nl(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=da(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(o(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(o(317));a[Ae]=t}else pa(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;nl(t),a=!1}else a=ha(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(su(t),t):(su(t),null)}if(su(t),128&t.flags)return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var i=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(i=r.memoizedState.cachePool.pool),i!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),el(t,t.updateQueue),nl(t),null;case 4:return Q(),null===e&&Ic(t.stateNode.containerInfo),nl(t),null;case 10:return wa(t.type),nl(t),null;case 19:if(I(cu),null===(a=t.memoizedState))return nl(t),null;if(r=!!(128&t.flags),null===(i=a.rendering))if(r)tl(a,!1);else{if(0!==ds||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(i=fu(e))){for(t.flags|=128,tl(a,!1),e=i.updateQueue,t.updateQueue=e,el(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Ur(n,e),n=n.sibling;return $(cu,1&cu.current|2),t.child}e=e.sibling}null!==a.tail&&te()>ks&&(t.flags|=128,r=!0,tl(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=fu(i))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,el(t,e),tl(a,!0),null===a.tail&&"hidden"===a.tailMode&&!i.alternate&&!ia)return nl(t),null}else 2*te()-a.renderingStartTime>ks&&536870912!==n&&(t.flags|=128,r=!0,tl(a,!1),t.lanes=4194304);a.isBackwards?(i.sibling=t.child,t.child=i):(null!==(e=a.last)?e.sibling=i:t.child=i,a.last=i)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=te(),t.sibling=null,e=cu.current,$(cu,r?1&e|2:1&e),t):(nl(t),null);case 22:case 23:return su(t),mi(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?!!(536870912&n)&&!(128&t.flags)&&(nl(t),6&t.subtreeFlags&&(t.flags|=8192)):nl(t),null!==(n=t.updateQueue)&&el(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&I($a),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),wa(Aa),nl(t),null;case 25:case 30:return null}throw Error(o(156,t.tag))}function al(e,t){switch(na(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return wa(Aa),Q(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return G(t),null;case 13:if(su(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));pa()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return I(cu),null;case 4:return Q(),null;case 10:return wa(t.type),null;case 22:case 23:return su(t),mi(),null!==e&&I($a),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return wa(Aa),null;default:return null}}function il(e,t){switch(na(t),t.tag){case 3:wa(Aa),Q();break;case 26:case 27:case 5:G(t);break;case 4:Q();break;case 13:su(t);break;case 19:I(cu);break;case 10:wa(t.type);break;case 22:case 23:su(t),mi(),null!==e&&I($a);break;case 24:wa(Aa)}}function ol(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var i=n.create,o=n.inst;r=i(),o.destroy=r}n=n.next}while(n!==a)}}catch(e){cc(t,t.return,e)}}function ul(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var i=a.next;r=i;do{if((r.tag&e)===e){var o=r.inst,u=o.destroy;if(void 0!==u){o.destroy=void 0,a=t;var l=n,s=u;try{s()}catch(e){cc(a,l,e)}}}r=r.next}while(r!==i)}}catch(e){cc(t,t.return,e)}}function ll(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{di(t,n)}catch(t){cc(e,e.return,t)}}}function sl(e,t,n){n.props=vu(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(n){cc(e,t,n)}}function cl(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"==typeof n?e.refCleanup=n(r):n.current=r}}catch(n){cc(e,t,n)}}function fl(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"==typeof r)try{r()}catch(n){cc(e,t,n)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(n){cc(e,t,n)}else n.current=null}function dl(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(t){cc(e,e.return,t)}}function pl(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,i=null,u=null,l=null,s=null,c=null,f=null;for(h in n){var d=n[h];if(n.hasOwnProperty(h)&&null!=d)switch(h){case"checked":case"value":break;case"defaultValue":s=d;default:r.hasOwnProperty(h)||Zc(e,t,h,null,r,d)}}for(var p in r){var h=r[p];if(d=n[p],r.hasOwnProperty(p)&&(null!=h||null!=d))switch(p){case"type":i=h;break;case"name":a=h;break;case"checked":c=h;break;case"defaultChecked":f=h;break;case"value":u=h;break;case"defaultValue":l=h;break;case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(o(137,t));break;default:h!==d&&Zc(e,t,p,h,r,d)}}return void vt(e,u,l,s,c,f,i,a);case"select":for(i in h=u=l=p=null,n)if(s=n[i],n.hasOwnProperty(i)&&null!=s)switch(i){case"value":break;case"multiple":h=s;default:r.hasOwnProperty(i)||Zc(e,t,i,null,r,s)}for(a in r)if(i=r[a],s=n[a],r.hasOwnProperty(a)&&(null!=i||null!=s))switch(a){case"value":p=i;break;case"defaultValue":l=i;break;case"multiple":u=i;default:i!==s&&Zc(e,t,a,i,r,s)}return t=l,n=u,r=h,void(null!=p?bt(e,!!n,p,!1):!!r!=!!n&&(null!=t?bt(e,!!n,t,!0):bt(e,!!n,n?[]:"",!1)));case"textarea":for(l in h=p=null,n)if(a=n[l],n.hasOwnProperty(l)&&null!=a&&!r.hasOwnProperty(l))switch(l){case"value":case"children":break;default:Zc(e,t,l,null,r,a)}for(u in r)if(a=r[u],i=n[u],r.hasOwnProperty(u)&&(null!=a||null!=i))switch(u){case"value":p=a;break;case"defaultValue":h=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(o(91));break;default:a!==i&&Zc(e,t,u,a,r,i)}return void wt(e,p,h);case"option":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&("selected"===g?e.selected=!1:Zc(e,t,g,null,r,p));for(s in r)p=r[s],h=n[s],!r.hasOwnProperty(s)||p===h||null==p&&null==h||("selected"===s?e.selected=p&&"function"!=typeof p&&"symbol"!=typeof p:Zc(e,t,s,p,r,h));return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var v in n)p=n[v],n.hasOwnProperty(v)&&null!=p&&!r.hasOwnProperty(v)&&Zc(e,t,v,null,r,p);for(c in r)if(p=r[c],h=n[c],r.hasOwnProperty(c)&&p!==h&&(null!=p||null!=h))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(o(137,t));break;default:Zc(e,t,c,p,r,h)}return;default:if(Ct(t)){for(var m in n)p=n[m],n.hasOwnProperty(m)&&void 0!==p&&!r.hasOwnProperty(m)&&Jc(e,t,m,void 0,r,p);for(f in r)p=r[f],h=n[f],!r.hasOwnProperty(f)||p===h||void 0===p&&void 0===h||Jc(e,t,f,p,r,h);return}}for(var y in n)p=n[y],n.hasOwnProperty(y)&&null!=p&&!r.hasOwnProperty(y)&&Zc(e,t,y,null,r,p);for(d in r)p=r[d],h=n[d],!r.hasOwnProperty(d)||p===h||null==p&&null==h||Zc(e,t,d,p,r,h)}(r,e.type,n,t),r[Oe]=t}catch(t){cc(e,e.return,t)}}function hl(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&hf(e.type)||4===e.tag}function gl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||hl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&hf(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function vl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Xc));else if(4!==r&&(27===r&&hf(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(vl(e,t,n),e=e.sibling;null!==e;)vl(e,t,n),e=e.sibling}function ml(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&hf(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(ml(e,t,n),e=e.sibling;null!==e;)ml(e,t,n),e=e.sibling}function yl(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);ef(t,r,n),t[Ae]=e,t[Oe]=n}catch(t){cc(e,e.return,t)}}var bl=!1,wl=!1,_l=!1,kl="function"==typeof WeakSet?WeakSet:Set,xl=null;function Sl(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Rl(e,n),4&r&&ol(5,n);break;case 1:if(Rl(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(e){cc(n,n.return,e)}else{var a=vu(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(e){cc(n,n.return,e)}}64&r&&ll(n),512&r&&cl(n,n.return);break;case 3:if(Rl(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{di(e,t)}catch(e){cc(n,n.return,e)}}break;case 27:null===t&&4&r&&yl(n);case 26:case 5:Rl(e,n),null===t&&4&r&&dl(n),512&r&&cl(n,n.return);break;case 12:Rl(e,n);break;case 13:Rl(e,n),4&r&&Ml(e,n),64&r&&null!==(e=n.memoizedState)&&null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=hc.bind(null,n));break;case 22:if(!(r=null!==n.memoizedState||bl)){t=null!==t&&null!==t.memoizedState||wl,a=bl;var i=wl;bl=r,(wl=t)&&!i?Ul(e,n,!!(8772&n.subtreeFlags)):Rl(e,n),bl=a,wl=i}break;case 30:break;default:Rl(e,n)}}function El(e){var t=e.alternate;null!==t&&(e.alternate=null,El(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&null!==(t=e.stateNode)&&Ie(t),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Cl=null,Tl=!1;function Pl(e,t,n){for(n=n.child;null!==n;)zl(e,t,n),n=n.sibling}function zl(e,t,n){if(fe&&"function"==typeof fe.onCommitFiberUnmount)try{fe.onCommitFiberUnmount(ce,n)}catch(e){}switch(n.tag){case 26:wl||fl(n,t),Pl(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:wl||fl(n,t);var r=Cl,a=Tl;hf(n.type)&&(Cl=n.stateNode,Tl=!1),Pl(e,t,n),kf(n.stateNode),Cl=r,Tl=a;break;case 5:wl||fl(n,t);case 6:if(r=Cl,a=Tl,Cl=null,Pl(e,t,n),Tl=a,null!==(Cl=r))if(Tl)try{(9===Cl.nodeType?Cl.body:"HTML"===Cl.nodeName?Cl.ownerDocument.body:Cl).removeChild(n.stateNode)}catch(e){cc(n,t,e)}else try{Cl.removeChild(n.stateNode)}catch(e){cc(n,t,e)}break;case 18:null!==Cl&&(Tl?(gf(9===(e=Cl).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Pd(e)):gf(Cl,n.stateNode));break;case 4:r=Cl,a=Tl,Cl=n.stateNode.containerInfo,Tl=!0,Pl(e,t,n),Cl=r,Tl=a;break;case 0:case 11:case 14:case 15:wl||ul(2,n,t),wl||ul(4,n,t),Pl(e,t,n);break;case 1:wl||(fl(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount&&sl(n,t,r)),Pl(e,t,n);break;case 21:Pl(e,t,n);break;case 22:wl=(r=wl)||null!==n.memoizedState,Pl(e,t,n),wl=r;break;default:Pl(e,t,n)}}function Ml(e,t){if(null===t.memoizedState&&null!==(e=t.alternate)&&null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))try{Pd(e)}catch(e){cc(t,t.return,e)}}function Nl(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new kl),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new kl),t;default:throw Error(o(435,e.tag))}}(e);t.forEach((function(t){var r=gc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}function Al(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],i=e,u=t,l=u;e:for(;null!==l;){switch(l.tag){case 27:if(hf(l.type)){Cl=l.stateNode,Tl=!1;break e}break;case 5:Cl=l.stateNode,Tl=!1;break e;case 3:case 4:Cl=l.stateNode.containerInfo,Tl=!0;break e}l=l.return}if(null===Cl)throw Error(o(160));zl(i,u,a),Cl=null,Tl=!1,null!==(i=a.alternate)&&(i.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Ll(t,e),t=t.sibling}var Ol=null;function Ll(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Al(t,e),Fl(e),4&r&&(ul(3,e,e.return),ol(3,e),ul(5,e,e.return));break;case 1:Al(t,e),Fl(e),512&r&&(wl||null===n||fl(n,n.return)),64&r&&bl&&null!==(e=e.updateQueue)&&null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r));break;case 26:var a=Ol;if(Al(t,e),Fl(e),512&r&&(wl||null===n||fl(n,n.return)),4&r){var i=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(i=a.getElementsByTagName("title")[0])||i[Ue]||i[Ae]||"http://www.w3.org/2000/svg"===i.namespaceURI||i.hasAttribute("itemprop"))&&(i=a.createElement(r),a.head.insertBefore(i,a.querySelector("head > title"))),ef(i,r,n),i[Ae]=e,Ve(i),r=i;break e;case"link":var u=If("link","href",a).get(r+(n.href||""));if(u)for(var l=0;l<u.length;l++)if((i=u[l]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&i.getAttribute("rel")===(null==n.rel?null:n.rel)&&i.getAttribute("title")===(null==n.title?null:n.title)&&i.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){u.splice(l,1);break t}ef(i=a.createElement(r),r,n),a.head.appendChild(i);break;case"meta":if(u=If("meta","content",a).get(r+(n.content||"")))for(l=0;l<u.length;l++)if((i=u[l]).getAttribute("content")===(null==n.content?null:""+n.content)&&i.getAttribute("name")===(null==n.name?null:n.name)&&i.getAttribute("property")===(null==n.property?null:n.property)&&i.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&i.getAttribute("charset")===(null==n.charSet?null:n.charSet)){u.splice(l,1);break t}ef(i=a.createElement(r),r,n),a.head.appendChild(i);break;default:throw Error(o(468,r))}i[Ae]=e,Ve(i),r=i}e.stateNode=r}else $f(a,e.type,e.stateNode);else e.stateNode=Ff(a,r,e.memoizedProps);else i!==r?(null===i?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):i.count--,null===r?$f(a,e.type,e.stateNode):Ff(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&pl(e,e.memoizedProps,n.memoizedProps)}break;case 27:Al(t,e),Fl(e),512&r&&(wl||null===n||fl(n,n.return)),null!==n&&4&r&&pl(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Al(t,e),Fl(e),512&r&&(wl||null===n||fl(n,n.return)),32&e.flags){a=e.stateNode;try{kt(a,"")}catch(t){cc(e,e.return,t)}}4&r&&null!=e.stateNode&&pl(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(_l=!0);break;case 6:if(Al(t,e),Fl(e),4&r){if(null===e.stateNode)throw Error(o(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(t){cc(e,e.return,t)}}break;case 3:if(Uf=null,a=Ol,Ol=Ef(t.containerInfo),Al(t,e),Ol=a,Fl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Pd(t.containerInfo)}catch(t){cc(e,e.return,t)}_l&&(_l=!1,Dl(e));break;case 4:r=Ol,Ol=Ef(e.stateNode.containerInfo),Al(t,e),Fl(e),Ol=r;break;case 12:default:Al(t,e),Fl(e);break;case 13:Al(t,e),Fl(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==n&&null!==n.memoizedState)&&(_s=te()),4&r&&null!==(r=e.updateQueue)&&(e.updateQueue=null,Nl(e,r));break;case 22:a=null!==e.memoizedState;var s=null!==n&&null!==n.memoizedState,c=bl,f=wl;if(bl=c||a,wl=f||s,Al(t,e),wl=f,bl=c,Fl(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||s||bl||wl||jl(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){s=n=t;try{if(i=s.stateNode,a)"function"==typeof(u=i.style).setProperty?u.setProperty("display","none","important"):u.display="none";else{l=s.stateNode;var d=s.memoizedProps.style,p=null!=d&&d.hasOwnProperty("display")?d.display:null;l.style.display=null==p||"boolean"==typeof p?"":(""+p).trim()}}catch(e){cc(s,s.return,e)}}}else if(6===t.tag){if(null===n){s=t;try{s.stateNode.nodeValue=a?"":s.memoizedProps}catch(e){cc(s,s.return,e)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&null!==(r=e.updateQueue)&&null!==(n=r.retryQueue)&&(r.retryQueue=null,Nl(e,n));break;case 19:Al(t,e),Fl(e),4&r&&null!==(r=e.updateQueue)&&(e.updateQueue=null,Nl(e,r));case 30:case 21:}}function Fl(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(hl(r)){n=r;break}r=r.return}if(null==n)throw Error(o(160));switch(n.tag){case 27:var a=n.stateNode;ml(e,gl(e),a);break;case 5:var i=n.stateNode;32&n.flags&&(kt(i,""),n.flags&=-33),ml(e,gl(e),i);break;case 3:case 4:var u=n.stateNode.containerInfo;vl(e,gl(e),u);break;default:throw Error(o(161))}}catch(t){cc(e,e.return,t)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Dl(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Dl(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Rl(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Sl(e,t.alternate,t),t=t.sibling}function jl(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ul(4,t,t.return),jl(t);break;case 1:fl(t,t.return);var n=t.stateNode;"function"==typeof n.componentWillUnmount&&sl(t,t.return,n),jl(t);break;case 27:kf(t.stateNode);case 26:case 5:fl(t,t.return),jl(t);break;case 22:null===t.memoizedState&&jl(t);break;default:jl(t)}e=e.sibling}}function Ul(e,t,n){for(n=n&&!!(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,i=t,o=i.flags;switch(i.tag){case 0:case 11:case 15:Ul(a,i,n),ol(4,i);break;case 1:if(Ul(a,i,n),"function"==typeof(a=(r=i).stateNode).componentDidMount)try{a.componentDidMount()}catch(e){cc(r,r.return,e)}if(null!==(a=(r=i).updateQueue)){var u=r.stateNode;try{var l=a.shared.hiddenCallbacks;if(null!==l)for(a.shared.hiddenCallbacks=null,a=0;a<l.length;a++)fi(l[a],u)}catch(e){cc(r,r.return,e)}}n&&64&o&&ll(i),cl(i,i.return);break;case 27:yl(i);case 26:case 5:Ul(a,i,n),n&&null===r&&4&o&&dl(i),cl(i,i.return);break;case 12:Ul(a,i,n);break;case 13:Ul(a,i,n),n&&4&o&&Ml(a,i);break;case 22:null===i.memoizedState&&Ul(a,i,n),cl(i,i.return);break;case 30:break;default:Ul(a,i,n)}t=t.sibling}}function Il(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&La(n))}function $l(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&La(e))}function Bl(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Hl(e,t,n,r),t=t.sibling}function Hl(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Bl(e,t,n,r),2048&a&&ol(9,t);break;case 1:case 13:default:Bl(e,t,n,r);break;case 3:Bl(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&La(e)));break;case 12:if(2048&a){Bl(e,t,n,r),e=t.stateNode;try{var i=t.memoizedProps,o=i.id,u=i.onPostCommit;"function"==typeof u&&u(o,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(e){cc(t,t.return,e)}}else Bl(e,t,n,r);break;case 23:break;case 22:i=t.stateNode,o=t.alternate,null!==t.memoizedState?2&i._visibility?Bl(e,t,n,r):Vl(e,t):2&i._visibility?Bl(e,t,n,r):(i._visibility|=2,Wl(e,t,n,r,!!(10256&t.subtreeFlags))),2048&a&&Il(o,t);break;case 24:Bl(e,t,n,r),2048&a&&$l(t.alternate,t)}}function Wl(e,t,n,r,a){for(a=a&&!!(10256&t.subtreeFlags),t=t.child;null!==t;){var i=e,o=t,u=n,l=r,s=o.flags;switch(o.tag){case 0:case 11:case 15:Wl(i,o,u,l,a),ol(8,o);break;case 23:break;case 22:var c=o.stateNode;null!==o.memoizedState?2&c._visibility?Wl(i,o,u,l,a):Vl(i,o):(c._visibility|=2,Wl(i,o,u,l,a)),a&&2048&s&&Il(o.alternate,o);break;case 24:Wl(i,o,u,l,a),a&&2048&s&&$l(o.alternate,o);break;default:Wl(i,o,u,l,a)}t=t.sibling}}function Vl(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:Vl(n,r),2048&a&&Il(r.alternate,r);break;case 24:Vl(n,r),2048&a&&$l(r.alternate,r);break;default:Vl(n,r)}t=t.sibling}}var ql=8192;function Ql(e){if(e.subtreeFlags&ql)for(e=e.child;null!==e;)Yl(e),e=e.sibling}function Yl(e){switch(e.tag){case 26:Ql(e),e.flags&ql&&null!==e.memoizedState&&function(e,t,n){if(null===Hf)throw Error(o(475));var r=Hf;if(!("stylesheet"!==t.type||"string"==typeof n.media&&!1===matchMedia(n.media).matches||4&t.state.loading)){if(null===t.instance){var a=Mf(n.href),i=e.querySelector(Nf(a));if(i)return null!==(e=i._p)&&"object"==typeof e&&"function"==typeof e.then&&(r.count++,r=Vf.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=i,void Ve(i);i=e.ownerDocument||e,n=Af(n),(a=xf.get(a))&&Rf(n,a),Ve(i=i.createElement("link"));var u=i;u._p=new Promise((function(e,t){u.onload=e,u.onerror=t})),ef(i,"link",n),t.instance=i}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&!(3&t.state.loading)&&(r.count++,t=Vf.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Ol,e.memoizedState,e.memoizedProps);break;case 5:default:Ql(e);break;case 3:case 4:var t=Ol;Ol=Ef(e.stateNode.containerInfo),Ql(e),Ol=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=ql,ql=16777216,Ql(e),ql=t):Ql(e))}}function Gl(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Kl(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];xl=r,Jl(r,e)}Gl(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Xl(e),e=e.sibling}function Xl(e){switch(e.tag){case 0:case 11:case 15:Kl(e),2048&e.flags&&ul(9,e,e.return);break;case 3:case 12:default:Kl(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Zl(e)):Kl(e)}}function Zl(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];xl=r,Jl(r,e)}Gl(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:ul(8,t,t.return),Zl(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Zl(t));break;default:Zl(t)}e=e.sibling}}function Jl(e,t){for(;null!==xl;){var n=xl;switch(n.tag){case 0:case 11:case 15:ul(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:La(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,xl=r;else e:for(n=e;null!==xl;){var a=(r=xl).sibling,i=r.return;if(El(r),r===n){xl=null;break e}if(null!==a){a.return=i,xl=a;break e}xl=i}}}var es={getCacheForType:function(e){var t=Ca(Aa),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},ts="function"==typeof WeakMap?WeakMap:Map,ns=0,rs=null,as=null,is=0,os=0,us=null,ls=!1,ss=!1,cs=!1,fs=0,ds=0,ps=0,hs=0,gs=0,vs=0,ms=0,ys=null,bs=null,ws=!1,_s=0,ks=1/0,xs=null,Ss=null,Es=0,Cs=null,Ts=null,Ps=0,zs=0,Ms=null,Ns=null,As=0,Os=null;function Ls(){return 2&ns&&0!==is?is&-is:null!==L.T?0!==Ra?Ra:zc():Me()}function Fs(){0===vs&&(vs=536870912&is&&!ia?536870912:ke());var e=au.current;return null!==e&&(e.flags|=32),vs}function Ds(e,t,n){(e!==rs||2!==os&&9!==os)&&null===e.cancelPendingCommit||(Hs(e,0),Is(e,is,vs,!1)),Ee(e,n),2&ns&&e===rs||(e===rs&&(!(2&ns)&&(hs|=n),4===ds&&Is(e,is,vs,!1)),kc(e))}function Rs(e,t,n){if(6&ns)throw Error(o(327));for(var r=!n&&!(124&t)&&!(t&e.expiredLanes)||we(e,t),a=r?function(e,t){var n=ns;ns|=2;var r=Vs(),a=qs();rs!==e||is!==t?(xs=null,ks=te()+500,Hs(e,t)):ss=we(e,t);e:for(;;)try{if(0!==os&&null!==as){t=as;var i=us;t:switch(os){case 1:os=0,us=null,Js(e,t,i,1);break;case 2:case 9:if(Ga(i)){os=0,us=null,Zs(t);break}t=function(){2!==os&&9!==os||rs!==e||(os=7),kc(e)},i.then(t,t);break e;case 3:os=7;break e;case 4:os=5;break e;case 7:Ga(i)?(os=0,us=null,Zs(t)):(os=0,us=null,Js(e,t,i,7));break;case 5:var u=null;switch(as.tag){case 26:u=as.memoizedState;case 5:case 27:var l=as;if(!u||Bf(u)){os=0,us=null;var s=l.sibling;if(null!==s)as=s;else{var c=l.return;null!==c?(as=c,ec(c)):as=null}break t}}os=0,us=null,Js(e,t,i,5);break;case 6:os=0,us=null,Js(e,t,i,6);break;case 8:Bs(),ds=6;break e;default:throw Error(o(462))}}Ks();break}catch(t){Ws(e,t)}return ya=ma=null,L.H=r,L.A=a,ns=n,null!==as?0:(rs=null,is=0,Pr(),ds)}(e,t):Ys(e,t,!0),i=r;;){if(0===a){ss&&!r&&Is(e,t,0,!1);break}if(n=e.current.alternate,!i||Us(n)){if(2===a){if(i=t,e.errorRecoveryDisabledLanes&i)var u=0;else u=0!=(u=-536870913&e.pendingLanes)?u:536870912&u?536870912:0;if(0!==u){t=u;e:{var l=e;a=ys;var s=l.current.memoizedState.isDehydrated;if(s&&(Hs(l,u).flags|=256),2!==(u=Ys(l,u,!1))){if(cs&&!s){l.errorRecoveryDisabledLanes|=i,hs|=i,a=4;break e}i=bs,bs=a,null!==i&&(null===bs?bs=i:bs.push.apply(bs,i))}a=u}if(i=!1,2!==a)continue}}if(1===a){Hs(e,0),Is(e,t,0,!0);break}e:{switch(r=e,i=a){case 0:case 1:throw Error(o(345));case 4:if((4194048&t)!==t)break;case 6:Is(r,t,vs,!ls);break e;case 2:bs=null;break;case 3:case 5:break;default:throw Error(o(329))}if((62914560&t)===t&&10<(a=_s+300-te())){if(Is(r,t,vs,!ls),0!==be(r,0,!0))break e;r.timeoutHandle=sf(js.bind(null,r,n,bs,xs,ws,t,vs,hs,ms,ls,i,2,-0,0),a)}else js(r,n,bs,xs,ws,t,vs,hs,ms,ls,i,0,-0,0)}break}a=Ys(e,t,!1),i=!1}kc(e)}function js(e,t,n,r,a,i,u,l,s,c,f,d,p,h){if(e.timeoutHandle=-1,(8192&(d=t.subtreeFlags)||!(16785408&~d))&&(Hf={stylesheets:null,count:0,unsuspend:Wf},Yl(t),null!==(d=function(){if(null===Hf)throw Error(o(475));var e=Hf;return e.stylesheets&&0===e.count&&Qf(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&Qf(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=d(nc.bind(null,e,t,i,n,r,a,u,l,s,f,1,p,h)),void Is(e,i,u,!c);nc(e,t,i,n,r,a,u,l,s)}function Us(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&null!==(n=t.updateQueue)&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],i=a.getSnapshot;a=a.value;try{if(!Gn(i(),a))return!1}catch(e){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Is(e,t,n,r){t&=~gs,t&=~hs,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var i=31-pe(a),o=1<<i;r[i]=-1,a&=~o}0!==n&&Ce(e,n,t)}function $s(){return!!(6&ns)||(xc(0,!1),!1)}function Bs(){if(null!==as){if(0===os)var e=as.return;else ya=ma=null,Ri(e=as),Go=null,Ko=0,e=as;for(;null!==e;)il(e.alternate,e),e=e.return;as=null}}function Hs(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,cf(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Bs(),rs=e,as=n=jr(e.current,null),is=t,os=0,us=null,ls=!1,ss=we(e,t),cs=!1,ms=vs=gs=hs=ps=ds=0,bs=ys=null,ws=!1,8&t&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-pe(r),i=1<<a;t|=e[a],r&=~i}return fs=t,Pr(),n}function Ws(e,t){bi=null,L.H=Vo,t===Va||t===Qa?(t=Ja(),os=3):t===qa?(t=Ja(),os=4):os=t===Cu?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,us=t,null===as&&(ds=1,_u(e,Sr(t,e.current)))}function Vs(){var e=L.H;return L.H=Vo,null===e?Vo:e}function qs(){var e=L.A;return L.A=es,e}function Qs(){ds=4,ls||(4194048&is)!==is&&null!==au.current||(ss=!0),!(134217727&ps)&&!(134217727&hs)||null===rs||Is(rs,is,vs,!1)}function Ys(e,t,n){var r=ns;ns|=2;var a=Vs(),i=qs();rs===e&&is===t||(xs=null,Hs(e,t)),t=!1;var o=ds;e:for(;;)try{if(0!==os&&null!==as){var u=as,l=us;switch(os){case 8:Bs(),o=6;break e;case 3:case 2:case 9:case 6:null===au.current&&(t=!0);var s=os;if(os=0,us=null,Js(e,u,l,s),n&&ss){o=0;break e}break;default:s=os,os=0,us=null,Js(e,u,l,s)}}Gs(),o=ds;break}catch(t){Ws(e,t)}return t&&e.shellSuspendCounter++,ya=ma=null,ns=r,L.H=a,L.A=i,null===as&&(rs=null,is=0,Pr()),o}function Gs(){for(;null!==as;)Xs(as)}function Ks(){for(;null!==as&&!J();)Xs(as)}function Xs(e){var t=Xu(e.alternate,e,fs);e.memoizedProps=e.pendingProps,null===t?ec(e):as=t}function Zs(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Du(n,t,t.pendingProps,t.type,void 0,is);break;case 11:t=Du(n,t,t.pendingProps,t.type.render,t.ref,is);break;case 5:Ri(t);default:il(n,t),t=Xu(n,t=as=Ur(t,fs),fs)}e.memoizedProps=e.pendingProps,null===t?ec(e):as=t}function Js(e,t,n,r){ya=ma=null,Ri(t),Go=null,Ko=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"==typeof r&&"function"==typeof r.then){if(null!==(t=n.alternate)&&xa(t,n,a,!0),null!==(n=au.current)){switch(n.tag){case 13:return null===iu?Qs():null===n.alternate&&0===ds&&(ds=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Ya?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),fc(e,r,a)),!1;case 22:return n.flags|=65536,r===Ya?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),fc(e,r,a)),!1}throw Error(o(435,n.tag))}return fc(e,r,a),Qs(),!1}if(ia)return null!==(t=au.current)?(!(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==la&&ga(Sr(e=Error(o(422),{cause:r}),n))):(r!==la&&ga(Sr(t=Error(o(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=Sr(r,n),ui(e,a=xu(e.stateNode,r,a)),4!==ds&&(ds=2)),!1;var i=Error(o(520),{cause:r});if(i=Sr(i,n),null===ys?ys=[i]:ys.push(i),4!==ds&&(ds=2),null===t)return!0;r=Sr(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,ui(n,e=xu(n.stateNode,r,e)),!1;case 1:if(t=n.type,i=n.stateNode,!(128&n.flags||"function"!=typeof t.getDerivedStateFromError&&(null===i||"function"!=typeof i.componentDidCatch||null!==Ss&&Ss.has(i))))return n.flags|=65536,a&=-a,n.lanes|=a,Eu(a=Su(a),e,n,r),ui(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,is))return ds=1,_u(e,Sr(n,e.current)),void(as=null)}catch(t){if(null!==a)throw as=a,t;return ds=1,_u(e,Sr(n,e.current)),void(as=null)}32768&t.flags?(ia||1===r?e=!0:ss||536870912&is?e=!1:(ls=e=!0,(2===r||9===r||3===r||6===r)&&null!==(r=au.current)&&13===r.tag&&(r.flags|=16384)),tc(t,e)):ec(t)}function ec(e){var t=e;do{if(32768&t.flags)return void tc(t,ls);e=t.return;var n=rl(t.alternate,t,fs);if(null!==n)return void(as=n);if(null!==(t=t.sibling))return void(as=t);as=t=e}while(null!==t);0===ds&&(ds=5)}function tc(e,t){do{var n=al(e.alternate,e);if(null!==n)return n.flags&=32767,void(as=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(as=e);as=e=n}while(null!==e);ds=6,as=null}function nc(e,t,n,r,a,i,u,l,s){e.cancelPendingCommit=null;do{uc()}while(0!==Es);if(6&ns)throw Error(o(327));if(null!==t){if(t===e.current)throw Error(o(177));if(i=t.lanes|t.childLanes,function(e,t,n,r,a,i){var o=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var u=e.entanglements,l=e.expirationTimes,s=e.hiddenUpdates;for(n=o&~n;0<n;){var c=31-pe(n),f=1<<c;u[c]=0,l[c]=-1;var d=s[c];if(null!==d)for(s[c]=null,c=0;c<d.length;c++){var p=d[c];null!==p&&(p.lane&=-536870913)}n&=~f}0!==r&&Ce(e,r,0),0!==i&&0===a&&0!==e.tag&&(e.suspendedLanes|=i&~(o&~t))}(e,n,i|=Tr,u,l,s),e===rs&&(as=rs=null,is=0),Ts=t,Cs=e,Ps=n,zs=i,Ms=a,Ns=r,10256&t.subtreeFlags||10256&t.flags?(e.callbackNode=null,e.callbackPriority=0,X(ie,(function(){return lc(),null}))):(e.callbackNode=null,e.callbackPriority=0),r=!!(13878&t.flags),13878&t.subtreeFlags||r){r=L.T,L.T=null,a=F.p,F.p=2,u=ns,ns|=4;try{!function(e,t){if(e=e.containerInfo,tf=rd,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(e){n=null;break e}var u=0,l=-1,s=-1,c=0,f=0,d=e,p=null;t:for(;;){for(var h;d!==n||0!==a&&3!==d.nodeType||(l=u+a),d!==i||0!==r&&3!==d.nodeType||(s=u+r),3===d.nodeType&&(u+=d.nodeValue.length),null!==(h=d.firstChild);)p=d,d=h;for(;;){if(d===e)break t;if(p===n&&++c===a&&(l=u),p===i&&++f===r&&(s=u),null!==(h=d.nextSibling))break;p=(d=p).parentNode}d=h}n=-1===l||-1===s?null:{start:l,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(nf={focusedElem:e,selectionRange:n},rd=!1,xl=t;null!==xl;)if(e=(t=xl).child,1024&t.subtreeFlags&&null!==e)e.return=t,xl=e;else for(;null!==xl;){switch(i=(t=xl).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==i){e=void 0,n=t,a=i.memoizedProps,i=i.memoizedState,r=n.stateNode;try{var g=vu(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(g,i),r.__reactInternalSnapshotBeforeUpdate=e}catch(e){cc(n,n.return,e)}}break;case 3:if(1024&e)if(9===(n=(e=t.stateNode.containerInfo).nodeType))vf(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":vf(e);break;default:e.textContent=""}break;default:if(1024&e)throw Error(o(163))}if(null!==(e=t.sibling)){e.return=t.return,xl=e;break}xl=t.return}}(e,t)}finally{ns=u,F.p=a,L.T=r}}Es=1,rc(),ac(),ic()}}function rc(){if(1===Es){Es=0;var e=Cs,t=Ts,n=!!(13878&t.flags);if(13878&t.subtreeFlags||n){n=L.T,L.T=null;var r=F.p;F.p=2;var a=ns;ns|=4;try{Ll(t,e);var i=nf,o=er(e.containerInfo),u=i.focusedElem,l=i.selectionRange;if(o!==u&&u&&u.ownerDocument&&Jn(u.ownerDocument.documentElement,u)){if(null!==l&&tr(u)){var s=l.start,c=l.end;if(void 0===c&&(c=s),"selectionStart"in u)u.selectionStart=s,u.selectionEnd=Math.min(c,u.value.length);else{var f=u.ownerDocument||document,d=f&&f.defaultView||window;if(d.getSelection){var p=d.getSelection(),h=u.textContent.length,g=Math.min(l.start,h),v=void 0===l.end?g:Math.min(l.end,h);!p.extend&&g>v&&(o=v,v=g,g=o);var m=Zn(u,g),y=Zn(u,v);if(m&&y&&(1!==p.rangeCount||p.anchorNode!==m.node||p.anchorOffset!==m.offset||p.focusNode!==y.node||p.focusOffset!==y.offset)){var b=f.createRange();b.setStart(m.node,m.offset),p.removeAllRanges(),g>v?(p.addRange(b),p.extend(y.node,y.offset)):(b.setEnd(y.node,y.offset),p.addRange(b))}}}}for(f=[],p=u;p=p.parentNode;)1===p.nodeType&&f.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"==typeof u.focus&&u.focus(),u=0;u<f.length;u++){var w=f[u];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}rd=!!tf,nf=tf=null}finally{ns=a,F.p=r,L.T=n}}e.current=t,Es=2}}function ac(){if(2===Es){Es=0;var e=Cs,t=Ts,n=!!(8772&t.flags);if(8772&t.subtreeFlags||n){n=L.T,L.T=null;var r=F.p;F.p=2;var a=ns;ns|=4;try{Sl(e,t.alternate,t)}finally{ns=a,F.p=r,L.T=n}}Es=3}}function ic(){if(4===Es||3===Es){Es=0,ee();var e=Cs,t=Ts,n=Ps,r=Ns;10256&t.subtreeFlags||10256&t.flags?Es=5:(Es=0,Ts=Cs=null,oc(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(Ss=null),ze(n),t=t.stateNode,fe&&"function"==typeof fe.onCommitFiberRoot)try{fe.onCommitFiberRoot(ce,t,void 0,!(128&~t.current.flags))}catch(e){}if(null!==r){t=L.T,a=F.p,F.p=2,L.T=null;try{for(var i=e.onRecoverableError,o=0;o<r.length;o++){var u=r[o];i(u.value,{componentStack:u.stack})}}finally{L.T=t,F.p=a}}3&Ps&&uc(),kc(e),a=e.pendingLanes,4194090&n&&42&a?e===Os?As++:(As=0,Os=e):As=0,xc(0,!1)}}function oc(e,t){0==(e.pooledCacheLanes&=t)&&null!=(t=e.pooledCache)&&(e.pooledCache=null,La(t))}function uc(e){return rc(),ac(),ic(),lc()}function lc(){if(5!==Es)return!1;var e=Cs,t=zs;zs=0;var n=ze(Ps),r=L.T,a=F.p;try{F.p=32>n?32:n,L.T=null,n=Ms,Ms=null;var i=Cs,u=Ps;if(Es=0,Ts=Cs=null,Ps=0,6&ns)throw Error(o(331));var l=ns;if(ns|=4,Xl(i.current),Hl(i,i.current,u,n),ns=l,xc(0,!1),fe&&"function"==typeof fe.onPostCommitFiberRoot)try{fe.onPostCommitFiberRoot(ce,i)}catch(e){}return!0}finally{F.p=a,L.T=r,oc(e,t)}}function sc(e,t,n){t=Sr(n,t),null!==(e=ii(e,t=xu(e.stateNode,t,2),2))&&(Ee(e,2),kc(e))}function cc(e,t,n){if(3===e.tag)sc(e,e,n);else for(;null!==t;){if(3===t.tag){sc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Ss||!Ss.has(r))){e=Sr(n,e),null!==(r=ii(t,n=Su(2),2))&&(Eu(n,r,t,e),Ee(r,2),kc(r));break}}t=t.return}}function fc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new ts;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(cs=!0,a.add(n),e=dc.bind(null,e,t,n),t.then(e,e))}function dc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,rs===e&&(is&n)===n&&(4===ds||3===ds&&(62914560&is)===is&&300>te()-_s?!(2&ns)&&Hs(e,0):gs|=n,ms===is&&(ms=0)),kc(e)}function pc(e,t){0===t&&(t=xe()),null!==(e=Nr(e,t))&&(Ee(e,t),kc(e))}function hc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),pc(e,n)}function gc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(o(314))}null!==r&&r.delete(t),pc(e,n)}var vc=null,mc=null,yc=!1,bc=!1,wc=!1,_c=0;function kc(e){e!==mc&&null===e.next&&(null===mc?vc=mc=e:mc=mc.next=e),bc=!0,yc||(yc=!0,df((function(){6&ns?X(re,Sc):Ec()})))}function xc(e,t){if(!wc&&bc){wc=!0;do{for(var n=!1,r=vc;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var i=0;else{var o=r.suspendedLanes,u=r.pingedLanes;i=(1<<31-pe(42|e)+1)-1,i=201326741&(i&=a&~(o&~u))?201326741&i|1:i?2|i:0}0!==i&&(n=!0,Pc(r,i))}else i=is,!(3&(i=be(r,r===rs?i:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||we(r,i)||(n=!0,Pc(r,i));r=r.next}}while(n);wc=!1}}function Sc(){Ec()}function Ec(){bc=yc=!1;var e,t=0;0!==_c&&(((e=window.event)&&"popstate"===e.type?e!==lf&&(lf=e,!0):(lf=null,!1))&&(t=_c),_c=0);for(var n=te(),r=null,a=vc;null!==a;){var i=a.next,o=Cc(a,n);0===o?(a.next=null,null===r?vc=i:r.next=i,null===i&&(mc=r)):(r=a,(0!==t||3&o)&&(bc=!0)),a=i}xc(t,!1)}function Cc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,i=-62914561&e.pendingLanes;0<i;){var o=31-pe(i),u=1<<o,l=a[o];-1===l?u&n&&!(u&r)||(a[o]=_e(u,t)):l<=t&&(e.expiredLanes|=u),i&=~u}if(n=is,n=be(e,e===(t=rs)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===os||9===os)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&Z(r),e.callbackNode=null,e.callbackPriority=0;if(!(3&n)||we(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&Z(r),ze(n)){case 2:case 8:n=ae;break;case 32:default:n=ie;break;case 268435456:n=ue}return r=Tc.bind(null,e),n=X(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&Z(r),e.callbackPriority=2,e.callbackNode=null,2}function Tc(e,t){if(0!==Es&&5!==Es)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(uc()&&e.callbackNode!==n)return null;var r=is;return 0===(r=be(e,e===rs?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Rs(e,r,t),Cc(e,te()),null!=e.callbackNode&&e.callbackNode===n?Tc.bind(null,e):null)}function Pc(e,t){if(uc())return null;Rs(e,t,!0)}function zc(){return 0===_c&&(_c=ke()),_c}function Mc(e){return null==e||"symbol"==typeof e||"boolean"==typeof e?null:"function"==typeof e?e:zt(""+e)}function Nc(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Ac=0;Ac<_r.length;Ac++){var Oc=_r[Ac];kr(Oc.toLowerCase(),"on"+(Oc[0].toUpperCase()+Oc.slice(1)))}kr(pr,"onAnimationEnd"),kr(hr,"onAnimationIteration"),kr(gr,"onAnimationStart"),kr("dblclick","onDoubleClick"),kr("focusin","onFocus"),kr("focusout","onBlur"),kr(vr,"onTransitionRun"),kr(mr,"onTransitionStart"),kr(yr,"onTransitionCancel"),kr(br,"onTransitionEnd"),Ge("onMouseEnter",["mouseout","mouseover"]),Ge("onMouseLeave",["mouseout","mouseover"]),Ge("onPointerEnter",["pointerout","pointerover"]),Ge("onPointerLeave",["pointerout","pointerover"]),Ye("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ye("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ye("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ye("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ye("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ye("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Lc="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fc=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Lc));function Dc(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var u=r[o],l=u.instance,s=u.currentTarget;if(u=u.listener,l!==i&&a.isPropagationStopped())break e;i=u,a.currentTarget=s;try{i(a)}catch(e){mu(e)}a.currentTarget=null,i=l}else for(o=0;o<r.length;o++){if(l=(u=r[o]).instance,s=u.currentTarget,u=u.listener,l!==i&&a.isPropagationStopped())break e;i=u,a.currentTarget=s;try{i(a)}catch(e){mu(e)}a.currentTarget=null,i=l}}}}function Rc(e,t){var n=t[Fe];void 0===n&&(n=t[Fe]=new Set);var r=e+"__bubble";n.has(r)||($c(t,e,2,!1),n.add(r))}function jc(e,t,n){var r=0;t&&(r|=4),$c(n,e,r,t)}var Uc="_reactListening"+Math.random().toString(36).slice(2);function Ic(e){if(!e[Uc]){e[Uc]=!0,qe.forEach((function(t){"selectionchange"!==t&&(Fc.has(t)||jc(t,!1,e),jc(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Uc]||(t[Uc]=!0,jc("selectionchange",!1,t))}}function $c(e,t,n,r){switch(cd(t)){case 2:var a=ad;break;case 8:a=id;break;default:a=od}n=a.bind(null,t,n,e),a=void 0,!Ut||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Bc(e,t,n,r,a){var i=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var u=r.stateNode.containerInfo;if(u===a)break;if(4===o)for(o=r.return;null!==o;){var s=o.tag;if((3===s||4===s)&&o.stateNode.containerInfo===a)return;o=o.return}for(;null!==u;){if(null===(o=$e(u)))return;if(5===(s=o.tag)||6===s||26===s||27===s){r=i=o;continue e}u=u.parentNode}}r=r.return}Dt((function(){var r=i,a=Nt(n),o=[];e:{var u=wr.get(e);if(void 0!==u){var s=Jt,c=e;switch(e){case"keypress":if(0===Vt(n))break e;case"keydown":case"keyup":s=gn;break;case"focusin":c="focus",s=on;break;case"focusout":c="blur",s=on;break;case"beforeblur":case"afterblur":s=on;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=mn;break;case pr:case hr:case gr:s=un;break;case br:s=yn;break;case"scroll":case"scrollend":s=tn;break;case"wheel":s=bn;break;case"copy":case"cut":case"paste":s=ln;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=vn;break;case"toggle":case"beforetoggle":s=wn}var f=!!(4&t),d=!f&&("scroll"===e||"scrollend"===e),p=f?null!==u?u+"Capture":null:u;f=[];for(var h,g=r;null!==g;){var v=g;if(h=v.stateNode,5!==(v=v.tag)&&26!==v&&27!==v||null===h||null===p||null!=(v=Rt(g,p))&&f.push(Hc(g,v,h)),d)break;g=g.return}0<f.length&&(u=new s(u,c,null,n,a),o.push({event:u,listeners:f}))}}if(!(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(u="mouseover"===e||"pointerover"===e)||n===Mt||!(c=n.relatedTarget||n.fromElement)||!$e(c)&&!c[Le])&&(s||u)&&(u=a.window===a?a:(u=a.ownerDocument)?u.defaultView||u.parentWindow:window,s?(s=r,null!==(c=(c=n.relatedTarget||n.toElement)?$e(c):null)&&(d=l(c),f=c.tag,c!==d||5!==f&&27!==f&&6!==f)&&(c=null)):(s=null,c=r),s!==c)){if(f=rn,v="onMouseLeave",p="onMouseEnter",g="mouse","pointerout"!==e&&"pointerover"!==e||(f=vn,v="onPointerLeave",p="onPointerEnter",g="pointer"),d=null==s?u:He(s),h=null==c?u:He(c),(u=new f(v,g+"leave",s,n,a)).target=d,u.relatedTarget=h,v=null,$e(a)===r&&((f=new f(p,g+"enter",c,n,a)).target=h,f.relatedTarget=d,v=f),d=v,s&&c)e:{for(p=c,g=0,h=f=s;h;h=Vc(h))g++;for(h=0,v=p;v;v=Vc(v))h++;for(;0<g-h;)f=Vc(f),g--;for(;0<h-g;)p=Vc(p),h--;for(;g--;){if(f===p||null!==p&&f===p.alternate)break e;f=Vc(f),p=Vc(p)}f=null}else f=null;null!==s&&qc(o,u,s,f,!1),null!==c&&null!==d&&qc(o,d,c,f,!0)}if("select"===(s=(u=r?He(r):window).nodeName&&u.nodeName.toLowerCase())||"input"===s&&"file"===u.type)var m=jn;else if(An(u))if(Un)m=Yn;else{m=qn;var y=Vn}else!(s=u.nodeName)||"input"!==s.toLowerCase()||"checkbox"!==u.type&&"radio"!==u.type?r&&Ct(r.elementType)&&(m=jn):m=Qn;switch(m&&(m=m(e,r))?On(o,m,n,a):(y&&y(e,u,r),"focusout"===e&&r&&"number"===u.type&&null!=r.memoizedProps.value&&yt(u,"number",u.value)),y=r?He(r):window,e){case"focusin":(An(y)||"true"===y.contentEditable)&&(rr=y,ar=r,ir=null);break;case"focusout":ir=ar=rr=null;break;case"mousedown":or=!0;break;case"contextmenu":case"mouseup":case"dragend":or=!1,ur(o,n,a);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":ur(o,n,a)}var b;if(kn)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else Mn?Pn(e,n)&&(w="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(w="onCompositionStart");w&&(En&&"ko"!==n.locale&&(Mn||"onCompositionStart"!==w?"onCompositionEnd"===w&&Mn&&(b=Wt()):(Bt="value"in($t=a)?$t.value:$t.textContent,Mn=!0)),0<(y=Wc(r,w)).length&&(w=new sn(w,e,null,n,a),o.push({event:w,listeners:y}),(b||null!==(b=zn(n)))&&(w.data=b))),(b=Sn?function(e,t){switch(e){case"compositionend":return zn(t);case"keypress":return 32!==t.which?null:(Tn=!0,Cn);case"textInput":return(e=t.data)===Cn&&Tn?null:e;default:return null}}(e,n):function(e,t){if(Mn)return"compositionend"===e||!kn&&Pn(e,t)?(e=Wt(),Ht=Bt=$t=null,Mn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return En&&"ko"!==t.locale?null:t.data}}(e,n))&&0<(w=Wc(r,"onBeforeInput")).length&&(y=new sn("onBeforeInput","beforeinput",null,n,a),o.push({event:y,listeners:w}),y.data=b),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var i=Mc((a[Oe]||null).action),o=r.submitter;o&&null!==(t=(t=o[Oe]||null)?Mc(t.formAction):o.getAttribute("formAction"))&&(i=t,o=null);var u=new Jt("action","action",null,r,a);e.push({event:u,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==_c){var e=o?Nc(a,o):new FormData(a);No(n,{pending:!0,data:e,method:a.method,action:i},null,e)}}else"function"==typeof i&&(u.preventDefault(),e=o?Nc(a,o):new FormData(a),No(n,{pending:!0,data:e,method:a.method,action:i},i,e))},currentTarget:a}]})}}(o,e,r,n,a)}Dc(o,t)}))}function Hc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Wc(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,i=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===i||(null!=(a=Rt(e,n))&&r.unshift(Hc(e,a,i)),null!=(a=Rt(e,t))&&r.push(Hc(e,a,i))),3===e.tag)return r;e=e.return}return[]}function Vc(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function qc(e,t,n,r,a){for(var i=t._reactName,o=[];null!==n&&n!==r;){var u=n,l=u.alternate,s=u.stateNode;if(u=u.tag,null!==l&&l===r)break;5!==u&&26!==u&&27!==u||null===s||(l=s,a?null!=(s=Rt(n,i))&&o.unshift(Hc(n,s,l)):a||null!=(s=Rt(n,i))&&o.push(Hc(n,s,l))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Qc=/\r\n?/g,Yc=/\u0000|\uFFFD/g;function Gc(e){return("string"==typeof e?e:""+e).replace(Qc,"\n").replace(Yc,"")}function Kc(e,t){return t=Gc(t),Gc(e)===t}function Xc(){}function Zc(e,t,n,r,a,i){switch(n){case"children":"string"==typeof r?"body"===t||"textarea"===t&&""===r||kt(e,r):("number"==typeof r||"bigint"==typeof r)&&"body"!==t&&kt(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":Et(e,r,i);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"==typeof r||"symbol"==typeof r||"boolean"==typeof r){e.removeAttribute(n);break}r=zt(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"==typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"==typeof i&&("formAction"===n?("input"!==t&&Zc(e,t,"name",a.name,a,null),Zc(e,t,"formEncType",a.formEncType,a,null),Zc(e,t,"formMethod",a.formMethod,a,null),Zc(e,t,"formTarget",a.formTarget,a,null)):(Zc(e,t,"encType",a.encType,a,null),Zc(e,t,"method",a.method,a,null),Zc(e,t,"target",a.target,a,null))),null==r||"symbol"==typeof r||"boolean"==typeof r){e.removeAttribute(n);break}r=zt(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Xc);break;case"onScroll":null!=r&&Rc("scroll",e);break;case"onScrollEnd":null!=r&&Rc("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!=typeof r||!("__html"in r))throw Error(o(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(o(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!=typeof r&&"symbol"!=typeof r;break;case"muted":e.muted=r&&"function"!=typeof r&&"symbol"!=typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"==typeof r||"boolean"==typeof r||"symbol"==typeof r){e.removeAttribute("xlink:href");break}n=zt(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!=typeof r&&"symbol"!=typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"==typeof r||"symbol"==typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Rc("beforetoggle",e),Rc("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Tt.get(n)||n,r)}}function Jc(e,t,n,r,a,i){switch(n){case"style":Et(e,r,i);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!=typeof r||!("__html"in r))throw Error(o(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(o(60));e.innerHTML=n}}break;case"children":"string"==typeof r?kt(e,r):("number"==typeof r||"bigint"==typeof r)&&kt(e,""+r);break;case"onScroll":null!=r&&Rc("scroll",e);break;case"onScrollEnd":null!=r&&Rc("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Xc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Qe.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"==typeof(i=null!=(i=e[Oe]||null)?i[n]:null)&&e.removeEventListener(t,i,a),"function"!=typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!=typeof i&&null!==i&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function ef(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Rc("error",e),Rc("load",e);var r,a=!1,i=!1;for(r in n)if(n.hasOwnProperty(r)){var u=n[r];if(null!=u)switch(r){case"src":a=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Zc(e,t,r,u,n,null)}}return i&&Zc(e,t,"srcSet",n.srcSet,n,null),void(a&&Zc(e,t,"src",n.src,n,null));case"input":Rc("invalid",e);var l=r=u=i=null,s=null,c=null;for(a in n)if(n.hasOwnProperty(a)){var f=n[a];if(null!=f)switch(a){case"name":i=f;break;case"type":u=f;break;case"checked":s=f;break;case"defaultChecked":c=f;break;case"value":r=f;break;case"defaultValue":l=f;break;case"children":case"dangerouslySetInnerHTML":if(null!=f)throw Error(o(137,t));break;default:Zc(e,t,a,f,n,null)}}return mt(e,r,l,s,c,u,i,!1),void ft(e);case"select":for(i in Rc("invalid",e),a=u=r=null,n)if(n.hasOwnProperty(i)&&null!=(l=n[i]))switch(i){case"value":r=l;break;case"defaultValue":u=l;break;case"multiple":a=l;default:Zc(e,t,i,l,n,null)}return t=r,n=u,e.multiple=!!a,void(null!=t?bt(e,!!a,t,!1):null!=n&&bt(e,!!a,n,!0));case"textarea":for(u in Rc("invalid",e),r=i=a=null,n)if(n.hasOwnProperty(u)&&null!=(l=n[u]))switch(u){case"value":a=l;break;case"defaultValue":i=l;break;case"children":r=l;break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(o(91));break;default:Zc(e,t,u,l,n,null)}return _t(e,a,i,r),void ft(e);case"option":for(s in n)n.hasOwnProperty(s)&&null!=(a=n[s])&&("selected"===s?e.selected=a&&"function"!=typeof a&&"symbol"!=typeof a:Zc(e,t,s,a,n,null));return;case"dialog":Rc("beforetoggle",e),Rc("toggle",e),Rc("cancel",e),Rc("close",e);break;case"iframe":case"object":Rc("load",e);break;case"video":case"audio":for(a=0;a<Lc.length;a++)Rc(Lc[a],e);break;case"image":Rc("error",e),Rc("load",e);break;case"details":Rc("toggle",e);break;case"embed":case"source":case"link":Rc("error",e),Rc("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Zc(e,t,c,a,n,null)}return;default:if(Ct(t)){for(f in n)n.hasOwnProperty(f)&&void 0!==(a=n[f])&&Jc(e,t,f,a,n,void 0);return}}for(l in n)n.hasOwnProperty(l)&&null!=(a=n[l])&&Zc(e,t,l,a,n,null)}var tf=null,nf=null;function rf(e){return 9===e.nodeType?e:e.ownerDocument}function af(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function of(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function uf(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"bigint"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var lf=null,sf="function"==typeof setTimeout?setTimeout:void 0,cf="function"==typeof clearTimeout?clearTimeout:void 0,ff="function"==typeof Promise?Promise:void 0,df="function"==typeof queueMicrotask?queueMicrotask:void 0!==ff?function(e){return ff.resolve(null).then(e).catch(pf)}:sf;function pf(e){setTimeout((function(){throw e}))}function hf(e){return"head"===e}function gf(e,t){var n=t,r=0,a=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&8===i.nodeType)if("/$"===(n=i.data)){if(0<r&&8>r){n=r;var o=e.ownerDocument;if(1&n&&kf(o.documentElement),2&n&&kf(o.body),4&n)for(kf(n=o.head),o=n.firstChild;o;){var u=o.nextSibling,l=o.nodeName;o[Ue]||"SCRIPT"===l||"STYLE"===l||"LINK"===l&&"stylesheet"===o.rel.toLowerCase()||n.removeChild(o),o=u}}if(0===a)return e.removeChild(i),void Pd(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=i}while(n);Pd(t)}function vf(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":vf(n),Ie(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function mf(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yf(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var bf=null;function wf(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function _f(e,t,n){switch(t=rf(n),e){case"html":if(!(e=t.documentElement))throw Error(o(452));return e;case"head":if(!(e=t.head))throw Error(o(453));return e;case"body":if(!(e=t.body))throw Error(o(454));return e;default:throw Error(o(451))}}function kf(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ie(e)}var xf=new Map,Sf=new Set;function Ef(e){return"function"==typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Cf=F.d;F.d={f:function(){var e=Cf.f(),t=$s();return e||t},r:function(e){var t=Be(e);null!==t&&5===t.tag&&"form"===t.type?Oo(t):Cf.r(e)},D:function(e){Cf.D(e),Pf("dns-prefetch",e,null)},C:function(e,t){Cf.C(e,t),Pf("preconnect",e,t)},L:function(e,t,n){Cf.L(e,t,n);var r=Tf;if(r&&e&&t){var a='link[rel="preload"][as="'+gt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+gt(n.imageSrcSet)+'"]',"string"==typeof n.imageSizes&&(a+='[imagesizes="'+gt(n.imageSizes)+'"]')):a+='[href="'+gt(e)+'"]';var i=a;switch(t){case"style":i=Mf(e);break;case"script":i=Of(e)}xf.has(i)||(e=d({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),xf.set(i,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Nf(i))||"script"===t&&r.querySelector(Lf(i))||(ef(t=r.createElement("link"),"link",e),Ve(t),r.head.appendChild(t)))}},m:function(e,t){Cf.m(e,t);var n=Tf;if(n&&e){var r=t&&"string"==typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+gt(r)+'"][href="'+gt(e)+'"]',i=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Of(e)}if(!xf.has(i)&&(e=d({rel:"modulepreload",href:e},t),xf.set(i,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Lf(i)))return}ef(r=n.createElement("link"),"link",e),Ve(r),n.head.appendChild(r)}}},X:function(e,t){Cf.X(e,t);var n=Tf;if(n&&e){var r=We(n).hoistableScripts,a=Of(e),i=r.get(a);i||((i=n.querySelector(Lf(a)))||(e=d({src:e,async:!0},t),(t=xf.get(a))&&jf(e,t),Ve(i=n.createElement("script")),ef(i,"link",e),n.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},r.set(a,i))}},S:function(e,t,n){Cf.S(e,t,n);var r=Tf;if(r&&e){var a=We(r).hoistableStyles,i=Mf(e);t=t||"default";var o=a.get(i);if(!o){var u={loading:0,preload:null};if(o=r.querySelector(Nf(i)))u.loading=5;else{e=d({rel:"stylesheet",href:e,"data-precedence":t},n),(n=xf.get(i))&&Rf(e,n);var l=o=r.createElement("link");Ve(l),ef(l,"link",e),l._p=new Promise((function(e,t){l.onload=e,l.onerror=t})),l.addEventListener("load",(function(){u.loading|=1})),l.addEventListener("error",(function(){u.loading|=2})),u.loading|=4,Df(o,t,r)}o={type:"stylesheet",instance:o,count:1,state:u},a.set(i,o)}}},M:function(e,t){Cf.M(e,t);var n=Tf;if(n&&e){var r=We(n).hoistableScripts,a=Of(e),i=r.get(a);i||((i=n.querySelector(Lf(a)))||(e=d({src:e,async:!0,type:"module"},t),(t=xf.get(a))&&jf(e,t),Ve(i=n.createElement("script")),ef(i,"link",e),n.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},r.set(a,i))}}};var Tf="undefined"==typeof document?null:document;function Pf(e,t,n){var r=Tf;if(r&&"string"==typeof t&&t){var a=gt(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"==typeof n&&(a+='[crossorigin="'+n+'"]'),Sf.has(a)||(Sf.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(ef(t=r.createElement("link"),"link",e),Ve(t),r.head.appendChild(t)))}}function zf(e,t,n,r){var a,i,u,l,s=(s=W.current)?Ef(s):null;if(!s)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return"string"==typeof n.precedence&&"string"==typeof n.href?(t=Mf(n.href),(r=(n=We(s).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"==typeof n.href&&"string"==typeof n.precedence){e=Mf(n.href);var c=We(s).hoistableStyles,f=c.get(e);if(f||(s=s.ownerDocument||s,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,f),(c=s.querySelector(Nf(e)))&&!c._p&&(f.instance=c,f.state.loading=5),xf.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},xf.set(e,n),c||(a=s,i=e,u=n,l=f.state,a.querySelector('link[rel="preload"][as="style"]['+i+"]")?l.loading=1:(i=a.createElement("link"),l.preload=i,i.addEventListener("load",(function(){return l.loading|=1})),i.addEventListener("error",(function(){return l.loading|=2})),ef(i,"link",u),Ve(i),a.head.appendChild(i))))),t&&null===r)throw Error(o(528,""));return f}if(t&&null!==r)throw Error(o(529,""));return null;case"script":return t=n.async,"string"==typeof(n=n.src)&&t&&"function"!=typeof t&&"symbol"!=typeof t?(t=Of(n),(r=(n=We(s).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function Mf(e){return'href="'+gt(e)+'"'}function Nf(e){return'link[rel="stylesheet"]['+e+"]"}function Af(e){return d({},e,{"data-precedence":e.precedence,precedence:null})}function Of(e){return'[src="'+gt(e)+'"]'}function Lf(e){return"script[async]"+e}function Ff(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+gt(n.href)+'"]');if(r)return t.instance=r,Ve(r),r;var a=d({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Ve(r=(e.ownerDocument||e).createElement("style")),ef(r,"style",a),Df(r,n.precedence,e),t.instance=r;case"stylesheet":a=Mf(n.href);var i=e.querySelector(Nf(a));if(i)return t.state.loading|=4,t.instance=i,Ve(i),i;r=Af(n),(a=xf.get(a))&&Rf(r,a),Ve(i=(e.ownerDocument||e).createElement("link"));var u=i;return u._p=new Promise((function(e,t){u.onload=e,u.onerror=t})),ef(i,"link",r),t.state.loading|=4,Df(i,n.precedence,e),t.instance=i;case"script":return i=Of(n.src),(a=e.querySelector(Lf(i)))?(t.instance=a,Ve(a),a):(r=n,(a=xf.get(i))&&jf(r=d({},n),a),Ve(a=(e=e.ownerDocument||e).createElement("script")),ef(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(o(443,t.type))}else"stylesheet"===t.type&&!(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Df(r,n.precedence,e));return t.instance}function Df(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,i=a,o=0;o<r.length;o++){var u=r[o];if(u.dataset.precedence===t)i=u;else if(i!==a)break}i?i.parentNode.insertBefore(e,i.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Rf(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function jf(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Uf=null;function If(e,t,n){if(null===Uf){var r=new Map,a=Uf=new Map;a.set(n,r)}else(r=(a=Uf).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var i=n[a];if(!(i[Ue]||i[Ae]||"link"===e&&"stylesheet"===i.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==i.namespaceURI){var o=i.getAttribute(t)||"";o=e+o;var u=r.get(o);u?u.push(i):r.set(o,[i])}}return r}function $f(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Bf(e){return!!("stylesheet"!==e.type||3&e.state.loading)}var Hf=null;function Wf(){}function Vf(){if(this.count--,0===this.count)if(this.stylesheets)Qf(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var qf=null;function Qf(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,qf=new Map,t.forEach(Yf,e),qf=null,Vf.call(e))}function Yf(e,t){if(!(4&t.state.loading)){var n=qf.get(e);if(n)var r=n.get(null);else{n=new Map,qf.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<a.length;i++){var o=a[i];"LINK"!==o.nodeName&&"not all"===o.getAttribute("media")||(n.set(o.dataset.precedence,o),r=o)}r&&n.set(null,r)}o=(a=t.instance).getAttribute("data-precedence"),(i=n.get(o)||r)===r&&n.set(null,a),n.set(o,a),this.count++,r=Vf.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),i?i.parentNode.insertBefore(a,i.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Gf={$$typeof:_,Provider:null,Consumer:null,_currentValue:D,_currentValue2:D,_threadCount:0};function Kf(e,t,n,r,a,i,o,u){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Se(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Se(0),this.hiddenUpdates=Se(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=i,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=u,this.incompleteTransitions=new Map}function Xf(e,t,n,r,a,i,o,u,l,s,c,f){return e=new Kf(e,t,n,o,u,l,s,f),t=1,!0===i&&(t|=24),i=Dr(3,null,null,t),e.current=i,i.stateNode=e,(t=Oa()).refCount++,e.pooledCache=t,t.refCount++,i.memoizedState={element:r,isDehydrated:n,cache:t},ni(i),e}function Zf(e){return e?e=Lr:Lr}function Jf(e,t,n,r,a,i){a=Zf(a),null===r.context?r.context=a:r.pendingContext=a,(r=ai(t)).payload={element:n},null!==(i=void 0===i?null:i)&&(r.callback=i),null!==(n=ii(e,r,t))&&(Ds(n,0,t),oi(n,e,t))}function ed(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function td(e,t){ed(e,t),(e=e.alternate)&&ed(e,t)}function nd(e){if(13===e.tag){var t=Nr(e,67108864);null!==t&&Ds(t,0,67108864),td(e,67108864)}}var rd=!0;function ad(e,t,n,r){var a=L.T;L.T=null;var i=F.p;try{F.p=2,od(e,t,n,r)}finally{F.p=i,L.T=a}}function id(e,t,n,r){var a=L.T;L.T=null;var i=F.p;try{F.p=8,od(e,t,n,r)}finally{F.p=i,L.T=a}}function od(e,t,n,r){if(rd){var a=ud(r);if(null===a)Bc(e,t,r,ld,n),bd(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return dd=wd(dd,e,t,n,r,a),!0;case"dragenter":return pd=wd(pd,e,t,n,r,a),!0;case"mouseover":return hd=wd(hd,e,t,n,r,a),!0;case"pointerover":var i=a.pointerId;return gd.set(i,wd(gd.get(i)||null,e,t,n,r,a)),!0;case"gotpointercapture":return i=a.pointerId,vd.set(i,wd(vd.get(i)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(bd(e,r),4&t&&-1<yd.indexOf(e)){for(;null!==a;){var i=Be(a);if(null!==i)switch(i.tag){case 3:if((i=i.stateNode).current.memoizedState.isDehydrated){var o=ye(i.pendingLanes);if(0!==o){var u=i;for(u.pendingLanes|=2,u.entangledLanes|=2;o;){var l=1<<31-pe(o);u.entanglements[1]|=l,o&=~l}kc(i),!(6&ns)&&(ks=te()+500,xc(0,!1))}}break;case 13:null!==(u=Nr(i,2))&&Ds(u,0,2),$s(),td(i,2)}if(null===(i=ud(r))&&Bc(e,t,r,ld,n),i===a)break;a=i}null!==a&&r.stopPropagation()}else Bc(e,t,r,null,n)}}function ud(e){return sd(e=Nt(e))}var ld=null;function sd(e){if(ld=null,null!==(e=$e(e))){var t=l(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=s(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return ld=e,null}function cd(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case ae:return 8;case ie:case oe:return 32;case ue:return 268435456;default:return 32}default:return 32}}var fd=!1,dd=null,pd=null,hd=null,gd=new Map,vd=new Map,md=[],yd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bd(e,t){switch(e){case"focusin":case"focusout":dd=null;break;case"dragenter":case"dragleave":pd=null;break;case"mouseover":case"mouseout":hd=null;break;case"pointerover":case"pointerout":gd.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":vd.delete(t.pointerId)}}function wd(e,t,n,r,a,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[a]},null!==t&&null!==(t=Be(t))&&nd(t),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function _d(e){var t=$e(e.target);if(null!==t){var n=l(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=s(n)))return e.blockedOn=t,void function(e){var t=F.p;try{return F.p=e,function(){if(13===n.tag){var e=Ls();e=Pe(e);var t=Nr(n,e);null!==t&&Ds(t,0,e),td(n,e)}}()}finally{F.p=t}}(e.priority)}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function kd(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=ud(e.nativeEvent);if(null!==n)return null!==(t=Be(n))&&nd(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Mt=r,n.target.dispatchEvent(r),Mt=null,t.shift()}return!0}function xd(e,t,n){kd(e)&&n.delete(t)}function Sd(){fd=!1,null!==dd&&kd(dd)&&(dd=null),null!==pd&&kd(pd)&&(pd=null),null!==hd&&kd(hd)&&(hd=null),gd.forEach(xd),vd.forEach(xd)}function Ed(e,t){e.blockedOn===t&&(e.blockedOn=null,fd||(fd=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Sd)))}var Cd=null;function Td(e){Cd!==e&&(Cd=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,(function(){Cd===e&&(Cd=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!=typeof r){if(null===sd(r||n))continue;break}var i=Be(n);null!==i&&(e.splice(t,3),t-=3,No(i,{pending:!0,data:a,method:n.method,action:r},r,a))}})))}function Pd(e){function t(t){return Ed(t,e)}null!==dd&&Ed(dd,e),null!==pd&&Ed(pd,e),null!==hd&&Ed(hd,e),gd.forEach(t),vd.forEach(t);for(var n=0;n<md.length;n++){var r=md[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<md.length&&null===(n=md[0]).blockedOn;)_d(n),null===n.blockedOn&&md.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],i=n[r+1],o=a[Oe]||null;if("function"==typeof i)o||Td(n);else if(o){var u=null;if(i&&i.hasAttribute("formAction")){if(a=i,o=i[Oe]||null)u=o.formAction;else if(null!==sd(a))continue}else u=o.action;"function"==typeof u?n[r+1]=u:(n.splice(r,3),r-=3),Td(n)}}}function zd(e){this._internalRoot=e}function Md(e){this._internalRoot=e}Md.prototype.render=zd.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Jf(t.current,Ls(),e,t,null,null)},Md.prototype.unmount=zd.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Jf(e.current,2,null,e,null,null),$s(),t[Le]=null}},Md.prototype.unstable_scheduleHydration=function(e){if(e){var t=Me();e={blockedOn:null,target:e,priority:t};for(var n=0;n<md.length&&0!==t&&t<md[n].priority;n++);md.splice(n,0,e),0===n&&_d(e)}};var Nd=a.version;if("19.1.0"!==Nd)throw Error(o(527,Nd,"19.1.0"));F.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=l(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var i=a.alternate;if(null===i){if(null!==(r=a.return)){n=r;continue}break}if(a.child===i.child){for(i=a.child;i;){if(i===n)return c(a),e;if(i===r)return c(a),t;i=i.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=i;else{for(var u=!1,s=a.child;s;){if(s===n){u=!0,n=a,r=i;break}if(s===r){u=!0,r=a,n=i;break}s=s.sibling}if(!u){for(s=i.child;s;){if(s===n){u=!0,n=i,r=a;break}if(s===r){u=!0,r=i,n=a;break}s=s.sibling}if(!u)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(t),null===(e=null!==e?f(e):null)?null:e.stateNode};var Ad={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:L,reconcilerVersion:"19.1.0"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Od=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Od.isDisabled&&Od.supportsFiber)try{ce=Od.inject(Ad),fe=Od}catch(e){}}t.createRoot=function(e,t){if(!u(e))throw Error(o(299));var n=!1,r="",a=yu,i=bu,l=wu;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(i=t.onCaughtError),void 0!==t.onRecoverableError&&(l=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Xf(e,1,!1,null,0,n,r,a,i,l,0,null),e[Le]=t.current,Ic(e),new zd(t)},t.hydrateRoot=function(e,t,n){if(!u(e))throw Error(o(299));var r=!1,a="",i=yu,l=bu,s=wu,c=null;return null!=n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(i=n.onUncaughtError),void 0!==n.onCaughtError&&(l=n.onCaughtError),void 0!==n.onRecoverableError&&(s=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=Xf(e,1,!0,t,0,r,a,i,l,s,0,c)).context=Zf(null),n=t.current,(a=ai(r=Pe(r=Ls()))).callback=null,ii(n,a,r),n=r,t.current.lanes=n,Ee(t,n),kc(t),e[Le]=t.current,Ic(e),new Md(t)},t.version="19.1.0"},338:(e,t,n)=>{"use strict";!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(247)},477:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<i(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var u=2*(r+1)-1,l=e[u],s=u+1,c=e[s];if(0>i(l,n))s<a&&0>i(c,l)?(e[r]=c,e[s]=n,r=s):(e[r]=l,e[u]=n,r=u);else{if(!(s<a&&0>i(c,n)))break e;e[r]=c,e[s]=n,r=s}}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var u=Date,l=u.now();t.unstable_now=function(){return u.now()-l}}var s=[],c=[],f=1,d=null,p=3,h=!1,g=!1,v=!1,m=!1,y="function"==typeof setTimeout?setTimeout:null,b="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function _(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(s,t)}t=r(c)}}function k(e){if(v=!1,_(e),!g)if(null!==r(s))g=!0,S||(S=!0,x());else{var t=r(c);null!==t&&A(k,t.startTime-e)}}var x,S=!1,E=-1,C=5,T=-1;function P(){return!(!m&&t.unstable_now()-T<C)}function z(){if(m=!1,S){var e=t.unstable_now();T=e;var n=!0;try{e:{g=!1,v&&(v=!1,b(E),E=-1),h=!0;var i=p;try{t:{for(_(e),d=r(s);null!==d&&!(d.expirationTime>e&&P());){var o=d.callback;if("function"==typeof o){d.callback=null,p=d.priorityLevel;var u=o(d.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof u){d.callback=u,_(e),n=!0;break t}d===r(s)&&a(s),_(e)}else a(s);d=r(s)}if(null!==d)n=!0;else{var l=r(c);null!==l&&A(k,l.startTime-e),n=!1}}break e}finally{d=null,p=i,h=!1}n=void 0}}finally{n?x():S=!1}}}if("function"==typeof w)x=function(){w(z)};else if("undefined"!=typeof MessageChannel){var M=new MessageChannel,N=M.port2;M.port1.onmessage=z,x=function(){N.postMessage(null)}}else x=function(){y(z,0)};function A(e,n){E=y((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_requestPaint=function(){m=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,i){var o=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?o+i:o,e){case 1:var u=-1;break;case 2:u=250;break;case 5:u=1073741823;break;case 4:u=1e4;break;default:u=5e3}return e={id:f++,callback:a,priorityLevel:e,startTime:i,expirationTime:u=i+u,sortIndex:-1},i>o?(e.sortIndex=i,n(c,e),null===r(s)&&e===r(c)&&(v?(b(E),E=-1):v=!0,A(k,i-o))):(e.sortIndex=u,n(s,e),g||h||(g=!0,S||(S=!0,x()))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},540:(e,t,n)=>{"use strict";e.exports=n(869)},543:function(e,t,n){var r;e=n.nmd(e),function(){var a,i="Expected a function",o="__lodash_hash_undefined__",u="__lodash_placeholder__",l=32,s=128,c=1/0,f=9007199254740991,d=NaN,p=4294967295,h=[["ary",s],["bind",1],["bindKey",2],["curry",8],["curryRight",16],["flip",512],["partial",l],["partialRight",64],["rearg",256]],g="[object Arguments]",v="[object Array]",m="[object Boolean]",y="[object Date]",b="[object Error]",w="[object Function]",_="[object GeneratorFunction]",k="[object Map]",x="[object Number]",S="[object Object]",E="[object Promise]",C="[object RegExp]",T="[object Set]",P="[object String]",z="[object Symbol]",M="[object WeakMap]",N="[object ArrayBuffer]",A="[object DataView]",O="[object Float32Array]",L="[object Float64Array]",F="[object Int8Array]",D="[object Int16Array]",R="[object Int32Array]",j="[object Uint8Array]",U="[object Uint8ClampedArray]",I="[object Uint16Array]",$="[object Uint32Array]",B=/\b__p \+= '';/g,H=/\b(__p \+=) '' \+/g,W=/(__e\(.*?\)|\b__t\)) \+\n'';/g,V=/&(?:amp|lt|gt|quot|#39);/g,q=/[&<>"']/g,Q=RegExp(V.source),Y=RegExp(q.source),G=/<%-([\s\S]+?)%>/g,K=/<%([\s\S]+?)%>/g,X=/<%=([\s\S]+?)%>/g,Z=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,J=/^\w*$/,ee=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,te=/[\\^$.*+?()[\]{}|]/g,ne=RegExp(te.source),re=/^\s+/,ae=/\s/,ie=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,oe=/\{\n\/\* \[wrapped with (.+)\] \*/,ue=/,? & /,le=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,se=/[()=,{}\[\]\/\s]/,ce=/\\(\\)?/g,fe=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,de=/\w*$/,pe=/^[-+]0x[0-9a-f]+$/i,he=/^0b[01]+$/i,ge=/^\[object .+?Constructor\]$/,ve=/^0o[0-7]+$/i,me=/^(?:0|[1-9]\d*)$/,ye=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,be=/($^)/,we=/['\n\r\u2028\u2029\\]/g,_e="\\ud800-\\udfff",ke="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",xe="\\u2700-\\u27bf",Se="a-z\\xdf-\\xf6\\xf8-\\xff",Ee="A-Z\\xc0-\\xd6\\xd8-\\xde",Ce="\\ufe0e\\ufe0f",Te="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Pe="["+_e+"]",ze="["+Te+"]",Me="["+ke+"]",Ne="\\d+",Ae="["+xe+"]",Oe="["+Se+"]",Le="[^"+_e+Te+Ne+xe+Se+Ee+"]",Fe="\\ud83c[\\udffb-\\udfff]",De="[^"+_e+"]",Re="(?:\\ud83c[\\udde6-\\uddff]){2}",je="[\\ud800-\\udbff][\\udc00-\\udfff]",Ue="["+Ee+"]",Ie="\\u200d",$e="(?:"+Oe+"|"+Le+")",Be="(?:"+Ue+"|"+Le+")",He="(?:['’](?:d|ll|m|re|s|t|ve))?",We="(?:['’](?:D|LL|M|RE|S|T|VE))?",Ve="(?:"+Me+"|"+Fe+")?",qe="["+Ce+"]?",Qe=qe+Ve+"(?:"+Ie+"(?:"+[De,Re,je].join("|")+")"+qe+Ve+")*",Ye="(?:"+[Ae,Re,je].join("|")+")"+Qe,Ge="(?:"+[De+Me+"?",Me,Re,je,Pe].join("|")+")",Ke=RegExp("['’]","g"),Xe=RegExp(Me,"g"),Ze=RegExp(Fe+"(?="+Fe+")|"+Ge+Qe,"g"),Je=RegExp([Ue+"?"+Oe+"+"+He+"(?="+[ze,Ue,"$"].join("|")+")",Be+"+"+We+"(?="+[ze,Ue+$e,"$"].join("|")+")",Ue+"?"+$e+"+"+He,Ue+"+"+We,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Ne,Ye].join("|"),"g"),et=RegExp("["+Ie+_e+ke+Ce+"]"),tt=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,nt=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],rt=-1,at={};at[O]=at[L]=at[F]=at[D]=at[R]=at[j]=at[U]=at[I]=at[$]=!0,at[g]=at[v]=at[N]=at[m]=at[A]=at[y]=at[b]=at[w]=at[k]=at[x]=at[S]=at[C]=at[T]=at[P]=at[M]=!1;var it={};it[g]=it[v]=it[N]=it[A]=it[m]=it[y]=it[O]=it[L]=it[F]=it[D]=it[R]=it[k]=it[x]=it[S]=it[C]=it[T]=it[P]=it[z]=it[j]=it[U]=it[I]=it[$]=!0,it[b]=it[w]=it[M]=!1;var ot={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},ut=parseFloat,lt=parseInt,st="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,ct="object"==typeof self&&self&&self.Object===Object&&self,ft=st||ct||Function("return this")(),dt=t&&!t.nodeType&&t,pt=dt&&e&&!e.nodeType&&e,ht=pt&&pt.exports===dt,gt=ht&&st.process,vt=function(){try{return pt&&pt.require&&pt.require("util").types||gt&&gt.binding&&gt.binding("util")}catch(e){}}(),mt=vt&&vt.isArrayBuffer,yt=vt&&vt.isDate,bt=vt&&vt.isMap,wt=vt&&vt.isRegExp,_t=vt&&vt.isSet,kt=vt&&vt.isTypedArray;function xt(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function St(e,t,n,r){for(var a=-1,i=null==e?0:e.length;++a<i;){var o=e[a];t(r,o,n(o),e)}return r}function Et(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}function Ct(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function Tt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0}function Pt(e,t){for(var n=-1,r=null==e?0:e.length,a=0,i=[];++n<r;){var o=e[n];t(o,n,e)&&(i[a++]=o)}return i}function zt(e,t){return!(null==e||!e.length)&&Ut(e,t,0)>-1}function Mt(e,t,n){for(var r=-1,a=null==e?0:e.length;++r<a;)if(n(t,e[r]))return!0;return!1}function Nt(e,t){for(var n=-1,r=null==e?0:e.length,a=Array(r);++n<r;)a[n]=t(e[n],n,e);return a}function At(e,t){for(var n=-1,r=t.length,a=e.length;++n<r;)e[a+n]=t[n];return e}function Ot(e,t,n,r){var a=-1,i=null==e?0:e.length;for(r&&i&&(n=e[++a]);++a<i;)n=t(n,e[a],a,e);return n}function Lt(e,t,n,r){var a=null==e?0:e.length;for(r&&a&&(n=e[--a]);a--;)n=t(n,e[a],a,e);return n}function Ft(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}var Dt=Ht("length");function Rt(e,t,n){var r;return n(e,(function(e,n,a){if(t(e,n,a))return r=n,!1})),r}function jt(e,t,n,r){for(var a=e.length,i=n+(r?1:-1);r?i--:++i<a;)if(t(e[i],i,e))return i;return-1}function Ut(e,t,n){return t==t?function(e,t,n){for(var r=n-1,a=e.length;++r<a;)if(e[r]===t)return r;return-1}(e,t,n):jt(e,$t,n)}function It(e,t,n,r){for(var a=n-1,i=e.length;++a<i;)if(r(e[a],t))return a;return-1}function $t(e){return e!=e}function Bt(e,t){var n=null==e?0:e.length;return n?qt(e,t)/n:d}function Ht(e){return function(t){return null==t?a:t[e]}}function Wt(e){return function(t){return null==e?a:e[t]}}function Vt(e,t,n,r,a){return a(e,(function(e,a,i){n=r?(r=!1,e):t(n,e,a,i)})),n}function qt(e,t){for(var n,r=-1,i=e.length;++r<i;){var o=t(e[r]);o!==a&&(n=n===a?o:n+o)}return n}function Qt(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function Yt(e){return e?e.slice(0,dn(e)+1).replace(re,""):e}function Gt(e){return function(t){return e(t)}}function Kt(e,t){return Nt(t,(function(t){return e[t]}))}function Xt(e,t){return e.has(t)}function Zt(e,t){for(var n=-1,r=e.length;++n<r&&Ut(t,e[n],0)>-1;);return n}function Jt(e,t){for(var n=e.length;n--&&Ut(t,e[n],0)>-1;);return n}var en=Wt({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"}),tn=Wt({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function nn(e){return"\\"+ot[e]}function rn(e){return et.test(e)}function an(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}function on(e,t){return function(n){return e(t(n))}}function un(e,t){for(var n=-1,r=e.length,a=0,i=[];++n<r;){var o=e[n];o!==t&&o!==u||(e[n]=u,i[a++]=n)}return i}function ln(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}function sn(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=[e,e]})),n}function cn(e){return rn(e)?function(e){for(var t=Ze.lastIndex=0;Ze.test(e);)++t;return t}(e):Dt(e)}function fn(e){return rn(e)?function(e){return e.match(Ze)||[]}(e):function(e){return e.split("")}(e)}function dn(e){for(var t=e.length;t--&&ae.test(e.charAt(t)););return t}var pn=Wt({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),hn=function e(t){var n,r=(t=null==t?ft:hn.defaults(ft.Object(),t,hn.pick(ft,nt))).Array,ae=t.Date,_e=t.Error,ke=t.Function,xe=t.Math,Se=t.Object,Ee=t.RegExp,Ce=t.String,Te=t.TypeError,Pe=r.prototype,ze=ke.prototype,Me=Se.prototype,Ne=t["__core-js_shared__"],Ae=ze.toString,Oe=Me.hasOwnProperty,Le=0,Fe=(n=/[^.]+$/.exec(Ne&&Ne.keys&&Ne.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"",De=Me.toString,Re=Ae.call(Se),je=ft._,Ue=Ee("^"+Ae.call(Oe).replace(te,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ie=ht?t.Buffer:a,$e=t.Symbol,Be=t.Uint8Array,He=Ie?Ie.allocUnsafe:a,We=on(Se.getPrototypeOf,Se),Ve=Se.create,qe=Me.propertyIsEnumerable,Qe=Pe.splice,Ye=$e?$e.isConcatSpreadable:a,Ge=$e?$e.iterator:a,Ze=$e?$e.toStringTag:a,et=function(){try{var e=li(Se,"defineProperty");return e({},"",{}),e}catch(e){}}(),ot=t.clearTimeout!==ft.clearTimeout&&t.clearTimeout,st=ae&&ae.now!==ft.Date.now&&ae.now,ct=t.setTimeout!==ft.setTimeout&&t.setTimeout,dt=xe.ceil,pt=xe.floor,gt=Se.getOwnPropertySymbols,vt=Ie?Ie.isBuffer:a,Dt=t.isFinite,Wt=Pe.join,gn=on(Se.keys,Se),vn=xe.max,mn=xe.min,yn=ae.now,bn=t.parseInt,wn=xe.random,_n=Pe.reverse,kn=li(t,"DataView"),xn=li(t,"Map"),Sn=li(t,"Promise"),En=li(t,"Set"),Cn=li(t,"WeakMap"),Tn=li(Se,"create"),Pn=Cn&&new Cn,zn={},Mn=Di(kn),Nn=Di(xn),An=Di(Sn),On=Di(En),Ln=Di(Cn),Fn=$e?$e.prototype:a,Dn=Fn?Fn.valueOf:a,Rn=Fn?Fn.toString:a;function jn(e){if(eu(e)&&!Ho(e)&&!(e instanceof Bn)){if(e instanceof $n)return e;if(Oe.call(e,"__wrapped__"))return Ri(e)}return new $n(e)}var Un=function(){function e(){}return function(t){if(!Jo(t))return{};if(Ve)return Ve(t);e.prototype=t;var n=new e;return e.prototype=a,n}}();function In(){}function $n(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=a}function Bn(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=p,this.__views__=[]}function Hn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Wn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Vn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function qn(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new Vn;++t<n;)this.add(e[t])}function Qn(e){var t=this.__data__=new Wn(e);this.size=t.size}function Yn(e,t){var n=Ho(e),r=!n&&Bo(e),a=!n&&!r&&Qo(e),i=!n&&!r&&!a&&lu(e),o=n||r||a||i,u=o?Qt(e.length,Ce):[],l=u.length;for(var s in e)!t&&!Oe.call(e,s)||o&&("length"==s||a&&("offset"==s||"parent"==s)||i&&("buffer"==s||"byteLength"==s||"byteOffset"==s)||gi(s,l))||u.push(s);return u}function Gn(e){var t=e.length;return t?e[Vr(0,t-1)]:a}function Kn(e,t){return Ni(Ca(e),ir(t,0,e.length))}function Xn(e){return Ni(Ca(e))}function Zn(e,t,n){(n!==a&&!Uo(e[t],n)||n===a&&!(t in e))&&rr(e,t,n)}function Jn(e,t,n){var r=e[t];Oe.call(e,t)&&Uo(r,n)&&(n!==a||t in e)||rr(e,t,n)}function er(e,t){for(var n=e.length;n--;)if(Uo(e[n][0],t))return n;return-1}function tr(e,t,n,r){return cr(e,(function(e,a,i){t(r,e,n(e),i)})),r}function nr(e,t){return e&&Ta(t,Mu(t),e)}function rr(e,t,n){"__proto__"==t&&et?et(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function ar(e,t){for(var n=-1,i=t.length,o=r(i),u=null==e;++n<i;)o[n]=u?a:Eu(e,t[n]);return o}function ir(e,t,n){return e==e&&(n!==a&&(e=e<=n?e:n),t!==a&&(e=e>=t?e:t)),e}function or(e,t,n,r,i,o){var u,l=1&t,s=2&t,c=4&t;if(n&&(u=i?n(e,r,i,o):n(e)),u!==a)return u;if(!Jo(e))return e;var f=Ho(e);if(f){if(u=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&Oe.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(e),!l)return Ca(e,u)}else{var d=fi(e),p=d==w||d==_;if(Qo(e))return wa(e,l);if(d==S||d==g||p&&!i){if(u=s||p?{}:pi(e),!l)return s?function(e,t){return Ta(e,ci(e),t)}(e,function(e,t){return e&&Ta(t,Nu(t),e)}(u,e)):function(e,t){return Ta(e,si(e),t)}(e,nr(u,e))}else{if(!it[d])return i?e:{};u=function(e,t,n){var r,a=e.constructor;switch(t){case N:return _a(e);case m:case y:return new a(+e);case A:return function(e,t){var n=t?_a(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}(e,n);case O:case L:case F:case D:case R:case j:case U:case I:case $:return ka(e,n);case k:return new a;case x:case P:return new a(e);case C:return function(e){var t=new e.constructor(e.source,de.exec(e));return t.lastIndex=e.lastIndex,t}(e);case T:return new a;case z:return r=e,Dn?Se(Dn.call(r)):{}}}(e,d,l)}}o||(o=new Qn);var h=o.get(e);if(h)return h;o.set(e,u),iu(e)?e.forEach((function(r){u.add(or(r,t,n,r,e,o))})):tu(e)&&e.forEach((function(r,a){u.set(a,or(r,t,n,a,e,o))}));var v=f?a:(c?s?ti:ei:s?Nu:Mu)(e);return Et(v||e,(function(r,a){v&&(r=e[a=r]),Jn(u,a,or(r,t,n,a,e,o))})),u}function ur(e,t,n){var r=n.length;if(null==e)return!r;for(e=Se(e);r--;){var i=n[r],o=t[i],u=e[i];if(u===a&&!(i in e)||!o(u))return!1}return!0}function lr(e,t,n){if("function"!=typeof e)throw new Te(i);return Ti((function(){e.apply(a,n)}),t)}function sr(e,t,n,r){var a=-1,i=zt,o=!0,u=e.length,l=[],s=t.length;if(!u)return l;n&&(t=Nt(t,Gt(n))),r?(i=Mt,o=!1):t.length>=200&&(i=Xt,o=!1,t=new qn(t));e:for(;++a<u;){var c=e[a],f=null==n?c:n(c);if(c=r||0!==c?c:0,o&&f==f){for(var d=s;d--;)if(t[d]===f)continue e;l.push(c)}else i(t,f,r)||l.push(c)}return l}jn.templateSettings={escape:G,evaluate:K,interpolate:X,variable:"",imports:{_:jn}},jn.prototype=In.prototype,jn.prototype.constructor=jn,$n.prototype=Un(In.prototype),$n.prototype.constructor=$n,Bn.prototype=Un(In.prototype),Bn.prototype.constructor=Bn,Hn.prototype.clear=function(){this.__data__=Tn?Tn(null):{},this.size=0},Hn.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Hn.prototype.get=function(e){var t=this.__data__;if(Tn){var n=t[e];return n===o?a:n}return Oe.call(t,e)?t[e]:a},Hn.prototype.has=function(e){var t=this.__data__;return Tn?t[e]!==a:Oe.call(t,e)},Hn.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Tn&&t===a?o:t,this},Wn.prototype.clear=function(){this.__data__=[],this.size=0},Wn.prototype.delete=function(e){var t=this.__data__,n=er(t,e);return!(n<0||(n==t.length-1?t.pop():Qe.call(t,n,1),--this.size,0))},Wn.prototype.get=function(e){var t=this.__data__,n=er(t,e);return n<0?a:t[n][1]},Wn.prototype.has=function(e){return er(this.__data__,e)>-1},Wn.prototype.set=function(e,t){var n=this.__data__,r=er(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},Vn.prototype.clear=function(){this.size=0,this.__data__={hash:new Hn,map:new(xn||Wn),string:new Hn}},Vn.prototype.delete=function(e){var t=oi(this,e).delete(e);return this.size-=t?1:0,t},Vn.prototype.get=function(e){return oi(this,e).get(e)},Vn.prototype.has=function(e){return oi(this,e).has(e)},Vn.prototype.set=function(e,t){var n=oi(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},qn.prototype.add=qn.prototype.push=function(e){return this.__data__.set(e,o),this},qn.prototype.has=function(e){return this.__data__.has(e)},Qn.prototype.clear=function(){this.__data__=new Wn,this.size=0},Qn.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Qn.prototype.get=function(e){return this.__data__.get(e)},Qn.prototype.has=function(e){return this.__data__.has(e)},Qn.prototype.set=function(e,t){var n=this.__data__;if(n instanceof Wn){var r=n.__data__;if(!xn||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new Vn(r)}return n.set(e,t),this.size=n.size,this};var cr=Ma(yr),fr=Ma(br,!0);function dr(e,t){var n=!0;return cr(e,(function(e,r,a){return n=!!t(e,r,a)})),n}function pr(e,t,n){for(var r=-1,i=e.length;++r<i;){var o=e[r],u=t(o);if(null!=u&&(l===a?u==u&&!uu(u):n(u,l)))var l=u,s=o}return s}function hr(e,t){var n=[];return cr(e,(function(e,r,a){t(e,r,a)&&n.push(e)})),n}function gr(e,t,n,r,a){var i=-1,o=e.length;for(n||(n=hi),a||(a=[]);++i<o;){var u=e[i];t>0&&n(u)?t>1?gr(u,t-1,n,r,a):At(a,u):r||(a[a.length]=u)}return a}var vr=Na(),mr=Na(!0);function yr(e,t){return e&&vr(e,t,Mu)}function br(e,t){return e&&mr(e,t,Mu)}function wr(e,t){return Pt(t,(function(t){return Ko(e[t])}))}function _r(e,t){for(var n=0,r=(t=va(t,e)).length;null!=e&&n<r;)e=e[Fi(t[n++])];return n&&n==r?e:a}function kr(e,t,n){var r=t(e);return Ho(e)?r:At(r,n(e))}function xr(e){return null==e?e===a?"[object Undefined]":"[object Null]":Ze&&Ze in Se(e)?function(e){var t=Oe.call(e,Ze),n=e[Ze];try{e[Ze]=a;var r=!0}catch(e){}var i=De.call(e);return r&&(t?e[Ze]=n:delete e[Ze]),i}(e):function(e){return De.call(e)}(e)}function Sr(e,t){return e>t}function Er(e,t){return null!=e&&Oe.call(e,t)}function Cr(e,t){return null!=e&&t in Se(e)}function Tr(e,t,n){for(var i=n?Mt:zt,o=e[0].length,u=e.length,l=u,s=r(u),c=1/0,f=[];l--;){var d=e[l];l&&t&&(d=Nt(d,Gt(t))),c=mn(d.length,c),s[l]=!n&&(t||o>=120&&d.length>=120)?new qn(l&&d):a}d=e[0];var p=-1,h=s[0];e:for(;++p<o&&f.length<c;){var g=d[p],v=t?t(g):g;if(g=n||0!==g?g:0,!(h?Xt(h,v):i(f,v,n))){for(l=u;--l;){var m=s[l];if(!(m?Xt(m,v):i(e[l],v,n)))continue e}h&&h.push(v),f.push(g)}}return f}function Pr(e,t,n){var r=null==(e=Si(e,t=va(t,e)))?e:e[Fi(Yi(t))];return null==r?a:xt(r,e,n)}function zr(e){return eu(e)&&xr(e)==g}function Mr(e,t,n,r,i){return e===t||(null==e||null==t||!eu(e)&&!eu(t)?e!=e&&t!=t:function(e,t,n,r,i,o){var u=Ho(e),l=Ho(t),s=u?v:fi(e),c=l?v:fi(t),f=(s=s==g?S:s)==S,d=(c=c==g?S:c)==S,p=s==c;if(p&&Qo(e)){if(!Qo(t))return!1;u=!0,f=!1}if(p&&!f)return o||(o=new Qn),u||lu(e)?Za(e,t,n,r,i,o):function(e,t,n,r,a,i,o){switch(n){case A:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case N:return!(e.byteLength!=t.byteLength||!i(new Be(e),new Be(t)));case m:case y:case x:return Uo(+e,+t);case b:return e.name==t.name&&e.message==t.message;case C:case P:return e==t+"";case k:var u=an;case T:var l=1&r;if(u||(u=ln),e.size!=t.size&&!l)return!1;var s=o.get(e);if(s)return s==t;r|=2,o.set(e,t);var c=Za(u(e),u(t),r,a,i,o);return o.delete(e),c;case z:if(Dn)return Dn.call(e)==Dn.call(t)}return!1}(e,t,s,n,r,i,o);if(!(1&n)){var h=f&&Oe.call(e,"__wrapped__"),w=d&&Oe.call(t,"__wrapped__");if(h||w){var _=h?e.value():e,E=w?t.value():t;return o||(o=new Qn),i(_,E,n,r,o)}}return!!p&&(o||(o=new Qn),function(e,t,n,r,i,o){var u=1&n,l=ei(e),s=l.length;if(s!=ei(t).length&&!u)return!1;for(var c=s;c--;){var f=l[c];if(!(u?f in t:Oe.call(t,f)))return!1}var d=o.get(e),p=o.get(t);if(d&&p)return d==t&&p==e;var h=!0;o.set(e,t),o.set(t,e);for(var g=u;++c<s;){var v=e[f=l[c]],m=t[f];if(r)var y=u?r(m,v,f,t,e,o):r(v,m,f,e,t,o);if(!(y===a?v===m||i(v,m,n,r,o):y)){h=!1;break}g||(g="constructor"==f)}if(h&&!g){var b=e.constructor,w=t.constructor;b==w||!("constructor"in e)||!("constructor"in t)||"function"==typeof b&&b instanceof b&&"function"==typeof w&&w instanceof w||(h=!1)}return o.delete(e),o.delete(t),h}(e,t,n,r,i,o))}(e,t,n,r,Mr,i))}function Nr(e,t,n,r){var i=n.length,o=i,u=!r;if(null==e)return!o;for(e=Se(e);i--;){var l=n[i];if(u&&l[2]?l[1]!==e[l[0]]:!(l[0]in e))return!1}for(;++i<o;){var s=(l=n[i])[0],c=e[s],f=l[1];if(u&&l[2]){if(c===a&&!(s in e))return!1}else{var d=new Qn;if(r)var p=r(c,f,s,e,t,d);if(!(p===a?Mr(f,c,3,r,d):p))return!1}}return!0}function Ar(e){return!(!Jo(e)||(t=e,Fe&&Fe in t))&&(Ko(e)?Ue:ge).test(Di(e));var t}function Or(e){return"function"==typeof e?e:null==e?nl:"object"==typeof e?Ho(e)?jr(e[0],e[1]):Rr(e):fl(e)}function Lr(e){if(!wi(e))return gn(e);var t=[];for(var n in Se(e))Oe.call(e,n)&&"constructor"!=n&&t.push(n);return t}function Fr(e,t){return e<t}function Dr(e,t){var n=-1,a=Vo(e)?r(e.length):[];return cr(e,(function(e,r,i){a[++n]=t(e,r,i)})),a}function Rr(e){var t=ui(e);return 1==t.length&&t[0][2]?ki(t[0][0],t[0][1]):function(n){return n===e||Nr(n,e,t)}}function jr(e,t){return mi(e)&&_i(t)?ki(Fi(e),t):function(n){var r=Eu(n,e);return r===a&&r===t?Cu(n,e):Mr(t,r,3)}}function Ur(e,t,n,r,i){e!==t&&vr(t,(function(o,u){if(i||(i=new Qn),Jo(o))!function(e,t,n,r,i,o,u){var l=Ei(e,n),s=Ei(t,n),c=u.get(s);if(c)Zn(e,n,c);else{var f=o?o(l,s,n+"",e,t,u):a,d=f===a;if(d){var p=Ho(s),h=!p&&Qo(s),g=!p&&!h&&lu(s);f=s,p||h||g?Ho(l)?f=l:qo(l)?f=Ca(l):h?(d=!1,f=wa(s,!0)):g?(d=!1,f=ka(s,!0)):f=[]:ru(s)||Bo(s)?(f=l,Bo(l)?f=vu(l):Jo(l)&&!Ko(l)||(f=pi(s))):d=!1}d&&(u.set(s,f),i(f,s,r,o,u),u.delete(s)),Zn(e,n,f)}}(e,t,u,n,Ur,r,i);else{var l=r?r(Ei(e,u),o,u+"",e,t,i):a;l===a&&(l=o),Zn(e,u,l)}}),Nu)}function Ir(e,t){var n=e.length;if(n)return gi(t+=t<0?n:0,n)?e[t]:a}function $r(e,t,n){t=t.length?Nt(t,(function(e){return Ho(e)?function(t){return _r(t,1===e.length?e[0]:e)}:e})):[nl];var r=-1;t=Nt(t,Gt(ii()));var a=Dr(e,(function(e,n,a){var i=Nt(t,(function(t){return t(e)}));return{criteria:i,index:++r,value:e}}));return function(e){var t=e.length;for(e.sort((function(e,t){return function(e,t,n){for(var r=-1,a=e.criteria,i=t.criteria,o=a.length,u=n.length;++r<o;){var l=xa(a[r],i[r]);if(l)return r>=u?l:l*("desc"==n[r]?-1:1)}return e.index-t.index}(e,t,n)}));t--;)e[t]=e[t].value;return e}(a)}function Br(e,t,n){for(var r=-1,a=t.length,i={};++r<a;){var o=t[r],u=_r(e,o);n(u,o)&&Kr(i,va(o,e),u)}return i}function Hr(e,t,n,r){var a=r?It:Ut,i=-1,o=t.length,u=e;for(e===t&&(t=Ca(t)),n&&(u=Nt(e,Gt(n)));++i<o;)for(var l=0,s=t[i],c=n?n(s):s;(l=a(u,c,l,r))>-1;)u!==e&&Qe.call(u,l,1),Qe.call(e,l,1);return e}function Wr(e,t){for(var n=e?t.length:0,r=n-1;n--;){var a=t[n];if(n==r||a!==i){var i=a;gi(a)?Qe.call(e,a,1):la(e,a)}}return e}function Vr(e,t){return e+pt(wn()*(t-e+1))}function qr(e,t){var n="";if(!e||t<1||t>f)return n;do{t%2&&(n+=e),(t=pt(t/2))&&(e+=e)}while(t);return n}function Qr(e,t){return Pi(xi(e,t,nl),e+"")}function Yr(e){return Gn(Uu(e))}function Gr(e,t){var n=Uu(e);return Ni(n,ir(t,0,n.length))}function Kr(e,t,n,r){if(!Jo(e))return e;for(var i=-1,o=(t=va(t,e)).length,u=o-1,l=e;null!=l&&++i<o;){var s=Fi(t[i]),c=n;if("__proto__"===s||"constructor"===s||"prototype"===s)return e;if(i!=u){var f=l[s];(c=r?r(f,s,l):a)===a&&(c=Jo(f)?f:gi(t[i+1])?[]:{})}Jn(l,s,c),l=l[s]}return e}var Xr=Pn?function(e,t){return Pn.set(e,t),e}:nl,Zr=et?function(e,t){return et(e,"toString",{configurable:!0,enumerable:!1,value:Ju(t),writable:!0})}:nl;function Jr(e){return Ni(Uu(e))}function ea(e,t,n){var a=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(n=n>i?i:n)<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var o=r(i);++a<i;)o[a]=e[a+t];return o}function ta(e,t){var n;return cr(e,(function(e,r,a){return!(n=t(e,r,a))})),!!n}function na(e,t,n){var r=0,a=null==e?r:e.length;if("number"==typeof t&&t==t&&a<=2147483647){for(;r<a;){var i=r+a>>>1,o=e[i];null!==o&&!uu(o)&&(n?o<=t:o<t)?r=i+1:a=i}return a}return ra(e,t,nl,n)}function ra(e,t,n,r){var i=0,o=null==e?0:e.length;if(0===o)return 0;for(var u=(t=n(t))!=t,l=null===t,s=uu(t),c=t===a;i<o;){var f=pt((i+o)/2),d=n(e[f]),p=d!==a,h=null===d,g=d==d,v=uu(d);if(u)var m=r||g;else m=c?g&&(r||p):l?g&&p&&(r||!h):s?g&&p&&!h&&(r||!v):!h&&!v&&(r?d<=t:d<t);m?i=f+1:o=f}return mn(o,4294967294)}function aa(e,t){for(var n=-1,r=e.length,a=0,i=[];++n<r;){var o=e[n],u=t?t(o):o;if(!n||!Uo(u,l)){var l=u;i[a++]=0===o?0:o}}return i}function ia(e){return"number"==typeof e?e:uu(e)?d:+e}function oa(e){if("string"==typeof e)return e;if(Ho(e))return Nt(e,oa)+"";if(uu(e))return Rn?Rn.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function ua(e,t,n){var r=-1,a=zt,i=e.length,o=!0,u=[],l=u;if(n)o=!1,a=Mt;else if(i>=200){var s=t?null:qa(e);if(s)return ln(s);o=!1,a=Xt,l=new qn}else l=t?[]:u;e:for(;++r<i;){var c=e[r],f=t?t(c):c;if(c=n||0!==c?c:0,o&&f==f){for(var d=l.length;d--;)if(l[d]===f)continue e;t&&l.push(f),u.push(c)}else a(l,f,n)||(l!==u&&l.push(f),u.push(c))}return u}function la(e,t){return null==(e=Si(e,t=va(t,e)))||delete e[Fi(Yi(t))]}function sa(e,t,n,r){return Kr(e,t,n(_r(e,t)),r)}function ca(e,t,n,r){for(var a=e.length,i=r?a:-1;(r?i--:++i<a)&&t(e[i],i,e););return n?ea(e,r?0:i,r?i+1:a):ea(e,r?i+1:0,r?a:i)}function fa(e,t){var n=e;return n instanceof Bn&&(n=n.value()),Ot(t,(function(e,t){return t.func.apply(t.thisArg,At([e],t.args))}),n)}function da(e,t,n){var a=e.length;if(a<2)return a?ua(e[0]):[];for(var i=-1,o=r(a);++i<a;)for(var u=e[i],l=-1;++l<a;)l!=i&&(o[i]=sr(o[i]||u,e[l],t,n));return ua(gr(o,1),t,n)}function pa(e,t,n){for(var r=-1,i=e.length,o=t.length,u={};++r<i;){var l=r<o?t[r]:a;n(u,e[r],l)}return u}function ha(e){return qo(e)?e:[]}function ga(e){return"function"==typeof e?e:nl}function va(e,t){return Ho(e)?e:mi(e,t)?[e]:Li(mu(e))}var ma=Qr;function ya(e,t,n){var r=e.length;return n=n===a?r:n,!t&&n>=r?e:ea(e,t,n)}var ba=ot||function(e){return ft.clearTimeout(e)};function wa(e,t){if(t)return e.slice();var n=e.length,r=He?He(n):new e.constructor(n);return e.copy(r),r}function _a(e){var t=new e.constructor(e.byteLength);return new Be(t).set(new Be(e)),t}function ka(e,t){var n=t?_a(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function xa(e,t){if(e!==t){var n=e!==a,r=null===e,i=e==e,o=uu(e),u=t!==a,l=null===t,s=t==t,c=uu(t);if(!l&&!c&&!o&&e>t||o&&u&&s&&!l&&!c||r&&u&&s||!n&&s||!i)return 1;if(!r&&!o&&!c&&e<t||c&&n&&i&&!r&&!o||l&&n&&i||!u&&i||!s)return-1}return 0}function Sa(e,t,n,a){for(var i=-1,o=e.length,u=n.length,l=-1,s=t.length,c=vn(o-u,0),f=r(s+c),d=!a;++l<s;)f[l]=t[l];for(;++i<u;)(d||i<o)&&(f[n[i]]=e[i]);for(;c--;)f[l++]=e[i++];return f}function Ea(e,t,n,a){for(var i=-1,o=e.length,u=-1,l=n.length,s=-1,c=t.length,f=vn(o-l,0),d=r(f+c),p=!a;++i<f;)d[i]=e[i];for(var h=i;++s<c;)d[h+s]=t[s];for(;++u<l;)(p||i<o)&&(d[h+n[u]]=e[i++]);return d}function Ca(e,t){var n=-1,a=e.length;for(t||(t=r(a));++n<a;)t[n]=e[n];return t}function Ta(e,t,n,r){var i=!n;n||(n={});for(var o=-1,u=t.length;++o<u;){var l=t[o],s=r?r(n[l],e[l],l,n,e):a;s===a&&(s=e[l]),i?rr(n,l,s):Jn(n,l,s)}return n}function Pa(e,t){return function(n,r){var a=Ho(n)?St:tr,i=t?t():{};return a(n,e,ii(r,2),i)}}function za(e){return Qr((function(t,n){var r=-1,i=n.length,o=i>1?n[i-1]:a,u=i>2?n[2]:a;for(o=e.length>3&&"function"==typeof o?(i--,o):a,u&&vi(n[0],n[1],u)&&(o=i<3?a:o,i=1),t=Se(t);++r<i;){var l=n[r];l&&e(t,l,r,o)}return t}))}function Ma(e,t){return function(n,r){if(null==n)return n;if(!Vo(n))return e(n,r);for(var a=n.length,i=t?a:-1,o=Se(n);(t?i--:++i<a)&&!1!==r(o[i],i,o););return n}}function Na(e){return function(t,n,r){for(var a=-1,i=Se(t),o=r(t),u=o.length;u--;){var l=o[e?u:++a];if(!1===n(i[l],l,i))break}return t}}function Aa(e){return function(t){var n=rn(t=mu(t))?fn(t):a,r=n?n[0]:t.charAt(0),i=n?ya(n,1).join(""):t.slice(1);return r[e]()+i}}function Oa(e){return function(t){return Ot(Ku(Bu(t).replace(Ke,"")),e,"")}}function La(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Un(e.prototype),r=e.apply(n,t);return Jo(r)?r:n}}function Fa(e){return function(t,n,r){var i=Se(t);if(!Vo(t)){var o=ii(n,3);t=Mu(t),n=function(e){return o(i[e],e,i)}}var u=e(t,n,r);return u>-1?i[o?t[u]:u]:a}}function Da(e){return Ja((function(t){var n=t.length,r=n,o=$n.prototype.thru;for(e&&t.reverse();r--;){var u=t[r];if("function"!=typeof u)throw new Te(i);if(o&&!l&&"wrapper"==ri(u))var l=new $n([],!0)}for(r=l?r:n;++r<n;){var s=ri(u=t[r]),c="wrapper"==s?ni(u):a;l=c&&yi(c[0])&&424==c[1]&&!c[4].length&&1==c[9]?l[ri(c[0])].apply(l,c[3]):1==u.length&&yi(u)?l[s]():l.thru(u)}return function(){var e=arguments,r=e[0];if(l&&1==e.length&&Ho(r))return l.plant(r).value();for(var a=0,i=n?t[a].apply(this,e):r;++a<n;)i=t[a].call(this,i);return i}}))}function Ra(e,t,n,i,o,u,l,c,f,d){var p=t&s,h=1&t,g=2&t,v=24&t,m=512&t,y=g?a:La(e);return function s(){for(var b=arguments.length,w=r(b),_=b;_--;)w[_]=arguments[_];if(v)var k=ai(s),x=function(e,t){for(var n=e.length,r=0;n--;)e[n]===t&&++r;return r}(w,k);if(i&&(w=Sa(w,i,o,v)),u&&(w=Ea(w,u,l,v)),b-=x,v&&b<d){var S=un(w,k);return Wa(e,t,Ra,s.placeholder,n,w,S,c,f,d-b)}var E=h?n:this,C=g?E[e]:e;return b=w.length,c?w=function(e,t){for(var n=e.length,r=mn(t.length,n),i=Ca(e);r--;){var o=t[r];e[r]=gi(o,n)?i[o]:a}return e}(w,c):m&&b>1&&w.reverse(),p&&f<b&&(w.length=f),this&&this!==ft&&this instanceof s&&(C=y||La(C)),C.apply(E,w)}}function ja(e,t){return function(n,r){return function(e,t,n,r){return yr(e,(function(e,a,i){t(r,n(e),a,i)})),r}(n,e,t(r),{})}}function Ua(e,t){return function(n,r){var i;if(n===a&&r===a)return t;if(n!==a&&(i=n),r!==a){if(i===a)return r;"string"==typeof n||"string"==typeof r?(n=oa(n),r=oa(r)):(n=ia(n),r=ia(r)),i=e(n,r)}return i}}function Ia(e){return Ja((function(t){return t=Nt(t,Gt(ii())),Qr((function(n){var r=this;return e(t,(function(e){return xt(e,r,n)}))}))}))}function $a(e,t){var n=(t=t===a?" ":oa(t)).length;if(n<2)return n?qr(t,e):t;var r=qr(t,dt(e/cn(t)));return rn(t)?ya(fn(r),0,e).join(""):r.slice(0,e)}function Ba(e){return function(t,n,i){return i&&"number"!=typeof i&&vi(t,n,i)&&(n=i=a),t=du(t),n===a?(n=t,t=0):n=du(n),function(e,t,n,a){for(var i=-1,o=vn(dt((t-e)/(n||1)),0),u=r(o);o--;)u[a?o:++i]=e,e+=n;return u}(t,n,i=i===a?t<n?1:-1:du(i),e)}}function Ha(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=gu(t),n=gu(n)),e(t,n)}}function Wa(e,t,n,r,i,o,u,s,c,f){var d=8&t;t|=d?l:64,4&(t&=~(d?64:l))||(t&=-4);var p=[e,t,i,d?o:a,d?u:a,d?a:o,d?a:u,s,c,f],h=n.apply(a,p);return yi(e)&&Ci(h,p),h.placeholder=r,zi(h,e,t)}function Va(e){var t=xe[e];return function(e,n){if(e=gu(e),(n=null==n?0:mn(pu(n),292))&&Dt(e)){var r=(mu(e)+"e").split("e");return+((r=(mu(t(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))}return t(e)}}var qa=En&&1/ln(new En([,-0]))[1]==c?function(e){return new En(e)}:ul;function Qa(e){return function(t){var n=fi(t);return n==k?an(t):n==T?sn(t):function(e,t){return Nt(t,(function(t){return[t,e[t]]}))}(t,e(t))}}function Ya(e,t,n,o,c,f,d,p){var h=2&t;if(!h&&"function"!=typeof e)throw new Te(i);var g=o?o.length:0;if(g||(t&=-97,o=c=a),d=d===a?d:vn(pu(d),0),p=p===a?p:pu(p),g-=c?c.length:0,64&t){var v=o,m=c;o=c=a}var y=h?a:ni(e),b=[e,t,n,o,c,v,m,f,d,p];if(y&&function(e,t){var n=e[1],r=t[1],a=n|r,i=a<131,o=r==s&&8==n||r==s&&256==n&&e[7].length<=t[8]||384==r&&t[7].length<=t[8]&&8==n;if(!i&&!o)return e;1&r&&(e[2]=t[2],a|=1&n?0:4);var l=t[3];if(l){var c=e[3];e[3]=c?Sa(c,l,t[4]):l,e[4]=c?un(e[3],u):t[4]}(l=t[5])&&(c=e[5],e[5]=c?Ea(c,l,t[6]):l,e[6]=c?un(e[5],u):t[6]),(l=t[7])&&(e[7]=l),r&s&&(e[8]=null==e[8]?t[8]:mn(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=a}(b,y),e=b[0],t=b[1],n=b[2],o=b[3],c=b[4],!(p=b[9]=b[9]===a?h?0:e.length:vn(b[9]-g,0))&&24&t&&(t&=-25),t&&1!=t)w=8==t||16==t?function(e,t,n){var i=La(e);return function o(){for(var u=arguments.length,l=r(u),s=u,c=ai(o);s--;)l[s]=arguments[s];var f=u<3&&l[0]!==c&&l[u-1]!==c?[]:un(l,c);return(u-=f.length)<n?Wa(e,t,Ra,o.placeholder,a,l,f,a,a,n-u):xt(this&&this!==ft&&this instanceof o?i:e,this,l)}}(e,t,p):t!=l&&33!=t||c.length?Ra.apply(a,b):function(e,t,n,a){var i=1&t,o=La(e);return function t(){for(var u=-1,l=arguments.length,s=-1,c=a.length,f=r(c+l),d=this&&this!==ft&&this instanceof t?o:e;++s<c;)f[s]=a[s];for(;l--;)f[s++]=arguments[++u];return xt(d,i?n:this,f)}}(e,t,n,o);else var w=function(e,t,n){var r=1&t,a=La(e);return function t(){return(this&&this!==ft&&this instanceof t?a:e).apply(r?n:this,arguments)}}(e,t,n);return zi((y?Xr:Ci)(w,b),e,t)}function Ga(e,t,n,r){return e===a||Uo(e,Me[n])&&!Oe.call(r,n)?t:e}function Ka(e,t,n,r,i,o){return Jo(e)&&Jo(t)&&(o.set(t,e),Ur(e,t,a,Ka,o),o.delete(t)),e}function Xa(e){return ru(e)?a:e}function Za(e,t,n,r,i,o){var u=1&n,l=e.length,s=t.length;if(l!=s&&!(u&&s>l))return!1;var c=o.get(e),f=o.get(t);if(c&&f)return c==t&&f==e;var d=-1,p=!0,h=2&n?new qn:a;for(o.set(e,t),o.set(t,e);++d<l;){var g=e[d],v=t[d];if(r)var m=u?r(v,g,d,t,e,o):r(g,v,d,e,t,o);if(m!==a){if(m)continue;p=!1;break}if(h){if(!Ft(t,(function(e,t){if(!Xt(h,t)&&(g===e||i(g,e,n,r,o)))return h.push(t)}))){p=!1;break}}else if(g!==v&&!i(g,v,n,r,o)){p=!1;break}}return o.delete(e),o.delete(t),p}function Ja(e){return Pi(xi(e,a,Hi),e+"")}function ei(e){return kr(e,Mu,si)}function ti(e){return kr(e,Nu,ci)}var ni=Pn?function(e){return Pn.get(e)}:ul;function ri(e){for(var t=e.name+"",n=zn[t],r=Oe.call(zn,t)?n.length:0;r--;){var a=n[r],i=a.func;if(null==i||i==e)return a.name}return t}function ai(e){return(Oe.call(jn,"placeholder")?jn:e).placeholder}function ii(){var e=jn.iteratee||rl;return e=e===rl?Or:e,arguments.length?e(arguments[0],arguments[1]):e}function oi(e,t){var n,r,a=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?a["string"==typeof t?"string":"hash"]:a.map}function ui(e){for(var t=Mu(e),n=t.length;n--;){var r=t[n],a=e[r];t[n]=[r,a,_i(a)]}return t}function li(e,t){var n=function(e,t){return null==e?a:e[t]}(e,t);return Ar(n)?n:a}var si=gt?function(e){return null==e?[]:(e=Se(e),Pt(gt(e),(function(t){return qe.call(e,t)})))}:hl,ci=gt?function(e){for(var t=[];e;)At(t,si(e)),e=We(e);return t}:hl,fi=xr;function di(e,t,n){for(var r=-1,a=(t=va(t,e)).length,i=!1;++r<a;){var o=Fi(t[r]);if(!(i=null!=e&&n(e,o)))break;e=e[o]}return i||++r!=a?i:!!(a=null==e?0:e.length)&&Zo(a)&&gi(o,a)&&(Ho(e)||Bo(e))}function pi(e){return"function"!=typeof e.constructor||wi(e)?{}:Un(We(e))}function hi(e){return Ho(e)||Bo(e)||!!(Ye&&e&&e[Ye])}function gi(e,t){var n=typeof e;return!!(t=null==t?f:t)&&("number"==n||"symbol"!=n&&me.test(e))&&e>-1&&e%1==0&&e<t}function vi(e,t,n){if(!Jo(n))return!1;var r=typeof t;return!!("number"==r?Vo(n)&&gi(t,n.length):"string"==r&&t in n)&&Uo(n[t],e)}function mi(e,t){if(Ho(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!uu(e))||J.test(e)||!Z.test(e)||null!=t&&e in Se(t)}function yi(e){var t=ri(e),n=jn[t];if("function"!=typeof n||!(t in Bn.prototype))return!1;if(e===n)return!0;var r=ni(n);return!!r&&e===r[0]}(kn&&fi(new kn(new ArrayBuffer(1)))!=A||xn&&fi(new xn)!=k||Sn&&fi(Sn.resolve())!=E||En&&fi(new En)!=T||Cn&&fi(new Cn)!=M)&&(fi=function(e){var t=xr(e),n=t==S?e.constructor:a,r=n?Di(n):"";if(r)switch(r){case Mn:return A;case Nn:return k;case An:return E;case On:return T;case Ln:return M}return t});var bi=Ne?Ko:gl;function wi(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Me)}function _i(e){return e==e&&!Jo(e)}function ki(e,t){return function(n){return null!=n&&n[e]===t&&(t!==a||e in Se(n))}}function xi(e,t,n){return t=vn(t===a?e.length-1:t,0),function(){for(var a=arguments,i=-1,o=vn(a.length-t,0),u=r(o);++i<o;)u[i]=a[t+i];i=-1;for(var l=r(t+1);++i<t;)l[i]=a[i];return l[t]=n(u),xt(e,this,l)}}function Si(e,t){return t.length<2?e:_r(e,ea(t,0,-1))}function Ei(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}var Ci=Mi(Xr),Ti=ct||function(e,t){return ft.setTimeout(e,t)},Pi=Mi(Zr);function zi(e,t,n){var r=t+"";return Pi(e,function(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(ie,"{\n/* [wrapped with "+t+"] */\n")}(r,function(e,t){return Et(h,(function(n){var r="_."+n[0];t&n[1]&&!zt(e,r)&&e.push(r)})),e.sort()}(function(e){var t=e.match(oe);return t?t[1].split(ue):[]}(r),n)))}function Mi(e){var t=0,n=0;return function(){var r=yn(),i=16-(r-n);if(n=r,i>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(a,arguments)}}function Ni(e,t){var n=-1,r=e.length,i=r-1;for(t=t===a?r:t;++n<t;){var o=Vr(n,i),u=e[o];e[o]=e[n],e[n]=u}return e.length=t,e}var Ai,Oi,Li=(Ai=Oo((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(ee,(function(e,n,r,a){t.push(r?a.replace(ce,"$1"):n||e)})),t}),(function(e){return 500===Oi.size&&Oi.clear(),e})),Oi=Ai.cache,Ai);function Fi(e){if("string"==typeof e||uu(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function Di(e){if(null!=e){try{return Ae.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function Ri(e){if(e instanceof Bn)return e.clone();var t=new $n(e.__wrapped__,e.__chain__);return t.__actions__=Ca(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}var ji=Qr((function(e,t){return qo(e)?sr(e,gr(t,1,qo,!0)):[]})),Ui=Qr((function(e,t){var n=Yi(t);return qo(n)&&(n=a),qo(e)?sr(e,gr(t,1,qo,!0),ii(n,2)):[]})),Ii=Qr((function(e,t){var n=Yi(t);return qo(n)&&(n=a),qo(e)?sr(e,gr(t,1,qo,!0),a,n):[]}));function $i(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var a=null==n?0:pu(n);return a<0&&(a=vn(r+a,0)),jt(e,ii(t,3),a)}function Bi(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=r-1;return n!==a&&(i=pu(n),i=n<0?vn(r+i,0):mn(i,r-1)),jt(e,ii(t,3),i,!0)}function Hi(e){return null!=e&&e.length?gr(e,1):[]}function Wi(e){return e&&e.length?e[0]:a}var Vi=Qr((function(e){var t=Nt(e,ha);return t.length&&t[0]===e[0]?Tr(t):[]})),qi=Qr((function(e){var t=Yi(e),n=Nt(e,ha);return t===Yi(n)?t=a:n.pop(),n.length&&n[0]===e[0]?Tr(n,ii(t,2)):[]})),Qi=Qr((function(e){var t=Yi(e),n=Nt(e,ha);return(t="function"==typeof t?t:a)&&n.pop(),n.length&&n[0]===e[0]?Tr(n,a,t):[]}));function Yi(e){var t=null==e?0:e.length;return t?e[t-1]:a}var Gi=Qr(Ki);function Ki(e,t){return e&&e.length&&t&&t.length?Hr(e,t):e}var Xi=Ja((function(e,t){var n=null==e?0:e.length,r=ar(e,t);return Wr(e,Nt(t,(function(e){return gi(e,n)?+e:e})).sort(xa)),r}));function Zi(e){return null==e?e:_n.call(e)}var Ji=Qr((function(e){return ua(gr(e,1,qo,!0))})),eo=Qr((function(e){var t=Yi(e);return qo(t)&&(t=a),ua(gr(e,1,qo,!0),ii(t,2))})),to=Qr((function(e){var t=Yi(e);return t="function"==typeof t?t:a,ua(gr(e,1,qo,!0),a,t)}));function no(e){if(!e||!e.length)return[];var t=0;return e=Pt(e,(function(e){if(qo(e))return t=vn(e.length,t),!0})),Qt(t,(function(t){return Nt(e,Ht(t))}))}function ro(e,t){if(!e||!e.length)return[];var n=no(e);return null==t?n:Nt(n,(function(e){return xt(t,a,e)}))}var ao=Qr((function(e,t){return qo(e)?sr(e,t):[]})),io=Qr((function(e){return da(Pt(e,qo))})),oo=Qr((function(e){var t=Yi(e);return qo(t)&&(t=a),da(Pt(e,qo),ii(t,2))})),uo=Qr((function(e){var t=Yi(e);return t="function"==typeof t?t:a,da(Pt(e,qo),a,t)})),lo=Qr(no),so=Qr((function(e){var t=e.length,n=t>1?e[t-1]:a;return n="function"==typeof n?(e.pop(),n):a,ro(e,n)}));function co(e){var t=jn(e);return t.__chain__=!0,t}function fo(e,t){return t(e)}var po=Ja((function(e){var t=e.length,n=t?e[0]:0,r=this.__wrapped__,i=function(t){return ar(t,e)};return!(t>1||this.__actions__.length)&&r instanceof Bn&&gi(n)?((r=r.slice(n,+n+(t?1:0))).__actions__.push({func:fo,args:[i],thisArg:a}),new $n(r,this.__chain__).thru((function(e){return t&&!e.length&&e.push(a),e}))):this.thru(i)})),ho=Pa((function(e,t,n){Oe.call(e,n)?++e[n]:rr(e,n,1)})),go=Fa($i),vo=Fa(Bi);function mo(e,t){return(Ho(e)?Et:cr)(e,ii(t,3))}function yo(e,t){return(Ho(e)?Ct:fr)(e,ii(t,3))}var bo=Pa((function(e,t,n){Oe.call(e,n)?e[n].push(t):rr(e,n,[t])})),wo=Qr((function(e,t,n){var a=-1,i="function"==typeof t,o=Vo(e)?r(e.length):[];return cr(e,(function(e){o[++a]=i?xt(t,e,n):Pr(e,t,n)})),o})),_o=Pa((function(e,t,n){rr(e,n,t)}));function ko(e,t){return(Ho(e)?Nt:Dr)(e,ii(t,3))}var xo=Pa((function(e,t,n){e[n?0:1].push(t)}),(function(){return[[],[]]})),So=Qr((function(e,t){if(null==e)return[];var n=t.length;return n>1&&vi(e,t[0],t[1])?t=[]:n>2&&vi(t[0],t[1],t[2])&&(t=[t[0]]),$r(e,gr(t,1),[])})),Eo=st||function(){return ft.Date.now()};function Co(e,t,n){return t=n?a:t,t=e&&null==t?e.length:t,Ya(e,s,a,a,a,a,t)}function To(e,t){var n;if("function"!=typeof t)throw new Te(i);return e=pu(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=a),n}}var Po=Qr((function(e,t,n){var r=1;if(n.length){var a=un(n,ai(Po));r|=l}return Ya(e,r,t,n,a)})),zo=Qr((function(e,t,n){var r=3;if(n.length){var a=un(n,ai(zo));r|=l}return Ya(t,r,e,n,a)}));function Mo(e,t,n){var r,o,u,l,s,c,f=0,d=!1,p=!1,h=!0;if("function"!=typeof e)throw new Te(i);function g(t){var n=r,i=o;return r=o=a,f=t,l=e.apply(i,n)}function v(e){var n=e-c;return c===a||n>=t||n<0||p&&e-f>=u}function m(){var e=Eo();if(v(e))return y(e);s=Ti(m,function(e){var n=t-(e-c);return p?mn(n,u-(e-f)):n}(e))}function y(e){return s=a,h&&r?g(e):(r=o=a,l)}function b(){var e=Eo(),n=v(e);if(r=arguments,o=this,c=e,n){if(s===a)return function(e){return f=e,s=Ti(m,t),d?g(e):l}(c);if(p)return ba(s),s=Ti(m,t),g(c)}return s===a&&(s=Ti(m,t)),l}return t=gu(t)||0,Jo(n)&&(d=!!n.leading,u=(p="maxWait"in n)?vn(gu(n.maxWait)||0,t):u,h="trailing"in n?!!n.trailing:h),b.cancel=function(){s!==a&&ba(s),f=0,r=c=o=s=a},b.flush=function(){return s===a?l:y(Eo())},b}var No=Qr((function(e,t){return lr(e,1,t)})),Ao=Qr((function(e,t,n){return lr(e,gu(t)||0,n)}));function Oo(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new Te(i);var n=function(){var r=arguments,a=t?t.apply(this,r):r[0],i=n.cache;if(i.has(a))return i.get(a);var o=e.apply(this,r);return n.cache=i.set(a,o)||i,o};return n.cache=new(Oo.Cache||Vn),n}function Lo(e){if("function"!=typeof e)throw new Te(i);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}Oo.Cache=Vn;var Fo=ma((function(e,t){var n=(t=1==t.length&&Ho(t[0])?Nt(t[0],Gt(ii())):Nt(gr(t,1),Gt(ii()))).length;return Qr((function(r){for(var a=-1,i=mn(r.length,n);++a<i;)r[a]=t[a].call(this,r[a]);return xt(e,this,r)}))})),Do=Qr((function(e,t){var n=un(t,ai(Do));return Ya(e,l,a,t,n)})),Ro=Qr((function(e,t){var n=un(t,ai(Ro));return Ya(e,64,a,t,n)})),jo=Ja((function(e,t){return Ya(e,256,a,a,a,t)}));function Uo(e,t){return e===t||e!=e&&t!=t}var Io=Ha(Sr),$o=Ha((function(e,t){return e>=t})),Bo=zr(function(){return arguments}())?zr:function(e){return eu(e)&&Oe.call(e,"callee")&&!qe.call(e,"callee")},Ho=r.isArray,Wo=mt?Gt(mt):function(e){return eu(e)&&xr(e)==N};function Vo(e){return null!=e&&Zo(e.length)&&!Ko(e)}function qo(e){return eu(e)&&Vo(e)}var Qo=vt||gl,Yo=yt?Gt(yt):function(e){return eu(e)&&xr(e)==y};function Go(e){if(!eu(e))return!1;var t=xr(e);return t==b||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!ru(e)}function Ko(e){if(!Jo(e))return!1;var t=xr(e);return t==w||t==_||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Xo(e){return"number"==typeof e&&e==pu(e)}function Zo(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=f}function Jo(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function eu(e){return null!=e&&"object"==typeof e}var tu=bt?Gt(bt):function(e){return eu(e)&&fi(e)==k};function nu(e){return"number"==typeof e||eu(e)&&xr(e)==x}function ru(e){if(!eu(e)||xr(e)!=S)return!1;var t=We(e);if(null===t)return!0;var n=Oe.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Ae.call(n)==Re}var au=wt?Gt(wt):function(e){return eu(e)&&xr(e)==C},iu=_t?Gt(_t):function(e){return eu(e)&&fi(e)==T};function ou(e){return"string"==typeof e||!Ho(e)&&eu(e)&&xr(e)==P}function uu(e){return"symbol"==typeof e||eu(e)&&xr(e)==z}var lu=kt?Gt(kt):function(e){return eu(e)&&Zo(e.length)&&!!at[xr(e)]},su=Ha(Fr),cu=Ha((function(e,t){return e<=t}));function fu(e){if(!e)return[];if(Vo(e))return ou(e)?fn(e):Ca(e);if(Ge&&e[Ge])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[Ge]());var t=fi(e);return(t==k?an:t==T?ln:Uu)(e)}function du(e){return e?(e=gu(e))===c||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function pu(e){var t=du(e),n=t%1;return t==t?n?t-n:t:0}function hu(e){return e?ir(pu(e),0,p):0}function gu(e){if("number"==typeof e)return e;if(uu(e))return d;if(Jo(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Jo(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=Yt(e);var n=he.test(e);return n||ve.test(e)?lt(e.slice(2),n?2:8):pe.test(e)?d:+e}function vu(e){return Ta(e,Nu(e))}function mu(e){return null==e?"":oa(e)}var yu=za((function(e,t){if(wi(t)||Vo(t))Ta(t,Mu(t),e);else for(var n in t)Oe.call(t,n)&&Jn(e,n,t[n])})),bu=za((function(e,t){Ta(t,Nu(t),e)})),wu=za((function(e,t,n,r){Ta(t,Nu(t),e,r)})),_u=za((function(e,t,n,r){Ta(t,Mu(t),e,r)})),ku=Ja(ar),xu=Qr((function(e,t){e=Se(e);var n=-1,r=t.length,i=r>2?t[2]:a;for(i&&vi(t[0],t[1],i)&&(r=1);++n<r;)for(var o=t[n],u=Nu(o),l=-1,s=u.length;++l<s;){var c=u[l],f=e[c];(f===a||Uo(f,Me[c])&&!Oe.call(e,c))&&(e[c]=o[c])}return e})),Su=Qr((function(e){return e.push(a,Ka),xt(Ou,a,e)}));function Eu(e,t,n){var r=null==e?a:_r(e,t);return r===a?n:r}function Cu(e,t){return null!=e&&di(e,t,Cr)}var Tu=ja((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=De.call(t)),e[t]=n}),Ju(nl)),Pu=ja((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=De.call(t)),Oe.call(e,t)?e[t].push(n):e[t]=[n]}),ii),zu=Qr(Pr);function Mu(e){return Vo(e)?Yn(e):Lr(e)}function Nu(e){return Vo(e)?Yn(e,!0):function(e){if(!Jo(e))return function(e){var t=[];if(null!=e)for(var n in Se(e))t.push(n);return t}(e);var t=wi(e),n=[];for(var r in e)("constructor"!=r||!t&&Oe.call(e,r))&&n.push(r);return n}(e)}var Au=za((function(e,t,n){Ur(e,t,n)})),Ou=za((function(e,t,n,r){Ur(e,t,n,r)})),Lu=Ja((function(e,t){var n={};if(null==e)return n;var r=!1;t=Nt(t,(function(t){return t=va(t,e),r||(r=t.length>1),t})),Ta(e,ti(e),n),r&&(n=or(n,7,Xa));for(var a=t.length;a--;)la(n,t[a]);return n})),Fu=Ja((function(e,t){return null==e?{}:function(e,t){return Br(e,t,(function(t,n){return Cu(e,n)}))}(e,t)}));function Du(e,t){if(null==e)return{};var n=Nt(ti(e),(function(e){return[e]}));return t=ii(t),Br(e,n,(function(e,n){return t(e,n[0])}))}var Ru=Qa(Mu),ju=Qa(Nu);function Uu(e){return null==e?[]:Kt(e,Mu(e))}var Iu=Oa((function(e,t,n){return t=t.toLowerCase(),e+(n?$u(t):t)}));function $u(e){return Gu(mu(e).toLowerCase())}function Bu(e){return(e=mu(e))&&e.replace(ye,en).replace(Xe,"")}var Hu=Oa((function(e,t,n){return e+(n?"-":"")+t.toLowerCase()})),Wu=Oa((function(e,t,n){return e+(n?" ":"")+t.toLowerCase()})),Vu=Aa("toLowerCase"),qu=Oa((function(e,t,n){return e+(n?"_":"")+t.toLowerCase()})),Qu=Oa((function(e,t,n){return e+(n?" ":"")+Gu(t)})),Yu=Oa((function(e,t,n){return e+(n?" ":"")+t.toUpperCase()})),Gu=Aa("toUpperCase");function Ku(e,t,n){return e=mu(e),(t=n?a:t)===a?function(e){return tt.test(e)}(e)?function(e){return e.match(Je)||[]}(e):function(e){return e.match(le)||[]}(e):e.match(t)||[]}var Xu=Qr((function(e,t){try{return xt(e,a,t)}catch(e){return Go(e)?e:new _e(e)}})),Zu=Ja((function(e,t){return Et(t,(function(t){t=Fi(t),rr(e,t,Po(e[t],e))})),e}));function Ju(e){return function(){return e}}var el=Da(),tl=Da(!0);function nl(e){return e}function rl(e){return Or("function"==typeof e?e:or(e,1))}var al=Qr((function(e,t){return function(n){return Pr(n,e,t)}})),il=Qr((function(e,t){return function(n){return Pr(e,n,t)}}));function ol(e,t,n){var r=Mu(t),a=wr(t,r);null!=n||Jo(t)&&(a.length||!r.length)||(n=t,t=e,e=this,a=wr(t,Mu(t)));var i=!(Jo(n)&&"chain"in n&&!n.chain),o=Ko(e);return Et(a,(function(n){var r=t[n];e[n]=r,o&&(e.prototype[n]=function(){var t=this.__chain__;if(i||t){var n=e(this.__wrapped__);return(n.__actions__=Ca(this.__actions__)).push({func:r,args:arguments,thisArg:e}),n.__chain__=t,n}return r.apply(e,At([this.value()],arguments))})})),e}function ul(){}var ll=Ia(Nt),sl=Ia(Tt),cl=Ia(Ft);function fl(e){return mi(e)?Ht(Fi(e)):function(e){return function(t){return _r(t,e)}}(e)}var dl=Ba(),pl=Ba(!0);function hl(){return[]}function gl(){return!1}var vl,ml=Ua((function(e,t){return e+t}),0),yl=Va("ceil"),bl=Ua((function(e,t){return e/t}),1),wl=Va("floor"),_l=Ua((function(e,t){return e*t}),1),kl=Va("round"),xl=Ua((function(e,t){return e-t}),0);return jn.after=function(e,t){if("function"!=typeof t)throw new Te(i);return e=pu(e),function(){if(--e<1)return t.apply(this,arguments)}},jn.ary=Co,jn.assign=yu,jn.assignIn=bu,jn.assignInWith=wu,jn.assignWith=_u,jn.at=ku,jn.before=To,jn.bind=Po,jn.bindAll=Zu,jn.bindKey=zo,jn.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Ho(e)?e:[e]},jn.chain=co,jn.chunk=function(e,t,n){t=(n?vi(e,t,n):t===a)?1:vn(pu(t),0);var i=null==e?0:e.length;if(!i||t<1)return[];for(var o=0,u=0,l=r(dt(i/t));o<i;)l[u++]=ea(e,o,o+=t);return l},jn.compact=function(e){for(var t=-1,n=null==e?0:e.length,r=0,a=[];++t<n;){var i=e[t];i&&(a[r++]=i)}return a},jn.concat=function(){var e=arguments.length;if(!e)return[];for(var t=r(e-1),n=arguments[0],a=e;a--;)t[a-1]=arguments[a];return At(Ho(n)?Ca(n):[n],gr(t,1))},jn.cond=function(e){var t=null==e?0:e.length,n=ii();return e=t?Nt(e,(function(e){if("function"!=typeof e[1])throw new Te(i);return[n(e[0]),e[1]]})):[],Qr((function(n){for(var r=-1;++r<t;){var a=e[r];if(xt(a[0],this,n))return xt(a[1],this,n)}}))},jn.conforms=function(e){return function(e){var t=Mu(e);return function(n){return ur(n,e,t)}}(or(e,1))},jn.constant=Ju,jn.countBy=ho,jn.create=function(e,t){var n=Un(e);return null==t?n:nr(n,t)},jn.curry=function e(t,n,r){var i=Ya(t,8,a,a,a,a,a,n=r?a:n);return i.placeholder=e.placeholder,i},jn.curryRight=function e(t,n,r){var i=Ya(t,16,a,a,a,a,a,n=r?a:n);return i.placeholder=e.placeholder,i},jn.debounce=Mo,jn.defaults=xu,jn.defaultsDeep=Su,jn.defer=No,jn.delay=Ao,jn.difference=ji,jn.differenceBy=Ui,jn.differenceWith=Ii,jn.drop=function(e,t,n){var r=null==e?0:e.length;return r?ea(e,(t=n||t===a?1:pu(t))<0?0:t,r):[]},jn.dropRight=function(e,t,n){var r=null==e?0:e.length;return r?ea(e,0,(t=r-(t=n||t===a?1:pu(t)))<0?0:t):[]},jn.dropRightWhile=function(e,t){return e&&e.length?ca(e,ii(t,3),!0,!0):[]},jn.dropWhile=function(e,t){return e&&e.length?ca(e,ii(t,3),!0):[]},jn.fill=function(e,t,n,r){var i=null==e?0:e.length;return i?(n&&"number"!=typeof n&&vi(e,t,n)&&(n=0,r=i),function(e,t,n,r){var i=e.length;for((n=pu(n))<0&&(n=-n>i?0:i+n),(r=r===a||r>i?i:pu(r))<0&&(r+=i),r=n>r?0:hu(r);n<r;)e[n++]=t;return e}(e,t,n,r)):[]},jn.filter=function(e,t){return(Ho(e)?Pt:hr)(e,ii(t,3))},jn.flatMap=function(e,t){return gr(ko(e,t),1)},jn.flatMapDeep=function(e,t){return gr(ko(e,t),c)},jn.flatMapDepth=function(e,t,n){return n=n===a?1:pu(n),gr(ko(e,t),n)},jn.flatten=Hi,jn.flattenDeep=function(e){return null!=e&&e.length?gr(e,c):[]},jn.flattenDepth=function(e,t){return null!=e&&e.length?gr(e,t=t===a?1:pu(t)):[]},jn.flip=function(e){return Ya(e,512)},jn.flow=el,jn.flowRight=tl,jn.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,r={};++t<n;){var a=e[t];r[a[0]]=a[1]}return r},jn.functions=function(e){return null==e?[]:wr(e,Mu(e))},jn.functionsIn=function(e){return null==e?[]:wr(e,Nu(e))},jn.groupBy=bo,jn.initial=function(e){return null!=e&&e.length?ea(e,0,-1):[]},jn.intersection=Vi,jn.intersectionBy=qi,jn.intersectionWith=Qi,jn.invert=Tu,jn.invertBy=Pu,jn.invokeMap=wo,jn.iteratee=rl,jn.keyBy=_o,jn.keys=Mu,jn.keysIn=Nu,jn.map=ko,jn.mapKeys=function(e,t){var n={};return t=ii(t,3),yr(e,(function(e,r,a){rr(n,t(e,r,a),e)})),n},jn.mapValues=function(e,t){var n={};return t=ii(t,3),yr(e,(function(e,r,a){rr(n,r,t(e,r,a))})),n},jn.matches=function(e){return Rr(or(e,1))},jn.matchesProperty=function(e,t){return jr(e,or(t,1))},jn.memoize=Oo,jn.merge=Au,jn.mergeWith=Ou,jn.method=al,jn.methodOf=il,jn.mixin=ol,jn.negate=Lo,jn.nthArg=function(e){return e=pu(e),Qr((function(t){return Ir(t,e)}))},jn.omit=Lu,jn.omitBy=function(e,t){return Du(e,Lo(ii(t)))},jn.once=function(e){return To(2,e)},jn.orderBy=function(e,t,n,r){return null==e?[]:(Ho(t)||(t=null==t?[]:[t]),Ho(n=r?a:n)||(n=null==n?[]:[n]),$r(e,t,n))},jn.over=ll,jn.overArgs=Fo,jn.overEvery=sl,jn.overSome=cl,jn.partial=Do,jn.partialRight=Ro,jn.partition=xo,jn.pick=Fu,jn.pickBy=Du,jn.property=fl,jn.propertyOf=function(e){return function(t){return null==e?a:_r(e,t)}},jn.pull=Gi,jn.pullAll=Ki,jn.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?Hr(e,t,ii(n,2)):e},jn.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?Hr(e,t,a,n):e},jn.pullAt=Xi,jn.range=dl,jn.rangeRight=pl,jn.rearg=jo,jn.reject=function(e,t){return(Ho(e)?Pt:hr)(e,Lo(ii(t,3)))},jn.remove=function(e,t){var n=[];if(!e||!e.length)return n;var r=-1,a=[],i=e.length;for(t=ii(t,3);++r<i;){var o=e[r];t(o,r,e)&&(n.push(o),a.push(r))}return Wr(e,a),n},jn.rest=function(e,t){if("function"!=typeof e)throw new Te(i);return Qr(e,t=t===a?t:pu(t))},jn.reverse=Zi,jn.sampleSize=function(e,t,n){return t=(n?vi(e,t,n):t===a)?1:pu(t),(Ho(e)?Kn:Gr)(e,t)},jn.set=function(e,t,n){return null==e?e:Kr(e,t,n)},jn.setWith=function(e,t,n,r){return r="function"==typeof r?r:a,null==e?e:Kr(e,t,n,r)},jn.shuffle=function(e){return(Ho(e)?Xn:Jr)(e)},jn.slice=function(e,t,n){var r=null==e?0:e.length;return r?(n&&"number"!=typeof n&&vi(e,t,n)?(t=0,n=r):(t=null==t?0:pu(t),n=n===a?r:pu(n)),ea(e,t,n)):[]},jn.sortBy=So,jn.sortedUniq=function(e){return e&&e.length?aa(e):[]},jn.sortedUniqBy=function(e,t){return e&&e.length?aa(e,ii(t,2)):[]},jn.split=function(e,t,n){return n&&"number"!=typeof n&&vi(e,t,n)&&(t=n=a),(n=n===a?p:n>>>0)?(e=mu(e))&&("string"==typeof t||null!=t&&!au(t))&&!(t=oa(t))&&rn(e)?ya(fn(e),0,n):e.split(t,n):[]},jn.spread=function(e,t){if("function"!=typeof e)throw new Te(i);return t=null==t?0:vn(pu(t),0),Qr((function(n){var r=n[t],a=ya(n,0,t);return r&&At(a,r),xt(e,this,a)}))},jn.tail=function(e){var t=null==e?0:e.length;return t?ea(e,1,t):[]},jn.take=function(e,t,n){return e&&e.length?ea(e,0,(t=n||t===a?1:pu(t))<0?0:t):[]},jn.takeRight=function(e,t,n){var r=null==e?0:e.length;return r?ea(e,(t=r-(t=n||t===a?1:pu(t)))<0?0:t,r):[]},jn.takeRightWhile=function(e,t){return e&&e.length?ca(e,ii(t,3),!1,!0):[]},jn.takeWhile=function(e,t){return e&&e.length?ca(e,ii(t,3)):[]},jn.tap=function(e,t){return t(e),e},jn.throttle=function(e,t,n){var r=!0,a=!0;if("function"!=typeof e)throw new Te(i);return Jo(n)&&(r="leading"in n?!!n.leading:r,a="trailing"in n?!!n.trailing:a),Mo(e,t,{leading:r,maxWait:t,trailing:a})},jn.thru=fo,jn.toArray=fu,jn.toPairs=Ru,jn.toPairsIn=ju,jn.toPath=function(e){return Ho(e)?Nt(e,Fi):uu(e)?[e]:Ca(Li(mu(e)))},jn.toPlainObject=vu,jn.transform=function(e,t,n){var r=Ho(e),a=r||Qo(e)||lu(e);if(t=ii(t,4),null==n){var i=e&&e.constructor;n=a?r?new i:[]:Jo(e)&&Ko(i)?Un(We(e)):{}}return(a?Et:yr)(e,(function(e,r,a){return t(n,e,r,a)})),n},jn.unary=function(e){return Co(e,1)},jn.union=Ji,jn.unionBy=eo,jn.unionWith=to,jn.uniq=function(e){return e&&e.length?ua(e):[]},jn.uniqBy=function(e,t){return e&&e.length?ua(e,ii(t,2)):[]},jn.uniqWith=function(e,t){return t="function"==typeof t?t:a,e&&e.length?ua(e,a,t):[]},jn.unset=function(e,t){return null==e||la(e,t)},jn.unzip=no,jn.unzipWith=ro,jn.update=function(e,t,n){return null==e?e:sa(e,t,ga(n))},jn.updateWith=function(e,t,n,r){return r="function"==typeof r?r:a,null==e?e:sa(e,t,ga(n),r)},jn.values=Uu,jn.valuesIn=function(e){return null==e?[]:Kt(e,Nu(e))},jn.without=ao,jn.words=Ku,jn.wrap=function(e,t){return Do(ga(t),e)},jn.xor=io,jn.xorBy=oo,jn.xorWith=uo,jn.zip=lo,jn.zipObject=function(e,t){return pa(e||[],t||[],Jn)},jn.zipObjectDeep=function(e,t){return pa(e||[],t||[],Kr)},jn.zipWith=so,jn.entries=Ru,jn.entriesIn=ju,jn.extend=bu,jn.extendWith=wu,ol(jn,jn),jn.add=ml,jn.attempt=Xu,jn.camelCase=Iu,jn.capitalize=$u,jn.ceil=yl,jn.clamp=function(e,t,n){return n===a&&(n=t,t=a),n!==a&&(n=(n=gu(n))==n?n:0),t!==a&&(t=(t=gu(t))==t?t:0),ir(gu(e),t,n)},jn.clone=function(e){return or(e,4)},jn.cloneDeep=function(e){return or(e,5)},jn.cloneDeepWith=function(e,t){return or(e,5,t="function"==typeof t?t:a)},jn.cloneWith=function(e,t){return or(e,4,t="function"==typeof t?t:a)},jn.conformsTo=function(e,t){return null==t||ur(e,t,Mu(t))},jn.deburr=Bu,jn.defaultTo=function(e,t){return null==e||e!=e?t:e},jn.divide=bl,jn.endsWith=function(e,t,n){e=mu(e),t=oa(t);var r=e.length,i=n=n===a?r:ir(pu(n),0,r);return(n-=t.length)>=0&&e.slice(n,i)==t},jn.eq=Uo,jn.escape=function(e){return(e=mu(e))&&Y.test(e)?e.replace(q,tn):e},jn.escapeRegExp=function(e){return(e=mu(e))&&ne.test(e)?e.replace(te,"\\$&"):e},jn.every=function(e,t,n){var r=Ho(e)?Tt:dr;return n&&vi(e,t,n)&&(t=a),r(e,ii(t,3))},jn.find=go,jn.findIndex=$i,jn.findKey=function(e,t){return Rt(e,ii(t,3),yr)},jn.findLast=vo,jn.findLastIndex=Bi,jn.findLastKey=function(e,t){return Rt(e,ii(t,3),br)},jn.floor=wl,jn.forEach=mo,jn.forEachRight=yo,jn.forIn=function(e,t){return null==e?e:vr(e,ii(t,3),Nu)},jn.forInRight=function(e,t){return null==e?e:mr(e,ii(t,3),Nu)},jn.forOwn=function(e,t){return e&&yr(e,ii(t,3))},jn.forOwnRight=function(e,t){return e&&br(e,ii(t,3))},jn.get=Eu,jn.gt=Io,jn.gte=$o,jn.has=function(e,t){return null!=e&&di(e,t,Er)},jn.hasIn=Cu,jn.head=Wi,jn.identity=nl,jn.includes=function(e,t,n,r){e=Vo(e)?e:Uu(e),n=n&&!r?pu(n):0;var a=e.length;return n<0&&(n=vn(a+n,0)),ou(e)?n<=a&&e.indexOf(t,n)>-1:!!a&&Ut(e,t,n)>-1},jn.indexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var a=null==n?0:pu(n);return a<0&&(a=vn(r+a,0)),Ut(e,t,a)},jn.inRange=function(e,t,n){return t=du(t),n===a?(n=t,t=0):n=du(n),function(e,t,n){return e>=mn(t,n)&&e<vn(t,n)}(e=gu(e),t,n)},jn.invoke=zu,jn.isArguments=Bo,jn.isArray=Ho,jn.isArrayBuffer=Wo,jn.isArrayLike=Vo,jn.isArrayLikeObject=qo,jn.isBoolean=function(e){return!0===e||!1===e||eu(e)&&xr(e)==m},jn.isBuffer=Qo,jn.isDate=Yo,jn.isElement=function(e){return eu(e)&&1===e.nodeType&&!ru(e)},jn.isEmpty=function(e){if(null==e)return!0;if(Vo(e)&&(Ho(e)||"string"==typeof e||"function"==typeof e.splice||Qo(e)||lu(e)||Bo(e)))return!e.length;var t=fi(e);if(t==k||t==T)return!e.size;if(wi(e))return!Lr(e).length;for(var n in e)if(Oe.call(e,n))return!1;return!0},jn.isEqual=function(e,t){return Mr(e,t)},jn.isEqualWith=function(e,t,n){var r=(n="function"==typeof n?n:a)?n(e,t):a;return r===a?Mr(e,t,a,n):!!r},jn.isError=Go,jn.isFinite=function(e){return"number"==typeof e&&Dt(e)},jn.isFunction=Ko,jn.isInteger=Xo,jn.isLength=Zo,jn.isMap=tu,jn.isMatch=function(e,t){return e===t||Nr(e,t,ui(t))},jn.isMatchWith=function(e,t,n){return n="function"==typeof n?n:a,Nr(e,t,ui(t),n)},jn.isNaN=function(e){return nu(e)&&e!=+e},jn.isNative=function(e){if(bi(e))throw new _e("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return Ar(e)},jn.isNil=function(e){return null==e},jn.isNull=function(e){return null===e},jn.isNumber=nu,jn.isObject=Jo,jn.isObjectLike=eu,jn.isPlainObject=ru,jn.isRegExp=au,jn.isSafeInteger=function(e){return Xo(e)&&e>=-9007199254740991&&e<=f},jn.isSet=iu,jn.isString=ou,jn.isSymbol=uu,jn.isTypedArray=lu,jn.isUndefined=function(e){return e===a},jn.isWeakMap=function(e){return eu(e)&&fi(e)==M},jn.isWeakSet=function(e){return eu(e)&&"[object WeakSet]"==xr(e)},jn.join=function(e,t){return null==e?"":Wt.call(e,t)},jn.kebabCase=Hu,jn.last=Yi,jn.lastIndexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=r;return n!==a&&(i=(i=pu(n))<0?vn(r+i,0):mn(i,r-1)),t==t?function(e,t,n){for(var r=n+1;r--;)if(e[r]===t)return r;return r}(e,t,i):jt(e,$t,i,!0)},jn.lowerCase=Wu,jn.lowerFirst=Vu,jn.lt=su,jn.lte=cu,jn.max=function(e){return e&&e.length?pr(e,nl,Sr):a},jn.maxBy=function(e,t){return e&&e.length?pr(e,ii(t,2),Sr):a},jn.mean=function(e){return Bt(e,nl)},jn.meanBy=function(e,t){return Bt(e,ii(t,2))},jn.min=function(e){return e&&e.length?pr(e,nl,Fr):a},jn.minBy=function(e,t){return e&&e.length?pr(e,ii(t,2),Fr):a},jn.stubArray=hl,jn.stubFalse=gl,jn.stubObject=function(){return{}},jn.stubString=function(){return""},jn.stubTrue=function(){return!0},jn.multiply=_l,jn.nth=function(e,t){return e&&e.length?Ir(e,pu(t)):a},jn.noConflict=function(){return ft._===this&&(ft._=je),this},jn.noop=ul,jn.now=Eo,jn.pad=function(e,t,n){e=mu(e);var r=(t=pu(t))?cn(e):0;if(!t||r>=t)return e;var a=(t-r)/2;return $a(pt(a),n)+e+$a(dt(a),n)},jn.padEnd=function(e,t,n){e=mu(e);var r=(t=pu(t))?cn(e):0;return t&&r<t?e+$a(t-r,n):e},jn.padStart=function(e,t,n){e=mu(e);var r=(t=pu(t))?cn(e):0;return t&&r<t?$a(t-r,n)+e:e},jn.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),bn(mu(e).replace(re,""),t||0)},jn.random=function(e,t,n){if(n&&"boolean"!=typeof n&&vi(e,t,n)&&(t=n=a),n===a&&("boolean"==typeof t?(n=t,t=a):"boolean"==typeof e&&(n=e,e=a)),e===a&&t===a?(e=0,t=1):(e=du(e),t===a?(t=e,e=0):t=du(t)),e>t){var r=e;e=t,t=r}if(n||e%1||t%1){var i=wn();return mn(e+i*(t-e+ut("1e-"+((i+"").length-1))),t)}return Vr(e,t)},jn.reduce=function(e,t,n){var r=Ho(e)?Ot:Vt,a=arguments.length<3;return r(e,ii(t,4),n,a,cr)},jn.reduceRight=function(e,t,n){var r=Ho(e)?Lt:Vt,a=arguments.length<3;return r(e,ii(t,4),n,a,fr)},jn.repeat=function(e,t,n){return t=(n?vi(e,t,n):t===a)?1:pu(t),qr(mu(e),t)},jn.replace=function(){var e=arguments,t=mu(e[0]);return e.length<3?t:t.replace(e[1],e[2])},jn.result=function(e,t,n){var r=-1,i=(t=va(t,e)).length;for(i||(i=1,e=a);++r<i;){var o=null==e?a:e[Fi(t[r])];o===a&&(r=i,o=n),e=Ko(o)?o.call(e):o}return e},jn.round=kl,jn.runInContext=e,jn.sample=function(e){return(Ho(e)?Gn:Yr)(e)},jn.size=function(e){if(null==e)return 0;if(Vo(e))return ou(e)?cn(e):e.length;var t=fi(e);return t==k||t==T?e.size:Lr(e).length},jn.snakeCase=qu,jn.some=function(e,t,n){var r=Ho(e)?Ft:ta;return n&&vi(e,t,n)&&(t=a),r(e,ii(t,3))},jn.sortedIndex=function(e,t){return na(e,t)},jn.sortedIndexBy=function(e,t,n){return ra(e,t,ii(n,2))},jn.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var r=na(e,t);if(r<n&&Uo(e[r],t))return r}return-1},jn.sortedLastIndex=function(e,t){return na(e,t,!0)},jn.sortedLastIndexBy=function(e,t,n){return ra(e,t,ii(n,2),!0)},jn.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=na(e,t,!0)-1;if(Uo(e[n],t))return n}return-1},jn.startCase=Qu,jn.startsWith=function(e,t,n){return e=mu(e),n=null==n?0:ir(pu(n),0,e.length),t=oa(t),e.slice(n,n+t.length)==t},jn.subtract=xl,jn.sum=function(e){return e&&e.length?qt(e,nl):0},jn.sumBy=function(e,t){return e&&e.length?qt(e,ii(t,2)):0},jn.template=function(e,t,n){var r=jn.templateSettings;n&&vi(e,t,n)&&(t=a),e=mu(e),t=wu({},t,r,Ga);var i,o,u=wu({},t.imports,r.imports,Ga),l=Mu(u),s=Kt(u,l),c=0,f=t.interpolate||be,d="__p += '",p=Ee((t.escape||be).source+"|"+f.source+"|"+(f===X?fe:be).source+"|"+(t.evaluate||be).source+"|$","g"),h="//# sourceURL="+(Oe.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++rt+"]")+"\n";e.replace(p,(function(t,n,r,a,u,l){return r||(r=a),d+=e.slice(c,l).replace(we,nn),n&&(i=!0,d+="' +\n__e("+n+") +\n'"),u&&(o=!0,d+="';\n"+u+";\n__p += '"),r&&(d+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),c=l+t.length,t})),d+="';\n";var g=Oe.call(t,"variable")&&t.variable;if(g){if(se.test(g))throw new _e("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(o?d.replace(B,""):d).replace(H,"$1").replace(W,"$1;"),d="function("+(g||"obj")+") {\n"+(g?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(i?", __e = _.escape":"")+(o?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var v=Xu((function(){return ke(l,h+"return "+d).apply(a,s)}));if(v.source=d,Go(v))throw v;return v},jn.times=function(e,t){if((e=pu(e))<1||e>f)return[];var n=p,r=mn(e,p);t=ii(t),e-=p;for(var a=Qt(r,t);++n<e;)t(n);return a},jn.toFinite=du,jn.toInteger=pu,jn.toLength=hu,jn.toLower=function(e){return mu(e).toLowerCase()},jn.toNumber=gu,jn.toSafeInteger=function(e){return e?ir(pu(e),-9007199254740991,f):0===e?e:0},jn.toString=mu,jn.toUpper=function(e){return mu(e).toUpperCase()},jn.trim=function(e,t,n){if((e=mu(e))&&(n||t===a))return Yt(e);if(!e||!(t=oa(t)))return e;var r=fn(e),i=fn(t);return ya(r,Zt(r,i),Jt(r,i)+1).join("")},jn.trimEnd=function(e,t,n){if((e=mu(e))&&(n||t===a))return e.slice(0,dn(e)+1);if(!e||!(t=oa(t)))return e;var r=fn(e);return ya(r,0,Jt(r,fn(t))+1).join("")},jn.trimStart=function(e,t,n){if((e=mu(e))&&(n||t===a))return e.replace(re,"");if(!e||!(t=oa(t)))return e;var r=fn(e);return ya(r,Zt(r,fn(t))).join("")},jn.truncate=function(e,t){var n=30,r="...";if(Jo(t)){var i="separator"in t?t.separator:i;n="length"in t?pu(t.length):n,r="omission"in t?oa(t.omission):r}var o=(e=mu(e)).length;if(rn(e)){var u=fn(e);o=u.length}if(n>=o)return e;var l=n-cn(r);if(l<1)return r;var s=u?ya(u,0,l).join(""):e.slice(0,l);if(i===a)return s+r;if(u&&(l+=s.length-l),au(i)){if(e.slice(l).search(i)){var c,f=s;for(i.global||(i=Ee(i.source,mu(de.exec(i))+"g")),i.lastIndex=0;c=i.exec(f);)var d=c.index;s=s.slice(0,d===a?l:d)}}else if(e.indexOf(oa(i),l)!=l){var p=s.lastIndexOf(i);p>-1&&(s=s.slice(0,p))}return s+r},jn.unescape=function(e){return(e=mu(e))&&Q.test(e)?e.replace(V,pn):e},jn.uniqueId=function(e){var t=++Le;return mu(e)+t},jn.upperCase=Yu,jn.upperFirst=Gu,jn.each=mo,jn.eachRight=yo,jn.first=Wi,ol(jn,(vl={},yr(jn,(function(e,t){Oe.call(jn.prototype,t)||(vl[t]=e)})),vl),{chain:!1}),jn.VERSION="4.17.21",Et(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){jn[e].placeholder=jn})),Et(["drop","take"],(function(e,t){Bn.prototype[e]=function(n){n=n===a?1:vn(pu(n),0);var r=this.__filtered__&&!t?new Bn(this):this.clone();return r.__filtered__?r.__takeCount__=mn(n,r.__takeCount__):r.__views__.push({size:mn(n,p),type:e+(r.__dir__<0?"Right":"")}),r},Bn.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}})),Et(["filter","map","takeWhile"],(function(e,t){var n=t+1,r=1==n||3==n;Bn.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:ii(e,3),type:n}),t.__filtered__=t.__filtered__||r,t}})),Et(["head","last"],(function(e,t){var n="take"+(t?"Right":"");Bn.prototype[e]=function(){return this[n](1).value()[0]}})),Et(["initial","tail"],(function(e,t){var n="drop"+(t?"":"Right");Bn.prototype[e]=function(){return this.__filtered__?new Bn(this):this[n](1)}})),Bn.prototype.compact=function(){return this.filter(nl)},Bn.prototype.find=function(e){return this.filter(e).head()},Bn.prototype.findLast=function(e){return this.reverse().find(e)},Bn.prototype.invokeMap=Qr((function(e,t){return"function"==typeof e?new Bn(this):this.map((function(n){return Pr(n,e,t)}))})),Bn.prototype.reject=function(e){return this.filter(Lo(ii(e)))},Bn.prototype.slice=function(e,t){e=pu(e);var n=this;return n.__filtered__&&(e>0||t<0)?new Bn(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==a&&(n=(t=pu(t))<0?n.dropRight(-t):n.take(t-e)),n)},Bn.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},Bn.prototype.toArray=function(){return this.take(p)},yr(Bn.prototype,(function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),r=/^(?:head|last)$/.test(t),i=jn[r?"take"+("last"==t?"Right":""):t],o=r||/^find/.test(t);i&&(jn.prototype[t]=function(){var t=this.__wrapped__,u=r?[1]:arguments,l=t instanceof Bn,s=u[0],c=l||Ho(t),f=function(e){var t=i.apply(jn,At([e],u));return r&&d?t[0]:t};c&&n&&"function"==typeof s&&1!=s.length&&(l=c=!1);var d=this.__chain__,p=!!this.__actions__.length,h=o&&!d,g=l&&!p;if(!o&&c){t=g?t:new Bn(this);var v=e.apply(t,u);return v.__actions__.push({func:fo,args:[f],thisArg:a}),new $n(v,d)}return h&&g?e.apply(this,u):(v=this.thru(f),h?r?v.value()[0]:v.value():v)})})),Et(["pop","push","shift","sort","splice","unshift"],(function(e){var t=Pe[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);jn.prototype[e]=function(){var e=arguments;if(r&&!this.__chain__){var a=this.value();return t.apply(Ho(a)?a:[],e)}return this[n]((function(n){return t.apply(Ho(n)?n:[],e)}))}})),yr(Bn.prototype,(function(e,t){var n=jn[t];if(n){var r=n.name+"";Oe.call(zn,r)||(zn[r]=[]),zn[r].push({name:t,func:n})}})),zn[Ra(a,2).name]=[{name:"wrapper",func:a}],Bn.prototype.clone=function(){var e=new Bn(this.__wrapped__);return e.__actions__=Ca(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=Ca(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=Ca(this.__views__),e},Bn.prototype.reverse=function(){if(this.__filtered__){var e=new Bn(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},Bn.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Ho(e),r=t<0,a=n?e.length:0,i=function(e,t,n){for(var r=-1,a=n.length;++r<a;){var i=n[r],o=i.size;switch(i.type){case"drop":e+=o;break;case"dropRight":t-=o;break;case"take":t=mn(t,e+o);break;case"takeRight":e=vn(e,t-o)}}return{start:e,end:t}}(0,a,this.__views__),o=i.start,u=i.end,l=u-o,s=r?u:o-1,c=this.__iteratees__,f=c.length,d=0,p=mn(l,this.__takeCount__);if(!n||!r&&a==l&&p==l)return fa(e,this.__actions__);var h=[];e:for(;l--&&d<p;){for(var g=-1,v=e[s+=t];++g<f;){var m=c[g],y=m.iteratee,b=m.type,w=y(v);if(2==b)v=w;else if(!w){if(1==b)continue e;break e}}h[d++]=v}return h},jn.prototype.at=po,jn.prototype.chain=function(){return co(this)},jn.prototype.commit=function(){return new $n(this.value(),this.__chain__)},jn.prototype.next=function(){this.__values__===a&&(this.__values__=fu(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?a:this.__values__[this.__index__++]}},jn.prototype.plant=function(e){for(var t,n=this;n instanceof In;){var r=Ri(n);r.__index__=0,r.__values__=a,t?i.__wrapped__=r:t=r;var i=r;n=n.__wrapped__}return i.__wrapped__=e,t},jn.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof Bn){var t=e;return this.__actions__.length&&(t=new Bn(this)),(t=t.reverse()).__actions__.push({func:fo,args:[Zi],thisArg:a}),new $n(t,this.__chain__)}return this.thru(Zi)},jn.prototype.toJSON=jn.prototype.valueOf=jn.prototype.value=function(){return fa(this.__wrapped__,this.__actions__)},jn.prototype.first=jn.prototype.head,Ge&&(jn.prototype[Ge]=function(){return this}),jn}();ft._=hn,(r=function(){return hn}.call(t,n,t,e))===a||(e.exports=r)}.call(this)},869:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),u=Symbol.for("react.consumer"),l=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),p=Symbol.iterator,h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g=Object.assign,v={};function m(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}m.prototype.isReactComponent={},m.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},m.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=m.prototype;var w=b.prototype=new y;w.constructor=b,g(w,m.prototype),w.isPureReactComponent=!0;var _=Array.isArray,k={H:null,A:null,T:null,S:null,V:null},x=Object.prototype.hasOwnProperty;function S(e,t,r,a,i,o){return r=o.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:o}}function E(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function T(e,t){return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,r={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var n,r}function P(){}function z(e,t,a,i,o){var u=typeof e;"undefined"!==u&&"boolean"!==u||(e=null);var l,s,c=!1;if(null===e)c=!0;else switch(u){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case n:case r:c=!0;break;case d:return z((c=e._init)(e._payload),t,a,i,o)}}if(c)return o=o(e),c=""===i?"."+T(e,0):i,_(o)?(a="",null!=c&&(a=c.replace(C,"$&/")+"/"),z(o,t,a,"",(function(e){return e}))):null!=o&&(E(o)&&(l=o,s=a+(null==o.key||e&&e.key===o.key?"":(""+o.key).replace(C,"$&/")+"/")+c,o=S(l.type,s,void 0,0,0,l.props)),t.push(o)),1;c=0;var f,h=""===i?".":i+":";if(_(e))for(var g=0;g<e.length;g++)c+=z(i=e[g],t,a,u=h+T(i,g),o);else if("function"==typeof(g=null===(f=e)||"object"!=typeof f?null:"function"==typeof(f=p&&f[p]||f["@@iterator"])?f:null))for(e=g.call(e),g=0;!(i=e.next()).done;)c+=z(i=i.value,t,a,u=h+T(i,g++),o);else if("object"===u){if("function"==typeof e.then)return z(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(P,P):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,i,o);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return c}function M(e,t,n){if(null==e)return e;var r=[],a=0;return z(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function N(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var A="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function O(){}t.Children={map:M,forEach:function(e,t,n){M(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return M(e,(function(){t++})),t},toArray:function(e){return M(e,(function(e){return e}))||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=m,t.Fragment=a,t.Profiler=o,t.PureComponent=b,t.StrictMode=i,t.Suspense=c,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return k.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var r=g({},e.props),a=e.key;if(null!=t)for(i in t.ref,void 0!==t.key&&(a=""+t.key),t)!x.call(t,i)||"key"===i||"__self"===i||"__source"===i||"ref"===i&&void 0===t.ref||(r[i]=t[i]);var i=arguments.length-2;if(1===i)r.children=n;else if(1<i){for(var o=Array(i),u=0;u<i;u++)o[u]=arguments[u+2];r.children=o}return S(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:u,_context:e},e},t.createElement=function(e,t,n){var r,a={},i=null;if(null!=t)for(r in void 0!==t.key&&(i=""+t.key),t)x.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var o=arguments.length-2;if(1===o)a.children=n;else if(1<o){for(var u=Array(o),l=0;l<o;l++)u[l]=arguments[l+2];a.children=u}if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===a[r]&&(a[r]=o[r]);return S(e,i,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:s,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:d,_payload:{_status:-1,_result:e},_init:N}},t.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=k.T,n={};k.T=n;try{var r=e(),a=k.S;null!==a&&a(n,r),"object"==typeof r&&null!==r&&"function"==typeof r.then&&r.then(O,A)}catch(e){A(e)}finally{k.T=t}},t.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},t.use=function(e){return k.H.use(e)},t.useActionState=function(e,t,n){return k.H.useActionState(e,t,n)},t.useCallback=function(e,t){return k.H.useCallback(e,t)},t.useContext=function(e){return k.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return k.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=k.H;if("function"==typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return k.H.useId()},t.useImperativeHandle=function(e,t,n){return k.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return k.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return k.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return k.H.useMemo(e,t)},t.useOptimistic=function(e,t){return k.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return k.H.useReducer(e,t,n)},t.useRef=function(e){return k.H.useRef(e)},t.useState=function(e){return k.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return k.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return k.H.useTransition()},t.version="19.1.0"},961:(e,t,n)=>{"use strict";!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(221)},982:(e,t,n)=>{"use strict";e.exports=n(477)}},r={};function a(e){var t=r[e];if(void 0!==t)return t.exports;var i=r[e]={id:e,loaded:!1,exports:{}};return n[e].call(i.exports,i,i.exports,a),i.loaded=!0,i.exports}t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,a.t=function(n,r){if(1&r&&(n=this(n)),8&r)return n;if("object"==typeof n&&n){if(4&r&&n.__esModule)return n;if(16&r&&"function"==typeof n.then)return n}var i=Object.create(null);a.r(i);var o={};e=e||[null,t({}),t([]),t(t)];for(var u=2&r&&n;"object"==typeof u&&!~e.indexOf(u);u=t(u))Object.getOwnPropertyNames(u).forEach((e=>o[e]=()=>n[e]));return o.default=()=>n,a.d(i,o),i},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{"use strict";var e=a(540),t=a(338),n=a.t(t,2);const r=Math.sqrt(50),i=Math.sqrt(10),o=Math.sqrt(2);function u(e,t,n){const a=(t-e)/Math.max(0,n),l=Math.floor(Math.log10(a)),s=a/Math.pow(10,l),c=s>=r?10:s>=i?5:s>=o?2:1;let f,d,p;return l<0?(p=Math.pow(10,-l)/c,f=Math.round(e*p),d=Math.round(t*p),f/p<e&&++f,d/p>t&&--d,p=-p):(p=Math.pow(10,l)*c,f=Math.round(e/p),d=Math.round(t/p),f*p<e&&++f,d*p>t&&--d),d<f&&.5<=n&&n<2?u(e,t,2*n):[f,d,p]}function l(e,t,n){return u(e=+e,t=+t,n=+n)[2]}function s(e,t,n){n=+n;const r=(t=+t)<(e=+e),a=r?l(t,e,n):l(e,t,n);return(r?-1:1)*(a<0?1/-a:a)}function c(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function f(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function d(e){let t,n,r;function a(e,r,a=0,i=e.length){if(a<i){if(0!==t(r,r))return i;do{const t=a+i>>>1;n(e[t],r)<0?a=t+1:i=t}while(a<i)}return a}return 2!==e.length?(t=c,n=(t,n)=>c(e(t),n),r=(t,n)=>e(t)-n):(t=e===c||e===f?e:p,n=e,r=e),{left:a,center:function(e,t,n=0,i=e.length){const o=a(e,t,n,i-1);return o>n&&r(e[o-1],t)>-r(e[o],t)?o-1:o},right:function(e,r,a=0,i=e.length){if(a<i){if(0!==t(r,r))return i;do{const t=a+i>>>1;n(e[t],r)<=0?a=t+1:i=t}while(a<i)}return a}}}function p(){return 0}const h=d(c),g=h.right,v=(h.left,d((function(e){return null===e?NaN:+e})).center,g);function m(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function y(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function b(){}var w=.7,_=1/w,k="\\s*([+-]?\\d+)\\s*",x="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",S="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",E=/^#([0-9a-f]{3,8})$/,C=new RegExp(`^rgb\\(${k},${k},${k}\\)$`),T=new RegExp(`^rgb\\(${S},${S},${S}\\)$`),P=new RegExp(`^rgba\\(${k},${k},${k},${x}\\)$`),z=new RegExp(`^rgba\\(${S},${S},${S},${x}\\)$`),M=new RegExp(`^hsl\\(${x},${S},${S}\\)$`),N=new RegExp(`^hsla\\(${x},${S},${S},${x}\\)$`),A={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function O(){return this.rgb().formatHex()}function L(){return this.rgb().formatRgb()}function F(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=E.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?D(t):3===n?new U(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?R(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?R(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=C.exec(e))?new U(t[1],t[2],t[3],1):(t=T.exec(e))?new U(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=P.exec(e))?R(t[1],t[2],t[3],t[4]):(t=z.exec(e))?R(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=M.exec(e))?V(t[1],t[2]/100,t[3]/100,1):(t=N.exec(e))?V(t[1],t[2]/100,t[3]/100,t[4]):A.hasOwnProperty(e)?D(A[e]):"transparent"===e?new U(NaN,NaN,NaN,0):null}function D(e){return new U(e>>16&255,e>>8&255,255&e,1)}function R(e,t,n,r){return r<=0&&(e=t=n=NaN),new U(e,t,n,r)}function j(e,t,n,r){return 1===arguments.length?((a=e)instanceof b||(a=F(a)),a?new U((a=a.rgb()).r,a.g,a.b,a.opacity):new U):new U(e,t,n,null==r?1:r);var a}function U(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}function I(){return`#${W(this.r)}${W(this.g)}${W(this.b)}`}function $(){const e=B(this.opacity);return`${1===e?"rgb(":"rgba("}${H(this.r)}, ${H(this.g)}, ${H(this.b)}${1===e?")":`, ${e})`}`}function B(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function H(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function W(e){return((e=H(e))<16?"0":"")+e.toString(16)}function V(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Y(e,t,n,r)}function q(e){if(e instanceof Y)return new Y(e.h,e.s,e.l,e.opacity);if(e instanceof b||(e=F(e)),!e)return new Y;if(e instanceof Y)return e;var t=(e=e.rgb()).r/255,n=e.g/255,r=e.b/255,a=Math.min(t,n,r),i=Math.max(t,n,r),o=NaN,u=i-a,l=(i+a)/2;return u?(o=t===i?(n-r)/u+6*(n<r):n===i?(r-t)/u+2:(t-n)/u+4,u/=l<.5?i+a:2-i-a,o*=60):u=l>0&&l<1?0:o,new Y(o,u,l,e.opacity)}function Q(e,t,n,r){return 1===arguments.length?q(e):new Y(e,t,n,null==r?1:r)}function Y(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}function G(e){return(e=(e||0)%360)<0?e+360:e}function K(e){return Math.max(0,Math.min(1,e||0))}function X(e,t,n){return 255*(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)}function Z(e,t,n,r,a){var i=e*e,o=i*e;return((1-3*e+3*i-o)*t+(4-6*i+3*o)*n+(1+3*e+3*i-3*o)*r+o*a)/6}m(b,F,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:O,formatHex:O,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return q(this).formatHsl()},formatRgb:L,toString:L}),m(U,j,y(b,{brighter(e){return e=null==e?_:Math.pow(_,e),new U(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?w:Math.pow(w,e),new U(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new U(H(this.r),H(this.g),H(this.b),B(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:I,formatHex:I,formatHex8:function(){return`#${W(this.r)}${W(this.g)}${W(this.b)}${W(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:$,toString:$})),m(Y,Q,y(b,{brighter(e){return e=null==e?_:Math.pow(_,e),new Y(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?w:Math.pow(w,e),new Y(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,a=2*n-r;return new U(X(e>=240?e-240:e+120,a,r),X(e,a,r),X(e<120?e+240:e-120,a,r),this.opacity)},clamp(){return new Y(G(this.h),K(this.s),K(this.l),B(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=B(this.opacity);return`${1===e?"hsl(":"hsla("}${G(this.h)}, ${100*K(this.s)}%, ${100*K(this.l)}%${1===e?")":`, ${e})`}`}}));const J=e=>()=>e;function ee(e,t){var n=t-e;return n?function(e,t){return function(n){return e+n*t}}(e,n):J(isNaN(e)?t:e)}const te=function e(t){var n=function(e){return 1==(e=+e)?ee:function(t,n){return n-t?function(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}(t,n,e):J(isNaN(t)?n:t)}}(t);function r(e,t){var r=n((e=j(e)).r,(t=j(t)).r),a=n(e.g,t.g),i=n(e.b,t.b),o=ee(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=a(t),e.b=i(t),e.opacity=o(t),e+""}}return r.gamma=e,r}(1);function ne(e){return function(t){var n,r,a=t.length,i=new Array(a),o=new Array(a),u=new Array(a);for(n=0;n<a;++n)r=j(t[n]),i[n]=r.r||0,o[n]=r.g||0,u[n]=r.b||0;return i=e(i),o=e(o),u=e(u),r.opacity=1,function(e){return r.r=i(e),r.g=o(e),r.b=u(e),r+""}}}function re(e,t){var n,r=t?t.length:0,a=e?Math.min(r,e.length):0,i=new Array(a),o=new Array(r);for(n=0;n<a;++n)i[n]=fe(e[n],t[n]);for(;n<r;++n)o[n]=t[n];return function(e){for(n=0;n<a;++n)o[n]=i[n](e);return o}}function ae(e,t){var n=new Date;return e=+e,t=+t,function(r){return n.setTime(e*(1-r)+t*r),n}}function ie(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}function oe(e,t){var n,r={},a={};for(n in null!==e&&"object"==typeof e||(e={}),null!==t&&"object"==typeof t||(t={}),t)n in e?r[n]=fe(e[n],t[n]):a[n]=t[n];return function(e){for(n in r)a[n]=r[n](e);return a}}ne((function(e){var t=e.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,t-1):Math.floor(n*t),a=e[r],i=e[r+1],o=r>0?e[r-1]:2*a-i,u=r<t-1?e[r+2]:2*i-a;return Z((n-r/t)*t,o,a,i,u)}})),ne((function(e){var t=e.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*t),a=e[(r+t-1)%t],i=e[r%t],o=e[(r+1)%t],u=e[(r+2)%t];return Z((n-r/t)*t,a,i,o,u)}}));var ue=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,le=new RegExp(ue.source,"g");function se(e,t){var n,r,a,i=ue.lastIndex=le.lastIndex=0,o=-1,u=[],l=[];for(e+="",t+="";(n=ue.exec(e))&&(r=le.exec(t));)(a=r.index)>i&&(a=t.slice(i,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(r=r[0])?u[o]?u[o]+=r:u[++o]=r:(u[++o]=null,l.push({i:o,x:ie(n,r)})),i=le.lastIndex;return i<t.length&&(a=t.slice(i),u[o]?u[o]+=a:u[++o]=a),u.length<2?l[0]?function(e){return function(t){return e(t)+""}}(l[0].x):function(e){return function(){return e}}(t):(t=l.length,function(e){for(var n,r=0;r<t;++r)u[(n=l[r]).i]=n.x(e);return u.join("")})}function ce(e,t){t||(t=[]);var n,r=e?Math.min(t.length,e.length):0,a=t.slice();return function(i){for(n=0;n<r;++n)a[n]=e[n]*(1-i)+t[n]*i;return a}}function fe(e,t){var n,r,a=typeof t;return null==t||"boolean"===a?J(t):("number"===a?ie:"string"===a?(n=F(t))?(t=n,te):se:t instanceof F?te:t instanceof Date?ae:(r=t,!ArrayBuffer.isView(r)||r instanceof DataView?Array.isArray(t)?re:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?oe:ie:ce))(e,t)}function de(e,t){return e=+e,t=+t,function(n){return Math.round(e*(1-n)+t*n)}}function pe(e){return+e}var he=[0,1];function ge(e){return e}function ve(e,t){return(t-=e=+e)?function(n){return(n-e)/t}:(n=isNaN(t)?NaN:.5,function(){return n});var n}function me(e,t,n){var r=e[0],a=e[1],i=t[0],o=t[1];return a<r?(r=ve(a,r),i=n(o,i)):(r=ve(r,a),i=n(i,o)),function(e){return i(r(e))}}function ye(e,t,n){var r=Math.min(e.length,t.length)-1,a=new Array(r),i=new Array(r),o=-1;for(e[r]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<r;)a[o]=ve(e[o],e[o+1]),i[o]=n(t[o],t[o+1]);return function(t){var n=v(e,t,1,r)-1;return i[n](a[n](t))}}function be(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function we(){return function(){var e,t,n,r,a,i,o=he,u=he,l=fe,s=ge;function c(){var e,t,n,l=Math.min(o.length,u.length);return s!==ge&&(e=o[0],t=o[l-1],e>t&&(n=e,e=t,t=n),s=function(n){return Math.max(e,Math.min(t,n))}),r=l>2?ye:me,a=i=null,f}function f(t){return null==t||isNaN(t=+t)?n:(a||(a=r(o.map(e),u,l)))(e(s(t)))}return f.invert=function(n){return s(t((i||(i=r(u,o.map(e),ie)))(n)))},f.domain=function(e){return arguments.length?(o=Array.from(e,pe),c()):o.slice()},f.range=function(e){return arguments.length?(u=Array.from(e),c()):u.slice()},f.rangeRound=function(e){return u=Array.from(e),l=de,c()},f.clamp=function(e){return arguments.length?(s=!!e||ge,c()):s!==ge},f.interpolate=function(e){return arguments.length?(l=e,c()):l},f.unknown=function(e){return arguments.length?(n=e,f):n},function(n,r){return e=n,t=r,c()}}()(ge,ge)}function _e(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}var ke,xe=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Se(e){if(!(t=xe.exec(e)))throw new Error("invalid format: "+e);var t;return new Ee({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function Ee(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function Ce(e,t){if((n=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var n,r=e.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+e.slice(n+1)]}function Te(e){return(e=Ce(Math.abs(e)))?e[1]:NaN}function Pe(e,t){var n=Ce(e,t);if(!n)return e+"";var r=n[0],a=n[1];return a<0?"0."+new Array(-a).join("0")+r:r.length>a+1?r.slice(0,a+1)+"."+r.slice(a+1):r+new Array(a-r.length+2).join("0")}Se.prototype=Ee.prototype,Ee.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const ze={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Pe(100*e,t),r:Pe,s:function(e,t){var n=Ce(e,t);if(!n)return e+"";var r=n[0],a=n[1],i=a-(ke=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,o=r.length;return i===o?r:i>o?r+new Array(i-o+1).join("0"):i>0?r.slice(0,i)+"."+r.slice(i):"0."+new Array(1-i).join("0")+Ce(e,Math.max(0,t+i-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Me(e){return e}var Ne,Ae,Oe,Le=Array.prototype.map,Fe=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function De(e){var t=e.domain;return e.ticks=function(e){var n=t();return function(e,t,n){if(!((n=+n)>0))return[];if((e=+e)==(t=+t))return[e];const r=t<e,[a,i,o]=r?u(t,e,n):u(e,t,n);if(!(i>=a))return[];const l=i-a+1,s=new Array(l);if(r)if(o<0)for(let e=0;e<l;++e)s[e]=(i-e)/-o;else for(let e=0;e<l;++e)s[e]=(i-e)*o;else if(o<0)for(let e=0;e<l;++e)s[e]=(a+e)/-o;else for(let e=0;e<l;++e)s[e]=(a+e)*o;return s}(n[0],n[n.length-1],null==e?10:e)},e.tickFormat=function(e,n){var r=t();return function(e,t,n,r){var a,i=s(e,t,n);switch((r=Se(null==r?",f":r)).type){case"s":var o=Math.max(Math.abs(e),Math.abs(t));return null!=r.precision||isNaN(a=function(e,t){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Te(t)/3)))-Te(Math.abs(e)))}(i,o))||(r.precision=a),Oe(r,o);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(a=function(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Te(t)-Te(e))+1}(i,Math.max(Math.abs(e),Math.abs(t))))||(r.precision=a-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(a=function(e){return Math.max(0,-Te(Math.abs(e)))}(i))||(r.precision=a-2*("%"===r.type))}return Ae(r)}(r[0],r[r.length-1],null==e?10:e,n)},e.nice=function(n){null==n&&(n=10);var r,a,i=t(),o=0,u=i.length-1,s=i[o],c=i[u],f=10;for(c<s&&(a=s,s=c,c=a,a=o,o=u,u=a);f-- >0;){if((a=l(s,c,n))===r)return i[o]=s,i[u]=c,t(i);if(a>0)s=Math.floor(s/a)*a,c=Math.ceil(c/a)*a;else{if(!(a<0))break;s=Math.ceil(s*a)/a,c=Math.floor(c*a)/a}r=a}return e},e}function Re(){var e=we();return e.copy=function(){return be(e,Re())},_e.apply(e,arguments),De(e)}Ne=function(e){var t,n,r=void 0===e.grouping||void 0===e.thousands?Me:(t=Le.call(e.grouping,Number),n=e.thousands+"",function(e,r){for(var a=e.length,i=[],o=0,u=t[0],l=0;a>0&&u>0&&(l+u+1>r&&(u=Math.max(1,r-l)),i.push(e.substring(a-=u,a+u)),!((l+=u+1)>r));)u=t[o=(o+1)%t.length];return i.reverse().join(n)}),a=void 0===e.currency?"":e.currency[0]+"",i=void 0===e.currency?"":e.currency[1]+"",o=void 0===e.decimal?".":e.decimal+"",u=void 0===e.numerals?Me:function(e){return function(t){return t.replace(/[0-9]/g,(function(t){return e[+t]}))}}(Le.call(e.numerals,String)),l=void 0===e.percent?"%":e.percent+"",s=void 0===e.minus?"−":e.minus+"",c=void 0===e.nan?"NaN":e.nan+"";function f(e){var t=(e=Se(e)).fill,n=e.align,f=e.sign,d=e.symbol,p=e.zero,h=e.width,g=e.comma,v=e.precision,m=e.trim,y=e.type;"n"===y?(g=!0,y="g"):ze[y]||(void 0===v&&(v=12),m=!0,y="g"),(p||"0"===t&&"="===n)&&(p=!0,t="0",n="=");var b="$"===d?a:"#"===d&&/[boxX]/.test(y)?"0"+y.toLowerCase():"",w="$"===d?i:/[%p]/.test(y)?l:"",_=ze[y],k=/[defgprs%]/.test(y);function x(e){var a,i,l,d=b,x=w;if("c"===y)x=_(e)+x,e="";else{var S=(e=+e)<0||1/e<0;if(e=isNaN(e)?c:_(Math.abs(e),v),m&&(e=function(e){e:for(var t,n=e.length,r=1,a=-1;r<n;++r)switch(e[r]){case".":a=t=r;break;case"0":0===a&&(a=r),t=r;break;default:if(!+e[r])break e;a>0&&(a=0)}return a>0?e.slice(0,a)+e.slice(t+1):e}(e)),S&&0==+e&&"+"!==f&&(S=!1),d=(S?"("===f?f:s:"-"===f||"("===f?"":f)+d,x=("s"===y?Fe[8+ke/3]:"")+x+(S&&"("===f?")":""),k)for(a=-1,i=e.length;++a<i;)if(48>(l=e.charCodeAt(a))||l>57){x=(46===l?o+e.slice(a+1):e.slice(a))+x,e=e.slice(0,a);break}}g&&!p&&(e=r(e,1/0));var E=d.length+e.length+x.length,C=E<h?new Array(h-E+1).join(t):"";switch(g&&p&&(e=r(C+e,C.length?h-x.length:1/0),C=""),n){case"<":e=d+e+x+C;break;case"=":e=d+C+e+x;break;case"^":e=C.slice(0,E=C.length>>1)+d+e+x+C.slice(E);break;default:e=C+d+e+x}return u(e)}return v=void 0===v?6:/[gprs]/.test(y)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),x.toString=function(){return e+""},x}return{format:f,formatPrefix:function(e,t){var n=f(((e=Se(e)).type="f",e)),r=3*Math.max(-8,Math.min(8,Math.floor(Te(t)/3))),a=Math.pow(10,-r),i=Fe[8+r/3];return function(e){return n(a*e)+i}}}}({thousands:",",grouping:[3],currency:["$",""]}),Ae=Ne.format,Oe=Ne.formatPrefix;var je=a(543);const Ue={colors:{RdBu:["rgb(255, 13, 87)","rgb(30, 136, 229)"],GnPR:["rgb(24, 196, 93)","rgb(124, 82, 255)"],CyPU:["#0099C6","#990099"],PkYg:["#DD4477","#66AA00"],DrDb:["#B82E2E","#316395"],LpLb:["#994499","#22AA99"],YlDp:["#AAAA11","#6633CC"],OrId:["#E67300","#3E0099"]},gray:"#777"};function Ie(e){return Ie="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ie(e)}function $e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Be(r.key),r)}}function Be(e){var t=function(e){if("object"!=Ie(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Ie(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Ie(t)?t:t+""}function He(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(He=function(){return!!e})()}function We(e){return We=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},We(e)}function Ve(e,t){return Ve=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ve(e,t)}var qe=function(t){function n(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),(e=function(e,t,n){return t=We(t),function(e,t){if(t&&("object"==Ie(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,He()?Reflect.construct(t,n||[],We(e).constructor):t.apply(e,n))}(this,n)).width=100,window.lastSimpleListInstance=e,e.effectFormat=Ae(".2"),e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ve(e,t)}(n,t),r=n,(a=[{key:"render",value:function(){var t=this,n=void 0;"string"==typeof this.props.plot_cmap?this.props.plot_cmap in Ue.colors?n=Ue.colors[this.props.plot_cmap]:(console.log("Invalid color map name, reverting to default."),n=Ue.colors.RdBu):Array.isArray(this.props.plot_cmap)&&(n=this.props.plot_cmap),console.log(this.props.features,this.props.features),this.scale=Re().domain([0,(0,je.max)((0,je.map)(this.props.features,(function(e){return Math.abs(e.effect)})))]).range([0,this.width]);var r=(0,je.reverse)((0,je.sortBy)(Object.keys(this.props.features),(function(e){return Math.abs(t.props.features[e].effect)}))).map((function(r){var a,i,o=t.props.features[r],u=t.props.featureNames[r],l={width:t.scale(Math.abs(o.effect)),height:"20px",background:o.effect<0?n[0]:n[1],display:"inline-block"},s={lineHeight:"20px",display:"inline-block",width:t.width+40,verticalAlign:"top",marginRight:"5px",textAlign:"right"},c={lineHeight:"20px",display:"inline-block",width:t.width+40,verticalAlign:"top",marginLeft:"5px"};return o.effect<0?(i=e.createElement("span",{style:c},u),s.width=40+t.width-t.scale(Math.abs(o.effect)),s.textAlign="right",s.color="#999",s.fontSize="13px",a=e.createElement("span",{style:s},t.effectFormat(o.effect))):(s.textAlign="right",a=e.createElement("span",{style:s},u),c.width=40,c.textAlign="left",c.color="#999",c.fontSize="13px",i=e.createElement("span",{style:c},t.effectFormat(o.effect))),e.createElement("div",{key:r,style:{marginTop:"2px"}},a,e.createElement("div",{style:l}),i)}));return e.createElement("span",null,r)}}])&&$e(r.prototype,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,a}(e.Component);qe.defaultProps={plot_cmap:"RdBu"};const Qe=qe;function Ye(){}function Ge(e){return null==e?Ye:function(){return this.querySelector(e)}}function Ke(){return[]}function Xe(e){return function(t){return t.matches(e)}}var Ze=Array.prototype.find;function Je(){return this.firstElementChild}var et=Array.prototype.filter;function tt(){return Array.from(this.children)}function nt(e){return new Array(e.length)}function rt(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function at(e,t,n,r,a,i){for(var o,u=0,l=t.length,s=i.length;u<s;++u)(o=t[u])?(o.__data__=i[u],r[u]=o):n[u]=new rt(e,i[u]);for(;u<l;++u)(o=t[u])&&(a[u]=o)}function it(e,t,n,r,a,i,o){var u,l,s,c=new Map,f=t.length,d=i.length,p=new Array(f);for(u=0;u<f;++u)(l=t[u])&&(p[u]=s=o.call(l,l.__data__,u,t)+"",c.has(s)?a[u]=l:c.set(s,l));for(u=0;u<d;++u)s=o.call(e,i[u],u,i)+"",(l=c.get(s))?(r[u]=l,l.__data__=i[u],c.delete(s)):n[u]=new rt(e,i[u]);for(u=0;u<f;++u)(l=t[u])&&c.get(p[u])===l&&(a[u]=l)}function ot(e){return e.__data__}function ut(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function lt(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}rt.prototype={constructor:rt,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};var st="http://www.w3.org/1999/xhtml";const ct={svg:"http://www.w3.org/2000/svg",xhtml:st,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function ft(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),ct.hasOwnProperty(t)?{space:ct[t],local:e}:e}function dt(e){return function(){this.removeAttribute(e)}}function pt(e){return function(){this.removeAttributeNS(e.space,e.local)}}function ht(e,t){return function(){this.setAttribute(e,t)}}function gt(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function vt(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}function mt(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function yt(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function bt(e){return function(){this.style.removeProperty(e)}}function wt(e,t,n){return function(){this.style.setProperty(e,t,n)}}function _t(e,t,n){return function(){var r=t.apply(this,arguments);null==r?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function kt(e){return function(){delete this[e]}}function xt(e,t){return function(){this[e]=t}}function St(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}function Et(e){return e.trim().split(/^|\s+/)}function Ct(e){return e.classList||new Tt(e)}function Tt(e){this._node=e,this._names=Et(e.getAttribute("class")||"")}function Pt(e,t){for(var n=Ct(e),r=-1,a=t.length;++r<a;)n.add(t[r])}function zt(e,t){for(var n=Ct(e),r=-1,a=t.length;++r<a;)n.remove(t[r])}function Mt(e){return function(){Pt(this,e)}}function Nt(e){return function(){zt(this,e)}}function At(e,t){return function(){(t.apply(this,arguments)?Pt:zt)(this,e)}}function Ot(){this.textContent=""}function Lt(e){return function(){this.textContent=e}}function Ft(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}function Dt(){this.innerHTML=""}function Rt(e){return function(){this.innerHTML=e}}function jt(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}function Ut(){this.nextSibling&&this.parentNode.appendChild(this)}function It(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function $t(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===st&&t.documentElement.namespaceURI===st?t.createElement(e):t.createElementNS(n,e)}}function Bt(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Ht(e){var t=ft(e);return(t.local?Bt:$t)(t)}function Wt(){return null}function Vt(){var e=this.parentNode;e&&e.removeChild(this)}function qt(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Qt(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Yt(e){return function(){var t=this.__on;if(t){for(var n,r=0,a=-1,i=t.length;r<i;++r)n=t[r],e.type&&n.type!==e.type||n.name!==e.name?t[++a]=n:this.removeEventListener(n.type,n.listener,n.options);++a?t.length=a:delete this.__on}}}function Gt(e,t,n){return function(){var r,a=this.__on,i=function(e){return function(t){e.call(this,t,this.__data__)}}(t);if(a)for(var o=0,u=a.length;o<u;++o)if((r=a[o]).type===e.type&&r.name===e.name)return this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=i,r.options=n),void(r.value=t);this.addEventListener(e.type,i,n),r={type:e.type,name:e.name,value:t,listener:i,options:n},a?a.push(r):this.__on=[r]}}function Kt(e,t,n){var r=yt(e),a=r.CustomEvent;"function"==typeof a?a=new a(t,n):(a=r.document.createEvent("Event"),n?(a.initEvent(t,n.bubbles,n.cancelable),a.detail=n.detail):a.initEvent(t,!1,!1)),e.dispatchEvent(a)}function Xt(e,t){return function(){return Kt(this,e,t)}}function Zt(e,t){return function(){return Kt(this,e,t.apply(this,arguments))}}Tt.prototype={add:function(e){this._names.indexOf(e)<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var Jt=[null];function en(e,t){this._groups=e,this._parents=t}function tn(e){return"string"==typeof e?new en([[document.querySelector(e)]],[document.documentElement]):new en([[e]],Jt)}function nn(e){return e}en.prototype=function(){return new en([[document.documentElement]],Jt)}.prototype={constructor:en,select:function(e){"function"!=typeof e&&(e=Ge(e));for(var t=this._groups,n=t.length,r=new Array(n),a=0;a<n;++a)for(var i,o,u=t[a],l=u.length,s=r[a]=new Array(l),c=0;c<l;++c)(i=u[c])&&(o=e.call(i,i.__data__,c,u))&&("__data__"in i&&(o.__data__=i.__data__),s[c]=o);return new en(r,this._parents)},selectAll:function(e){e="function"==typeof e?function(e){return function(){return null==(t=e.apply(this,arguments))?[]:Array.isArray(t)?t:Array.from(t);var t}}(e):function(e){return null==e?Ke:function(){return this.querySelectorAll(e)}}(e);for(var t=this._groups,n=t.length,r=[],a=[],i=0;i<n;++i)for(var o,u=t[i],l=u.length,s=0;s<l;++s)(o=u[s])&&(r.push(e.call(o,o.__data__,s,u)),a.push(o));return new en(r,a)},selectChild:function(e){return this.select(null==e?Je:function(e){return function(){return Ze.call(this.children,e)}}("function"==typeof e?e:Xe(e)))},selectChildren:function(e){return this.selectAll(null==e?tt:function(e){return function(){return et.call(this.children,e)}}("function"==typeof e?e:Xe(e)))},filter:function(e){"function"!=typeof e&&(e=function(e){return function(){return this.matches(e)}}(e));for(var t=this._groups,n=t.length,r=new Array(n),a=0;a<n;++a)for(var i,o=t[a],u=o.length,l=r[a]=[],s=0;s<u;++s)(i=o[s])&&e.call(i,i.__data__,s,o)&&l.push(i);return new en(r,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,ot);var n,r=t?it:at,a=this._parents,i=this._groups;"function"!=typeof e&&(n=e,e=function(){return n});for(var o=i.length,u=new Array(o),l=new Array(o),s=new Array(o),c=0;c<o;++c){var f=a[c],d=i[c],p=d.length,h=ut(e.call(f,f&&f.__data__,c,a)),g=h.length,v=l[c]=new Array(g),m=u[c]=new Array(g);r(f,d,v,m,s[c]=new Array(p),h,t);for(var y,b,w=0,_=0;w<g;++w)if(y=v[w]){for(w>=_&&(_=w+1);!(b=m[_])&&++_<g;);y._next=b||null}}return(u=new en(u,a))._enter=l,u._exit=s,u},enter:function(){return new en(this._enter||this._groups.map(nt),this._parents)},exit:function(){return new en(this._exit||this._groups.map(nt),this._parents)},join:function(e,t,n){var r=this.enter(),a=this,i=this.exit();return"function"==typeof e?(r=e(r))&&(r=r.selection()):r=r.append(e+""),null!=t&&(a=t(a))&&(a=a.selection()),null==n?i.remove():n(i),r&&a?r.merge(a).order():a},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,a=n.length,i=r.length,o=Math.min(a,i),u=new Array(a),l=0;l<o;++l)for(var s,c=n[l],f=r[l],d=c.length,p=u[l]=new Array(d),h=0;h<d;++h)(s=c[h]||f[h])&&(p[h]=s);for(;l<a;++l)u[l]=n[l];return new en(u,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r,a=e[t],i=a.length-1,o=a[i];--i>=0;)(r=a[i])&&(o&&4^r.compareDocumentPosition(o)&&o.parentNode.insertBefore(r,o),o=r);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=lt);for(var n=this._groups,r=n.length,a=new Array(r),i=0;i<r;++i){for(var o,u=n[i],l=u.length,s=a[i]=new Array(l),c=0;c<l;++c)(o=u[c])&&(s[c]=o);s.sort(t)}return new en(a,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],a=0,i=r.length;a<i;++a){var o=r[a];if(o)return o}return null},size:function(){let e=0;for(const t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var a,i=t[n],o=0,u=i.length;o<u;++o)(a=i[o])&&e.call(a,a.__data__,o,i);return this},attr:function(e,t){var n=ft(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==t?n.local?pt:dt:"function"==typeof t?n.local?mt:vt:n.local?gt:ht)(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?bt:"function"==typeof t?_t:wt)(e,t,null==n?"":n)):function(e,t){return e.style.getPropertyValue(t)||yt(e).getComputedStyle(e,null).getPropertyValue(t)}(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?kt:"function"==typeof t?St:xt)(e,t)):this.node()[e]},classed:function(e,t){var n=Et(e+"");if(arguments.length<2){for(var r=Ct(this.node()),a=-1,i=n.length;++a<i;)if(!r.contains(n[a]))return!1;return!0}return this.each(("function"==typeof t?At:t?Mt:Nt)(n,t))},text:function(e){return arguments.length?this.each(null==e?Ot:("function"==typeof e?Ft:Lt)(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?Dt:("function"==typeof e?jt:Rt)(e)):this.node().innerHTML},raise:function(){return this.each(Ut)},lower:function(){return this.each(It)},append:function(e){var t="function"==typeof e?e:Ht(e);return this.select((function(){return this.appendChild(t.apply(this,arguments))}))},insert:function(e,t){var n="function"==typeof e?e:Ht(e),r=null==t?Wt:"function"==typeof t?t:Ge(t);return this.select((function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)}))},remove:function(){return this.each(Vt)},clone:function(e){return this.select(e?Qt:qt)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var r,a,i=function(e){return e.trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}))}(e+""),o=i.length;if(!(arguments.length<2)){for(u=t?Gt:Yt,r=0;r<o;++r)this.each(u(i[r],t,n));return this}var u=this.node().__on;if(u)for(var l,s=0,c=u.length;s<c;++s)for(r=0,l=u[s];r<o;++r)if((a=i[r]).type===l.type&&a.name===l.name)return l.value},dispatch:function(e,t){return this.each(("function"==typeof t?Zt:Xt)(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r,a=e[t],i=0,o=a.length;i<o;++i)(r=a[i])&&(yield r)}};var rn=1e-6;function an(e){return"translate("+e+",0)"}function on(e){return"translate(0,"+e+")"}function un(e){return t=>+e(t)}function ln(e,t){return t=Math.max(0,e.bandwidth()-2*t)/2,e.round()&&(t=Math.round(t)),n=>+e(n)+t}function sn(){return!this.__axis}function cn(e,t){var n=[],r=null,a=null,i=6,o=6,u=3,l="undefined"!=typeof window&&window.devicePixelRatio>1?0:.5,s=1===e||4===e?-1:1,c=4===e||2===e?"x":"y",f=1===e||3===e?an:on;function d(d){var p=null==r?t.ticks?t.ticks.apply(t,n):t.domain():r,h=null==a?t.tickFormat?t.tickFormat.apply(t,n):nn:a,g=Math.max(i,0)+u,v=t.range(),m=+v[0]+l,y=+v[v.length-1]+l,b=(t.bandwidth?ln:un)(t.copy(),l),w=d.selection?d.selection():d,_=w.selectAll(".domain").data([null]),k=w.selectAll(".tick").data(p,t).order(),x=k.exit(),S=k.enter().append("g").attr("class","tick"),E=k.select("line"),C=k.select("text");_=_.merge(_.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),k=k.merge(S),E=E.merge(S.append("line").attr("stroke","currentColor").attr(c+"2",s*i)),C=C.merge(S.append("text").attr("fill","currentColor").attr(c,s*g).attr("dy",1===e?"0em":3===e?"0.71em":"0.32em")),d!==w&&(_=_.transition(d),k=k.transition(d),E=E.transition(d),C=C.transition(d),x=x.transition(d).attr("opacity",rn).attr("transform",(function(e){return isFinite(e=b(e))?f(e+l):this.getAttribute("transform")})),S.attr("opacity",rn).attr("transform",(function(e){var t=this.parentNode.__axis;return f((t&&isFinite(t=t(e))?t:b(e))+l)}))),x.remove(),_.attr("d",4===e||2===e?o?"M"+s*o+","+m+"H"+l+"V"+y+"H"+s*o:"M"+l+","+m+"V"+y:o?"M"+m+","+s*o+"V"+l+"H"+y+"V"+s*o:"M"+m+","+l+"H"+y),k.attr("opacity",1).attr("transform",(function(e){return f(b(e)+l)})),E.attr(c+"2",s*i),C.attr(c,s*g).text(h),w.filter(sn).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",2===e?"start":4===e?"end":"middle"),w.each((function(){this.__axis=b}))}return d.scale=function(e){return arguments.length?(t=e,d):t},d.ticks=function(){return n=Array.from(arguments),d},d.tickArguments=function(e){return arguments.length?(n=null==e?[]:Array.from(e),d):n.slice()},d.tickValues=function(e){return arguments.length?(r=null==e?null:Array.from(e),d):r&&r.slice()},d.tickFormat=function(e){return arguments.length?(a=e,d):a},d.tickSize=function(e){return arguments.length?(i=o=+e,d):i},d.tickSizeInner=function(e){return arguments.length?(i=+e,d):i},d.tickSizeOuter=function(e){return arguments.length?(o=+e,d):o},d.tickPadding=function(e){return arguments.length?(u=+e,d):u},d.offset=function(e){return arguments.length?(l=+e,d):l},d}function fn(e){return cn(3,e)}function dn(e){return function(){return e}}function pn(e){this._context=e}function hn(e){return new pn(e)}Array.prototype.slice,pn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}};const gn=Math.PI,vn=2*gn,mn=1e-6,yn=vn-mn;function bn(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=arguments[t]+e[t]}class wn{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?bn:function(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return bn;const n=10**t;return function(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=Math.round(arguments[t]*n)/n+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,n,r){this._append`Q${+e},${+t},${this._x1=+n},${this._y1=+r}`}bezierCurveTo(e,t,n,r,a,i){this._append`C${+e},${+t},${+n},${+r},${this._x1=+a},${this._y1=+i}`}arcTo(e,t,n,r,a){if(e=+e,t=+t,n=+n,r=+r,(a=+a)<0)throw new Error(`negative radius: ${a}`);let i=this._x1,o=this._y1,u=n-e,l=r-t,s=i-e,c=o-t,f=s*s+c*c;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>mn)if(Math.abs(c*u-l*s)>mn&&a){let d=n-i,p=r-o,h=u*u+l*l,g=d*d+p*p,v=Math.sqrt(h),m=Math.sqrt(f),y=a*Math.tan((gn-Math.acos((h+f-g)/(2*v*m)))/2),b=y/m,w=y/v;Math.abs(b-1)>mn&&this._append`L${e+b*s},${t+b*c}`,this._append`A${a},${a},0,0,${+(c*d>s*p)},${this._x1=e+w*u},${this._y1=t+w*l}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,n,r,a,i){if(e=+e,t=+t,i=!!i,(n=+n)<0)throw new Error(`negative radius: ${n}`);let o=n*Math.cos(r),u=n*Math.sin(r),l=e+o,s=t+u,c=1^i,f=i?r-a:a-r;null===this._x1?this._append`M${l},${s}`:(Math.abs(this._x1-l)>mn||Math.abs(this._y1-s)>mn)&&this._append`L${l},${s}`,n&&(f<0&&(f=f%vn+vn),f>yn?this._append`A${n},${n},0,1,${c},${e-o},${t-u}A${n},${n},0,1,${c},${this._x1=l},${this._y1=s}`:f>mn&&this._append`A${n},${n},0,${+(f>=gn)},${c},${this._x1=e+n*Math.cos(a)},${this._y1=t+n*Math.sin(a)}`)}rect(e,t,n,r){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${n=+n}v${+r}h${-n}Z`}toString(){return this._}}function _n(e){return e[0]}function kn(e){return e[1]}function xn(e,t){var n=dn(!0),r=null,a=hn,i=null,o=function(e){let t=3;return e.digits=function(n){if(!arguments.length)return t;if(null==n)t=null;else{const e=Math.floor(n);if(!(e>=0))throw new RangeError(`invalid digits: ${n}`);t=e}return e},()=>new wn(t)}(u);function u(u){var l,s,c,f=(u=function(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}(u)).length,d=!1;for(null==r&&(i=a(c=o())),l=0;l<=f;++l)!(l<f&&n(s=u[l],l,u))===d&&((d=!d)?i.lineStart():i.lineEnd()),d&&i.point(+e(s,l,u),+t(s,l,u));if(c)return i=null,c+""||null}return e="function"==typeof e?e:void 0===e?_n:dn(e),t="function"==typeof t?t:void 0===t?kn:dn(t),u.x=function(t){return arguments.length?(e="function"==typeof t?t:dn(+t),u):e},u.y=function(e){return arguments.length?(t="function"==typeof e?e:dn(+e),u):t},u.defined=function(e){return arguments.length?(n="function"==typeof e?e:dn(!!e),u):n},u.curve=function(e){return arguments.length?(a=e,null!=r&&(i=a(r)),u):a},u.context=function(e){return arguments.length?(null==e?r=i=null:i=a(r=e),u):r},u}function Sn(e){return Sn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Sn(e)}function En(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Cn(r.key),r)}}function Cn(e){var t=function(e){if("object"!=Sn(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Sn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Sn(t)?t:t+""}function Tn(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Tn=function(){return!!e})()}function Pn(e){return Pn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Pn(e)}function zn(e,t){return zn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},zn(e,t)}var Mn=function(t){function n(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),e=function(e,t,n){return t=Pn(t),function(e,t){if(t&&("object"==Sn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,Tn()?Reflect.construct(t,n||[],Pn(e).constructor):t.apply(e,n))}(this,n),window.lastAdditiveForceVisualizer=e,e.effectFormat=Ae(".2"),e.redraw=(0,je.debounce)((function(){return e.draw()}),200),e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&zn(e,t)}(n,t),r=n,(a=[{key:"componentDidMount",value:function(){var e=this;this.mainGroup=this.svg.append("g"),this.axisElement=this.mainGroup.append("g").attr("transform","translate(0,35)").attr("class","force-bar-axis"),this.onTopGroup=this.svg.append("g"),this.baseValueTitle=this.svg.append("text"),this.joinPointLine=this.svg.append("line"),this.joinPointLabelOutline=this.svg.append("text"),this.joinPointLabel=this.svg.append("text"),this.joinPointTitleLeft=this.svg.append("text"),this.joinPointTitleLeftArrow=this.svg.append("text"),this.joinPointTitle=this.svg.append("text"),this.joinPointTitleRightArrow=this.svg.append("text"),this.joinPointTitleRight=this.svg.append("text"),this.hoverLabelBacking=this.svg.append("text").attr("x",10).attr("y",20).attr("text-anchor","middle").attr("font-size",12).attr("stroke","#fff").attr("fill","#fff").attr("stroke-width","4").attr("stroke-linejoin","round").text("").on("mouseover",(function(){e.hoverLabel.attr("opacity",1),e.hoverLabelBacking.attr("opacity",1)})).on("mouseout",(function(){e.hoverLabel.attr("opacity",0),e.hoverLabelBacking.attr("opacity",0)})),this.hoverLabel=this.svg.append("text").attr("x",10).attr("y",20).attr("text-anchor","middle").attr("font-size",12).attr("fill","#0f0").text("").on("mouseover",(function(){e.hoverLabel.attr("opacity",1),e.hoverLabelBacking.attr("opacity",1)})).on("mouseout",(function(){e.hoverLabel.attr("opacity",0),e.hoverLabelBacking.attr("opacity",0)}));var t=void 0;"string"==typeof this.props.plot_cmap?this.props.plot_cmap in Ue.colors?t=Ue.colors[this.props.plot_cmap]:(console.log("Invalid color map name, reverting to default."),t=Ue.colors.RdBu):Array.isArray(this.props.plot_cmap)&&(t=this.props.plot_cmap),this.colors=t.map((function(e){return Q(e)})),this.brighterColors=[1.45,1.6].map((function(t,n){return e.colors[n].brighter(t)})),this.colors.map((function(t,n){var r=e.svg.append("linearGradient").attr("id","linear-grad-"+n).attr("x1","0%").attr("y1","0%").attr("x2","0%").attr("y2","100%");r.append("stop").attr("offset","0%").attr("stop-color",t).attr("stop-opacity",.6),r.append("stop").attr("offset","100%").attr("stop-color",t).attr("stop-opacity",0);var a=e.svg.append("linearGradient").attr("id","linear-backgrad-"+n).attr("x1","0%").attr("y1","0%").attr("x2","0%").attr("y2","100%");a.append("stop").attr("offset","0%").attr("stop-color",t).attr("stop-opacity",.5),a.append("stop").attr("offset","100%").attr("stop-color",t).attr("stop-opacity",0)})),this.tickFormat=Ae(",.4"),this.scaleCentered=Re(),this.axis=fn().scale(this.scaleCentered).tickSizeInner(4).tickSizeOuter(0).tickFormat((function(t){return e.tickFormat(e.invLinkFunction(t))})).tickPadding(-18),window.addEventListener("resize",this.redraw),window.setTimeout(this.redraw,50)}},{key:"componentDidUpdate",value:function(){this.draw()}},{key:"draw",value:function(){var e=this;(0,je.each)(this.props.featureNames,(function(t,n){e.props.features[n]&&(e.props.features[n].name=t)})),"identity"===this.props.link?this.invLinkFunction=function(t){return e.props.baseValue+t}:"logit"===this.props.link?this.invLinkFunction=function(t){return 1/(1+Math.exp(-(e.props.baseValue+t)))}:console.log("ERROR: Unrecognized link function: ",this.props.link);var t=this.svg.node().parentNode.offsetWidth;if(0==t)return setTimeout((function(){return e.draw(e.props)}),500);this.svg.style("height","150px"),this.svg.style("width",t+"px");var n=(0,je.sortBy)(this.props.features,(function(e){return-1/(e.effect+1e-10)})),r=(0,je.sum)((0,je.map)(n,(function(e){return Math.abs(e.effect)}))),a=(0,je.sum)((0,je.map)((0,je.filter)(n,(function(e){return e.effect>0})),(function(e){return e.effect})))||0,i=(0,je.sum)((0,je.map)((0,je.filter)(n,(function(e){return e.effect<0})),(function(e){return-e.effect})))||0;this.domainSize=3*Math.max(a,i);var o=Re().domain([0,this.domainSize]).range([0,t]),u=t/2-o(i);this.scaleCentered.domain([-this.domainSize/2,this.domainSize/2]).range([0,t]).clamp(!0),this.axisElement.attr("transform","translate(0,50)").call(this.axis);var l,s,c,f=0;for(l=0;l<n.length;++l)n[l].x=f,n[l].effect<0&&void 0===s&&(s=f,c=l),f+=Math.abs(n[l].effect);void 0===s&&(s=f,c=l);var d=xn().x((function(e){return e[0]})).y((function(e){return e[1]})),p=function(t){return void 0!==t.value&&null!==t.value&&""!==t.value?t.name+" = "+(isNaN(t.value)?t.value:e.tickFormat(t.value)):t.name};n=this.props.hideBars?[]:n;var h=this.mainGroup.selectAll(".force-bar-blocks").data(n);h.enter().append("path").attr("class","force-bar-blocks").merge(h).attr("d",(function(e,t){var n=o(e.x)+u,r=o(Math.abs(e.effect)),a=e.effect<0?-4:4,i=a;return t===c&&(a=0),t===c-1&&(i=0),d([[n,56],[n+r,56],[n+r+i,64.5],[n+r,73],[n,73],[n+a,64.5]])})).attr("fill",(function(t){return t.effect>0?e.colors[0]:e.colors[1]})).on("mouseover",(function(t){if(o(Math.abs(t.effect))<o(r)/50||o(Math.abs(t.effect))<10){var n=o(t.x)+u,a=o(Math.abs(t.effect));e.hoverLabel.attr("opacity",1).attr("x",n+a/2).attr("y",50.5).attr("fill",t.effect>0?e.colors[0]:e.colors[1]).text(p(t)),e.hoverLabelBacking.attr("opacity",1).attr("x",n+a/2).attr("y",50.5).text(p(t))}})).on("mouseout",(function(){e.hoverLabel.attr("opacity",0),e.hoverLabelBacking.attr("opacity",0)})),h.exit().remove();var g=(0,je.filter)(n,(function(e){return o(Math.abs(e.effect))>o(r)/50&&o(Math.abs(e.effect))>10})),v=this.onTopGroup.selectAll(".force-bar-labels").data(g);if(v.exit().remove(),v=v.enter().append("text").attr("class","force-bar-labels").attr("font-size","12px").attr("y",98).merge(v).text((function(t){return void 0!==t.value&&null!==t.value&&""!==t.value?t.name+" = "+(isNaN(t.value)?t.value:e.tickFormat(t.value)):t.name})).attr("fill",(function(t){return t.effect>0?e.colors[0]:e.colors[1]})).attr("stroke",(function(e){return e.textWidth=Math.max(this.getComputedTextLength(),o(Math.abs(e.effect))-10),e.innerTextWidth=this.getComputedTextLength(),"none"})),this.filteredData=g,n.length>0){f=s+o.invert(5);for(var m=c;m<n.length;++m)n[m].textx=f,f+=o.invert(n[m].textWidth+10);f=s-o.invert(5);for(var y=c-1;y>=0;--y)n[y].textx=f,f-=o.invert(n[y].textWidth+10)}v.attr("x",(function(e){return o(e.textx)+u+(e.effect>0?-e.textWidth/2:e.textWidth/2)})).attr("text-anchor","middle"),g=(0,je.filter)(g,(function(n){return o(n.textx)+u>e.props.labelMargin&&o(n.textx)+u<t-e.props.labelMargin})),this.filteredData2=g;var b=g.slice(),w=(0,je.findIndex)(n,g[0])-1;w>=0&&b.unshift(n[w]);var _=this.mainGroup.selectAll(".force-bar-labelBacking").data(g);_.enter().append("path").attr("class","force-bar-labelBacking").attr("stroke","none").attr("opacity",.2).merge(_).attr("d",(function(e){return d([[o(e.x)+o(Math.abs(e.effect))+u,73],[(e.effect>0?o(e.textx):o(e.textx)+e.textWidth)+u+5,83],[(e.effect>0?o(e.textx):o(e.textx)+e.textWidth)+u+5,104],[(e.effect>0?o(e.textx)-e.textWidth:o(e.textx))+u-5,104],[(e.effect>0?o(e.textx)-e.textWidth:o(e.textx))+u-5,83],[o(e.x)+u,73]])})).attr("fill",(function(e){return"url(#linear-backgrad-".concat(e.effect>0?0:1,")")})),_.exit().remove();var k=this.mainGroup.selectAll(".force-bar-labelDividers").data(g.slice(0,-1));k.enter().append("rect").attr("class","force-bar-labelDividers").attr("height","21px").attr("width","1px").attr("y",83).merge(k).attr("x",(function(e){return(e.effect>0?o(e.textx):o(e.textx)+e.textWidth)+u+4.5})).attr("fill",(function(e){return"url(#linear-grad-".concat(e.effect>0?0:1,")")})),k.exit().remove();var x=this.mainGroup.selectAll(".force-bar-labelLinks").data(g.slice(0,-1));x.enter().append("line").attr("class","force-bar-labelLinks").attr("y1",73).attr("y2",83).attr("stroke-opacity",.5).attr("stroke-width",1).merge(x).attr("x1",(function(e){return o(e.x)+o(Math.abs(e.effect))+u})).attr("x2",(function(e){return(e.effect>0?o(e.textx):o(e.textx)+e.textWidth)+u+5})).attr("stroke",(function(t){return t.effect>0?e.colors[0]:e.colors[1]})),x.exit().remove();var S=this.mainGroup.selectAll(".force-bar-blockDividers").data(n.slice(0,-1));S.enter().append("path").attr("class","force-bar-blockDividers").attr("stroke-width",2).attr("fill","none").merge(S).attr("d",(function(e){var t=o(e.x)+o(Math.abs(e.effect))+u;return d([[t,56],[t+(e.effect<0?-4:4),64.5],[t,73]])})).attr("stroke",(function(t,n){return c===n+1||Math.abs(t.effect)<1e-8?"#rgba(0,0,0,0)":t.effect>0?e.brighterColors[0]:e.brighterColors[1]})),S.exit().remove(),this.joinPointLine.attr("x1",o(s)+u).attr("x2",o(s)+u).attr("y1",50).attr("y2",56).attr("stroke","#F2F2F2").attr("stroke-width",1).attr("opacity",1),this.joinPointLabelOutline.attr("x",o(s)+u).attr("y",45).attr("color","#fff").attr("text-anchor","middle").attr("font-weight","bold").attr("stroke","#fff").attr("stroke-width",6).text(Ae(",.2f")(this.invLinkFunction(s-i))).attr("opacity",1),console.log("joinPoint",s,u,50,i),this.joinPointLabel.attr("x",o(s)+u).attr("y",45).attr("text-anchor","middle").attr("font-weight","bold").attr("fill","#000").text(Ae(",.2f")(this.invLinkFunction(s-i))).attr("opacity",1),this.joinPointTitle.attr("x",o(s)+u).attr("y",28).attr("text-anchor","middle").attr("font-size","12").attr("fill","#000").text(this.props.outNames[0]).attr("opacity",.5),this.props.hideBars||(this.joinPointTitleLeft.attr("x",o(s)+u-16).attr("y",12).attr("text-anchor","end").attr("font-size","13").attr("fill",this.colors[0]).text("higher").attr("opacity",1),this.joinPointTitleRight.attr("x",o(s)+u+16).attr("y",12).attr("text-anchor","start").attr("font-size","13").attr("fill",this.colors[1]).text("lower").attr("opacity",1),this.joinPointTitleLeftArrow.attr("x",o(s)+u+7).attr("y",8).attr("text-anchor","end").attr("font-size","13").attr("fill",this.colors[0]).text("→").attr("opacity",1),this.joinPointTitleRightArrow.attr("x",o(s)+u-7).attr("y",14).attr("text-anchor","start").attr("font-size","13").attr("fill",this.colors[1]).text("←").attr("opacity",1)),this.props.hideBaseValueLabel||this.baseValueTitle.attr("x",this.scaleCentered(0)).attr("y",28).attr("text-anchor","middle").attr("font-size","12").attr("fill","#000").text("base value").attr("opacity",.5)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.redraw)}},{key:"render",value:function(){var t=this;return e.createElement("svg",{ref:function(e){return t.svg=tn(e)},style:{userSelect:"none",display:"block",fontFamily:"arial",sansSerif:!0}},e.createElement("style",{dangerouslySetInnerHTML:{__html:"\n          .force-bar-axis path {\n            fill: none;\n            opacity: 0.4;\n          }\n          .force-bar-axis paths {\n            display: none;\n          }\n          .tick line {\n            stroke: #000;\n            stroke-width: 1px;\n            opacity: 0.4;\n          }\n          .tick text {\n            fill: #000;\n            opacity: 0.5;\n            font-size: 12px;\n            padding: 0px;\n          }"}}))}}])&&En(r.prototype,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,a}(e.Component);Mn.defaultProps={plot_cmap:"RdBu"};const Nn=Mn,An=1e3,On=6e4,Ln=36e5,Fn=864e5,Dn=6048e5,Rn=31536e6,jn=new Date,Un=new Date;function In(e,t,n,r){function a(t){return e(t=0===arguments.length?new Date:new Date(+t)),t}return a.floor=t=>(e(t=new Date(+t)),t),a.ceil=n=>(e(n=new Date(n-1)),t(n,1),e(n),n),a.round=e=>{const t=a(e),n=a.ceil(e);return e-t<n-e?t:n},a.offset=(e,n)=>(t(e=new Date(+e),null==n?1:Math.floor(n)),e),a.range=(n,r,i)=>{const o=[];if(n=a.ceil(n),i=null==i?1:Math.floor(i),!(n<r&&i>0))return o;let u;do{o.push(u=new Date(+n)),t(n,i),e(n)}while(u<n&&n<r);return o},a.filter=n=>In((t=>{if(t>=t)for(;e(t),!n(t);)t.setTime(t-1)}),((e,r)=>{if(e>=e)if(r<0)for(;++r<=0;)for(;t(e,-1),!n(e););else for(;--r>=0;)for(;t(e,1),!n(e););})),n&&(a.count=(t,r)=>(jn.setTime(+t),Un.setTime(+r),e(jn),e(Un),Math.floor(n(jn,Un))),a.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?a.filter(r?t=>r(t)%e==0:t=>a.count(0,t)%e==0):a:null)),a}const $n=In((()=>{}),((e,t)=>{e.setTime(+e+t)}),((e,t)=>t-e));$n.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?In((t=>{t.setTime(Math.floor(t/e)*e)}),((t,n)=>{t.setTime(+t+n*e)}),((t,n)=>(n-t)/e)):$n:null),$n.range;const Bn=In((e=>{e.setTime(e-e.getMilliseconds())}),((e,t)=>{e.setTime(+e+t*An)}),((e,t)=>(t-e)/An),(e=>e.getUTCSeconds())),Hn=(Bn.range,In((e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*An)}),((e,t)=>{e.setTime(+e+t*On)}),((e,t)=>(t-e)/On),(e=>e.getMinutes()))),Wn=(Hn.range,In((e=>{e.setUTCSeconds(0,0)}),((e,t)=>{e.setTime(+e+t*On)}),((e,t)=>(t-e)/On),(e=>e.getUTCMinutes()))),Vn=(Wn.range,In((e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*An-e.getMinutes()*On)}),((e,t)=>{e.setTime(+e+t*Ln)}),((e,t)=>(t-e)/Ln),(e=>e.getHours()))),qn=(Vn.range,In((e=>{e.setUTCMinutes(0,0,0)}),((e,t)=>{e.setTime(+e+t*Ln)}),((e,t)=>(t-e)/Ln),(e=>e.getUTCHours()))),Qn=(qn.range,In((e=>e.setHours(0,0,0,0)),((e,t)=>e.setDate(e.getDate()+t)),((e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*On)/Fn),(e=>e.getDate()-1))),Yn=(Qn.range,In((e=>{e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+t)}),((e,t)=>(t-e)/Fn),(e=>e.getUTCDate()-1))),Gn=(Yn.range,In((e=>{e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+t)}),((e,t)=>(t-e)/Fn),(e=>Math.floor(e/Fn))));function Kn(e){return In((t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)}),((e,t)=>{e.setDate(e.getDate()+7*t)}),((e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*On)/Dn))}Gn.range;const Xn=Kn(0),Zn=Kn(1),Jn=Kn(2),er=Kn(3),tr=Kn(4),nr=Kn(5),rr=Kn(6);function ar(e){return In((t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)}),((e,t)=>(t-e)/Dn))}Xn.range,Zn.range,Jn.range,er.range,tr.range,nr.range,rr.range;const ir=ar(0),or=ar(1),ur=ar(2),lr=ar(3),sr=ar(4),cr=ar(5),fr=ar(6),dr=(ir.range,or.range,ur.range,lr.range,sr.range,cr.range,fr.range,In((e=>{e.setDate(1),e.setHours(0,0,0,0)}),((e,t)=>{e.setMonth(e.getMonth()+t)}),((e,t)=>t.getMonth()-e.getMonth()+12*(t.getFullYear()-e.getFullYear())),(e=>e.getMonth()))),pr=(dr.range,In((e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)}),((e,t)=>t.getUTCMonth()-e.getUTCMonth()+12*(t.getUTCFullYear()-e.getUTCFullYear())),(e=>e.getUTCMonth()))),hr=(pr.range,In((e=>{e.setMonth(0,1),e.setHours(0,0,0,0)}),((e,t)=>{e.setFullYear(e.getFullYear()+t)}),((e,t)=>t.getFullYear()-e.getFullYear()),(e=>e.getFullYear())));hr.every=e=>isFinite(e=Math.floor(e))&&e>0?In((t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,n)=>{t.setFullYear(t.getFullYear()+n*e)})):null,hr.range;const gr=In((e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)}),((e,t)=>t.getUTCFullYear()-e.getUTCFullYear()),(e=>e.getUTCFullYear()));function vr(e,t,n,r,a,i){const o=[[Bn,1,An],[Bn,5,5e3],[Bn,15,15e3],[Bn,30,3e4],[i,1,On],[i,5,3e5],[i,15,9e5],[i,30,18e5],[a,1,Ln],[a,3,108e5],[a,6,216e5],[a,12,432e5],[r,1,Fn],[r,2,1728e5],[n,1,Dn],[t,1,2592e6],[t,3,7776e6],[e,1,Rn]];function u(t,n,r){const a=Math.abs(n-t)/r,i=d((([,,e])=>e)).right(o,a);if(i===o.length)return e.every(s(t/Rn,n/Rn,r));if(0===i)return $n.every(Math.max(s(t,n,r),1));const[u,l]=o[a/o[i-1][2]<o[i][2]/a?i-1:i];return u.every(l)}return[function(e,t,n){const r=t<e;r&&([e,t]=[t,e]);const a=n&&"function"==typeof n.range?n:u(e,t,n),i=a?a.range(e,+t+1):[];return r?i.reverse():i},u]}gr.every=e=>isFinite(e=Math.floor(e))&&e>0?In((t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n*e)})):null,gr.range;const[mr,yr]=vr(gr,pr,ir,Gn,qn,Wn),[br,wr]=vr(hr,dr,Xn,Qn,Vn,Hn);function _r(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function kr(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function xr(e,t,n){return{y:e,m:t,d:n,H:0,M:0,S:0,L:0}}var Sr,Er,Cr,Tr={"-":"",_:" ",0:"0"},Pr=/^\s*\d+/,zr=/^%/,Mr=/[\\^$*+?|[\]().{}]/g;function Nr(e,t,n){var r=e<0?"-":"",a=(r?-e:e)+"",i=a.length;return r+(i<n?new Array(n-i+1).join(t)+a:a)}function Ar(e){return e.replace(Mr,"\\$&")}function Or(e){return new RegExp("^(?:"+e.map(Ar).join("|")+")","i")}function Lr(e){return new Map(e.map(((e,t)=>[e.toLowerCase(),t])))}function Fr(e,t,n){var r=Pr.exec(t.slice(n,n+1));return r?(e.w=+r[0],n+r[0].length):-1}function Dr(e,t,n){var r=Pr.exec(t.slice(n,n+1));return r?(e.u=+r[0],n+r[0].length):-1}function Rr(e,t,n){var r=Pr.exec(t.slice(n,n+2));return r?(e.U=+r[0],n+r[0].length):-1}function jr(e,t,n){var r=Pr.exec(t.slice(n,n+2));return r?(e.V=+r[0],n+r[0].length):-1}function Ur(e,t,n){var r=Pr.exec(t.slice(n,n+2));return r?(e.W=+r[0],n+r[0].length):-1}function Ir(e,t,n){var r=Pr.exec(t.slice(n,n+4));return r?(e.y=+r[0],n+r[0].length):-1}function $r(e,t,n){var r=Pr.exec(t.slice(n,n+2));return r?(e.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function Br(e,t,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(n,n+6));return r?(e.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function Hr(e,t,n){var r=Pr.exec(t.slice(n,n+1));return r?(e.q=3*r[0]-3,n+r[0].length):-1}function Wr(e,t,n){var r=Pr.exec(t.slice(n,n+2));return r?(e.m=r[0]-1,n+r[0].length):-1}function Vr(e,t,n){var r=Pr.exec(t.slice(n,n+2));return r?(e.d=+r[0],n+r[0].length):-1}function qr(e,t,n){var r=Pr.exec(t.slice(n,n+3));return r?(e.m=0,e.d=+r[0],n+r[0].length):-1}function Qr(e,t,n){var r=Pr.exec(t.slice(n,n+2));return r?(e.H=+r[0],n+r[0].length):-1}function Yr(e,t,n){var r=Pr.exec(t.slice(n,n+2));return r?(e.M=+r[0],n+r[0].length):-1}function Gr(e,t,n){var r=Pr.exec(t.slice(n,n+2));return r?(e.S=+r[0],n+r[0].length):-1}function Kr(e,t,n){var r=Pr.exec(t.slice(n,n+3));return r?(e.L=+r[0],n+r[0].length):-1}function Xr(e,t,n){var r=Pr.exec(t.slice(n,n+6));return r?(e.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function Zr(e,t,n){var r=zr.exec(t.slice(n,n+1));return r?n+r[0].length:-1}function Jr(e,t,n){var r=Pr.exec(t.slice(n));return r?(e.Q=+r[0],n+r[0].length):-1}function ea(e,t,n){var r=Pr.exec(t.slice(n));return r?(e.s=+r[0],n+r[0].length):-1}function ta(e,t){return Nr(e.getDate(),t,2)}function na(e,t){return Nr(e.getHours(),t,2)}function ra(e,t){return Nr(e.getHours()%12||12,t,2)}function aa(e,t){return Nr(1+Qn.count(hr(e),e),t,3)}function ia(e,t){return Nr(e.getMilliseconds(),t,3)}function oa(e,t){return ia(e,t)+"000"}function ua(e,t){return Nr(e.getMonth()+1,t,2)}function la(e,t){return Nr(e.getMinutes(),t,2)}function sa(e,t){return Nr(e.getSeconds(),t,2)}function ca(e){var t=e.getDay();return 0===t?7:t}function fa(e,t){return Nr(Xn.count(hr(e)-1,e),t,2)}function da(e){var t=e.getDay();return t>=4||0===t?tr(e):tr.ceil(e)}function pa(e,t){return e=da(e),Nr(tr.count(hr(e),e)+(4===hr(e).getDay()),t,2)}function ha(e){return e.getDay()}function ga(e,t){return Nr(Zn.count(hr(e)-1,e),t,2)}function va(e,t){return Nr(e.getFullYear()%100,t,2)}function ma(e,t){return Nr((e=da(e)).getFullYear()%100,t,2)}function ya(e,t){return Nr(e.getFullYear()%1e4,t,4)}function ba(e,t){var n=e.getDay();return Nr((e=n>=4||0===n?tr(e):tr.ceil(e)).getFullYear()%1e4,t,4)}function wa(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+Nr(t/60|0,"0",2)+Nr(t%60,"0",2)}function _a(e,t){return Nr(e.getUTCDate(),t,2)}function ka(e,t){return Nr(e.getUTCHours(),t,2)}function xa(e,t){return Nr(e.getUTCHours()%12||12,t,2)}function Sa(e,t){return Nr(1+Yn.count(gr(e),e),t,3)}function Ea(e,t){return Nr(e.getUTCMilliseconds(),t,3)}function Ca(e,t){return Ea(e,t)+"000"}function Ta(e,t){return Nr(e.getUTCMonth()+1,t,2)}function Pa(e,t){return Nr(e.getUTCMinutes(),t,2)}function za(e,t){return Nr(e.getUTCSeconds(),t,2)}function Ma(e){var t=e.getUTCDay();return 0===t?7:t}function Na(e,t){return Nr(ir.count(gr(e)-1,e),t,2)}function Aa(e){var t=e.getUTCDay();return t>=4||0===t?sr(e):sr.ceil(e)}function Oa(e,t){return e=Aa(e),Nr(sr.count(gr(e),e)+(4===gr(e).getUTCDay()),t,2)}function La(e){return e.getUTCDay()}function Fa(e,t){return Nr(or.count(gr(e)-1,e),t,2)}function Da(e,t){return Nr(e.getUTCFullYear()%100,t,2)}function Ra(e,t){return Nr((e=Aa(e)).getUTCFullYear()%100,t,2)}function ja(e,t){return Nr(e.getUTCFullYear()%1e4,t,4)}function Ua(e,t){var n=e.getUTCDay();return Nr((e=n>=4||0===n?sr(e):sr.ceil(e)).getUTCFullYear()%1e4,t,4)}function Ia(){return"+0000"}function $a(){return"%"}function Ba(e){return+e}function Ha(e){return Math.floor(+e/1e3)}function Wa(e){return new Date(e)}function Va(e){return e instanceof Date?+e:+new Date(+e)}function qa(e,t,n,r,a,i,o,u,l,s){var c=we(),f=c.invert,d=c.domain,p=s(".%L"),h=s(":%S"),g=s("%I:%M"),v=s("%I %p"),m=s("%a %d"),y=s("%b %d"),b=s("%B"),w=s("%Y");function _(e){return(l(e)<e?p:u(e)<e?h:o(e)<e?g:i(e)<e?v:r(e)<e?a(e)<e?m:y:n(e)<e?b:w)(e)}return c.invert=function(e){return new Date(f(e))},c.domain=function(e){return arguments.length?d(Array.from(e,Va)):d().map(Wa)},c.ticks=function(t){var n=d();return e(n[0],n[n.length-1],null==t?10:t)},c.tickFormat=function(e,t){return null==t?_:s(t)},c.nice=function(e){var n=d();return e&&"function"==typeof e.range||(e=t(n[0],n[n.length-1],null==e?10:e)),e?d(function(e,t){var n,r=0,a=(e=e.slice()).length-1,i=e[r],o=e[a];return o<i&&(n=r,r=a,a=n,n=i,i=o,o=n),e[r]=t.floor(i),e[a]=t.ceil(o),e}(n,e)):c},c.copy=function(){return be(c,qa(e,t,n,r,a,i,o,u,l,s))},c}function Qa(){return _e.apply(qa(br,wr,hr,dr,Xn,Qn,Vn,Hn,Bn,Er).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function Ya(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return Ga(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ga(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw i}}}}function Ga(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Ka(e){return Ka="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ka(e)}function Xa(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Za(r.key),r)}}function Za(e){var t=function(e){if("object"!=Ka(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Ka(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Ka(t)?t:t+""}function Ja(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Ja=function(){return!!e})()}function ei(e){return ei=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ei(e)}function ti(e,t){return ti=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ti(e,t)}Sr=function(e){var t=e.dateTime,n=e.date,r=e.time,a=e.periods,i=e.days,o=e.shortDays,u=e.months,l=e.shortMonths,s=Or(a),c=Lr(a),f=Or(i),d=Lr(i),p=Or(o),h=Lr(o),g=Or(u),v=Lr(u),m=Or(l),y=Lr(l),b={a:function(e){return o[e.getDay()]},A:function(e){return i[e.getDay()]},b:function(e){return l[e.getMonth()]},B:function(e){return u[e.getMonth()]},c:null,d:ta,e:ta,f:oa,g:ma,G:ba,H:na,I:ra,j:aa,L:ia,m:ua,M:la,p:function(e){return a[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:Ba,s:Ha,S:sa,u:ca,U:fa,V:pa,w:ha,W:ga,x:null,X:null,y:va,Y:ya,Z:wa,"%":$a},w={a:function(e){return o[e.getUTCDay()]},A:function(e){return i[e.getUTCDay()]},b:function(e){return l[e.getUTCMonth()]},B:function(e){return u[e.getUTCMonth()]},c:null,d:_a,e:_a,f:Ca,g:Ra,G:Ua,H:ka,I:xa,j:Sa,L:Ea,m:Ta,M:Pa,p:function(e){return a[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:Ba,s:Ha,S:za,u:Ma,U:Na,V:Oa,w:La,W:Fa,x:null,X:null,y:Da,Y:ja,Z:Ia,"%":$a},_={a:function(e,t,n){var r=p.exec(t.slice(n));return r?(e.w=h.get(r[0].toLowerCase()),n+r[0].length):-1},A:function(e,t,n){var r=f.exec(t.slice(n));return r?(e.w=d.get(r[0].toLowerCase()),n+r[0].length):-1},b:function(e,t,n){var r=m.exec(t.slice(n));return r?(e.m=y.get(r[0].toLowerCase()),n+r[0].length):-1},B:function(e,t,n){var r=g.exec(t.slice(n));return r?(e.m=v.get(r[0].toLowerCase()),n+r[0].length):-1},c:function(e,n,r){return S(e,t,n,r)},d:Vr,e:Vr,f:Xr,g:$r,G:Ir,H:Qr,I:Qr,j:qr,L:Kr,m:Wr,M:Yr,p:function(e,t,n){var r=s.exec(t.slice(n));return r?(e.p=c.get(r[0].toLowerCase()),n+r[0].length):-1},q:Hr,Q:Jr,s:ea,S:Gr,u:Dr,U:Rr,V:jr,w:Fr,W:Ur,x:function(e,t,r){return S(e,n,t,r)},X:function(e,t,n){return S(e,r,t,n)},y:$r,Y:Ir,Z:Br,"%":Zr};function k(e,t){return function(n){var r,a,i,o=[],u=-1,l=0,s=e.length;for(n instanceof Date||(n=new Date(+n));++u<s;)37===e.charCodeAt(u)&&(o.push(e.slice(l,u)),null!=(a=Tr[r=e.charAt(++u)])?r=e.charAt(++u):a="e"===r?" ":"0",(i=t[r])&&(r=i(n,a)),o.push(r),l=u+1);return o.push(e.slice(l,u)),o.join("")}}function x(e,t){return function(n){var r,a,i=xr(1900,void 0,1);if(S(i,e,n+="",0)!=n.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(t&&!("Z"in i)&&(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(a=(r=kr(xr(i.y,0,1))).getUTCDay(),r=a>4||0===a?or.ceil(r):or(r),r=Yn.offset(r,7*(i.V-1)),i.y=r.getUTCFullYear(),i.m=r.getUTCMonth(),i.d=r.getUTCDate()+(i.w+6)%7):(a=(r=_r(xr(i.y,0,1))).getDay(),r=a>4||0===a?Zn.ceil(r):Zn(r),r=Qn.offset(r,7*(i.V-1)),i.y=r.getFullYear(),i.m=r.getMonth(),i.d=r.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),a="Z"in i?kr(xr(i.y,0,1)).getUTCDay():_r(xr(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(a+5)%7:i.w+7*i.U-(a+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,kr(i)):_r(i)}}function S(e,t,n,r){for(var a,i,o=0,u=t.length,l=n.length;o<u;){if(r>=l)return-1;if(37===(a=t.charCodeAt(o++))){if(a=t.charAt(o++),!(i=_[a in Tr?t.charAt(o++):a])||(r=i(e,n,r))<0)return-1}else if(a!=n.charCodeAt(r++))return-1}return r}return b.x=k(n,b),b.X=k(r,b),b.c=k(t,b),w.x=k(n,w),w.X=k(r,w),w.c=k(t,w),{format:function(e){var t=k(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=x(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=k(e+="",w);return t.toString=function(){return e},t},utcParse:function(e){var t=x(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]}),Er=Sr.format,Cr=Sr.parse,Sr.utcFormat,Sr.utcParse;var ni=function(t){function n(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),e=function(e,t,n){return t=ei(t),function(e,t){if(t&&("object"==Ka(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,Ja()?Reflect.construct(t,n||[],ei(e).constructor):t.apply(e,n))}(this,n),window.lastAdditiveForceArrayVisualizer=e,e.topOffset=28,e.leftOffset=80,e.height=350,e.effectFormat=Ae(".2"),e.redraw=(0,je.debounce)((function(){return e.draw()}),200),e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ti(e,t)}(n,t),r=n,a=[{key:"componentDidMount",value:function(){var e=this;this.mainGroup=this.svg.append("g"),this.onTopGroup=this.svg.append("g"),this.xaxisElement=this.onTopGroup.append("g").attr("transform","translate(0,35)").attr("class","force-bar-array-xaxis"),this.yaxisElement=this.onTopGroup.append("g").attr("transform","translate(0,35)").attr("class","force-bar-array-yaxis"),this.hoverGroup1=this.svg.append("g"),this.hoverGroup2=this.svg.append("g"),this.baseValueTitle=this.svg.append("text"),this.hoverLine=this.svg.append("line"),this.hoverxOutline=this.svg.append("text").attr("text-anchor","middle").attr("font-weight","bold").attr("fill","#fff").attr("stroke","#fff").attr("stroke-width","6").attr("font-size","12px"),this.hoverx=this.svg.append("text").attr("text-anchor","middle").attr("font-weight","bold").attr("fill","#000").attr("font-size","12px"),this.hoverxTitle=this.svg.append("text").attr("text-anchor","middle").attr("opacity",.6).attr("font-size","12px"),this.hoveryOutline=this.svg.append("text").attr("text-anchor","end").attr("font-weight","bold").attr("fill","#fff").attr("stroke","#fff").attr("stroke-width","6").attr("font-size","12px"),this.hovery=this.svg.append("text").attr("text-anchor","end").attr("font-weight","bold").attr("fill","#000").attr("font-size","12px"),this.xlabel=this.wrapper.select(".additive-force-array-xlabel"),this.ylabel=this.wrapper.select(".additive-force-array-ylabel");var t=void 0;"string"==typeof this.props.plot_cmap?this.props.plot_cmap in Ue.colors?t=Ue.colors[this.props.plot_cmap]:(console.log("Invalid color map name, reverting to default."),t=Ue.colors.RdBu):Array.isArray(this.props.plot_cmap)&&(t=this.props.plot_cmap),this.colors=t.map((function(e){return Q(e)})),this.brighterColors=[1.45,1.6].map((function(t,n){return e.colors[n].brighter(t)}));var n=Ae(",.4");null!=this.props.ordering_keys&&null!=this.props.ordering_keys_time_format?(this.parseTime=Cr(this.props.ordering_keys_time_format),this.formatTime=Er(this.props.ordering_keys_time_format),this.xtickFormat=function(e){return"object"==Ka(e)?this.formatTime(e):n(e)}):(this.parseTime=null,this.formatTime=null,this.xtickFormat=n),this.xscale=Re(),this.xaxis=fn().scale(this.xscale).tickSizeInner(4).tickSizeOuter(0).tickFormat((function(t){return e.xtickFormat(t)})).tickPadding(-18),this.ytickFormat=n,this.yscale=Re(),this.yaxis=cn(4,undefined).scale(this.yscale).tickSizeInner(4).tickSizeOuter(0).tickFormat((function(t){return e.ytickFormat(e.invLinkFunction(t))})).tickPadding(2),this.xlabel.node().onchange=function(){return e.internalDraw()},this.ylabel.node().onchange=function(){return e.internalDraw()},this.svg.on("mousemove",(function(t){return e.mouseMoved(t)})),this.svg.on("click",(function(){return alert("This original index of the sample you clicked is "+e.nearestExpIndex)})),this.svg.on("mouseout",(function(t){return e.mouseOut(t)})),window.addEventListener("resize",this.redraw),window.setTimeout(this.redraw,50)}},{key:"componentDidUpdate",value:function(){this.draw()}},{key:"mouseOut",value:function(){this.hoverLine.attr("display","none"),this.hoverx.attr("display","none"),this.hoverxOutline.attr("display","none"),this.hoverxTitle.attr("display","none"),this.hovery.attr("display","none"),this.hoveryOutline.attr("display","none"),this.hoverGroup1.attr("display","none"),this.hoverGroup2.attr("display","none")}},{key:"mouseMoved",value:function(e){var t,n,r=this;this.hoverLine.attr("display",""),this.hoverx.attr("display",""),this.hoverxOutline.attr("display",""),this.hoverxTitle.attr("display",""),this.hovery.attr("display",""),this.hoveryOutline.attr("display",""),this.hoverGroup1.attr("display",""),this.hoverGroup2.attr("display","");var a=function(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,[(r=r.matrixTransform(t.getScreenCTM().inverse())).x,r.y]}if(t.getBoundingClientRect){var a=t.getBoundingClientRect();return[e.clientX-a.left-t.clientLeft,e.clientY-a.top-t.clientTop]}}return[e.pageX,e.pageY]}(e,this.svg.node())[0];if(this.props.explanations){for(t=0;t<this.currExplanations.length;++t)(!n||Math.abs(n.xmapScaled-a)>Math.abs(this.currExplanations[t].xmapScaled-a))&&(n=this.currExplanations[t]);this.nearestExpIndex=n.origInd,this.hoverLine.attr("x1",n.xmapScaled).attr("x2",n.xmapScaled).attr("y1",0+this.topOffset).attr("y2",this.height),this.hoverx.attr("x",n.xmapScaled).attr("y",this.topOffset-5).text(this.xtickFormat(n.xmap)),this.hoverxOutline.attr("x",n.xmapScaled).attr("y",this.topOffset-5).text(this.xtickFormat(n.xmap)),this.hoverxTitle.attr("x",n.xmapScaled).attr("y",this.topOffset-18).text(n.count>1?n.count+" averaged samples":""),this.hovery.attr("x",this.leftOffset-6).attr("y",n.joinPointy).text(this.ytickFormat(this.invLinkFunction(n.joinPoint))),this.hoveryOutline.attr("x",this.leftOffset-6).attr("y",n.joinPointy).text(this.ytickFormat(this.invLinkFunction(n.joinPoint)));for(var i,o,u=[],l=this.currPosOrderedFeatures.length-1;l>=0;--l){var s=this.currPosOrderedFeatures[l],c=n.features[s];o=5+(c.posyTop+c.posyBottom)/2,(!i||o-i>=15)&&c.posyTop-c.posyBottom>=6&&(u.push(c),i=o)}var f=[];i=void 0;var d,p=Ya(this.currNegOrderedFeatures);try{for(p.s();!(d=p.n()).done;){var h=d.value,g=n.features[h];o=5+(g.negyTop+g.negyBottom)/2,(!i||i-o>=15)&&g.negyTop-g.negyBottom>=6&&(f.push(g),i=o)}}catch(e){p.e(e)}finally{p.f()}var v=function(e){var t="";return null!==e.value&&void 0!==e.value&&(t=" = "+(isNaN(e.value)?e.value:r.ytickFormat(e.value))),n.count>1?"mean("+r.props.featureNames[e.ind]+")"+t:r.props.featureNames[e.ind]+t},m=this.hoverGroup1.selectAll(".pos-values").data(u);m.enter().append("text").attr("class","pos-values").merge(m).attr("x",n.xmapScaled+5).attr("y",(function(e){return 4+(e.posyTop+e.posyBottom)/2})).attr("text-anchor","start").attr("font-size",12).attr("stroke","#fff").attr("fill","#fff").attr("stroke-width","4").attr("stroke-linejoin","round").attr("opacity",1).text(v),m.exit().remove();var y=this.hoverGroup2.selectAll(".pos-values").data(u);y.enter().append("text").attr("class","pos-values").merge(y).attr("x",n.xmapScaled+5).attr("y",(function(e){return 4+(e.posyTop+e.posyBottom)/2})).attr("text-anchor","start").attr("font-size",12).attr("fill",this.colors[0]).text(v),y.exit().remove();var b=this.hoverGroup1.selectAll(".neg-values").data(f);b.enter().append("text").attr("class","neg-values").merge(b).attr("x",n.xmapScaled+5).attr("y",(function(e){return 4+(e.negyTop+e.negyBottom)/2})).attr("text-anchor","start").attr("font-size",12).attr("stroke","#fff").attr("fill","#fff").attr("stroke-width","4").attr("stroke-linejoin","round").attr("opacity",1).text(v),b.exit().remove();var w=this.hoverGroup2.selectAll(".neg-values").data(f);w.enter().append("text").attr("class","neg-values").merge(w).attr("x",n.xmapScaled+5).attr("y",(function(e){return 4+(e.negyTop+e.negyBottom)/2})).attr("text-anchor","start").attr("font-size",12).attr("fill",this.colors[1]).text(v),w.exit().remove()}}},{key:"draw",value:function(){var e=this;if(this.props.explanations&&0!==this.props.explanations.length){(0,je.each)(this.props.explanations,(function(e,t){return e.origInd=t}));var t,n={},r={},a={},i=Ya(this.props.explanations);try{for(i.s();!(t=i.n()).done;){var o=t.value;for(var u in o.features)void 0===n[u]&&(n[u]=0,r[u]=0,a[u]=0),o.features[u].effect>0?n[u]+=o.features[u].effect:r[u]-=o.features[u].effect,null!==o.features[u].value&&void 0!==o.features[u].value&&(a[u]+=1)}}catch(e){i.e(e)}finally{i.f()}this.usedFeatures=(0,je.sortBy)((0,je.keys)(n),(function(e){return-(n[e]+r[e])})),console.log("found ",this.usedFeatures.length," used features"),this.posOrderedFeatures=(0,je.sortBy)(this.usedFeatures,(function(e){return n[e]})),this.negOrderedFeatures=(0,je.sortBy)(this.usedFeatures,(function(e){return-r[e]})),this.singleValueFeatures=(0,je.filter)(this.usedFeatures,(function(e){return a[e]>0}));var l=["sample order by similarity","sample order by output value","original sample ordering"].concat(this.singleValueFeatures.map((function(t){return e.props.featureNames[t]})));null!=this.props.ordering_keys&&l.unshift("sample order by key");var s=this.xlabel.selectAll("option").data(l);s.enter().append("option").merge(s).attr("value",(function(e){return e})).text((function(e){return e})),s.exit().remove();var c=this.props.outNames[0]?this.props.outNames[0]:"model output value";(l=(0,je.map)(this.usedFeatures,(function(t){return[e.props.featureNames[t],e.props.featureNames[t]+" effects"]}))).unshift(["model output value",c]);var f=this.ylabel.selectAll("option").data(l);f.enter().append("option").merge(f).attr("value",(function(e){return e[0]})).text((function(e){return e[1]})),f.exit().remove(),this.ylabel.style("top",(this.height-10-this.topOffset)/2+this.topOffset+"px").style("left",10-this.ylabel.node().offsetWidth/2+"px"),this.internalDraw()}}},{key:"internalDraw",value:function(){var e,t,n=this,r=Ya(this.props.explanations);try{for(r.s();!(e=r.n()).done;){var a,i=e.value,o=Ya(this.usedFeatures);try{for(o.s();!(a=o.n()).done;){var u=a.value;i.features.hasOwnProperty(u)||(i.features[u]={effect:0,value:0}),i.features[u].ind=u}}catch(e){o.e(e)}finally{o.f()}}}catch(e){r.e(e)}finally{r.f()}var l=this.xlabel.node().value,s="sample order by key"===l&&null!=this.props.ordering_keys_time_format;if(this.xscale=s?Qa():Re(),this.xaxis.scale(this.xscale),"sample order by similarity"===l)t=(0,je.sortBy)(this.props.explanations,(function(e){return e.simIndex})),(0,je.each)(t,(function(e,t){return e.xmap=t}));else if("sample order by output value"===l)t=(0,je.sortBy)(this.props.explanations,(function(e){return-e.outValue})),(0,je.each)(t,(function(e,t){return e.xmap=t}));else if("original sample ordering"===l)t=(0,je.sortBy)(this.props.explanations,(function(e){return e.origInd})),(0,je.each)(t,(function(e,t){return e.xmap=t}));else if("sample order by key"===l)t=this.props.explanations,s?(0,je.each)(t,(function(e,t){return e.xmap=n.parseTime(n.props.ordering_keys[t])})):(0,je.each)(t,(function(e,t){return e.xmap=n.props.ordering_keys[t]})),t=(0,je.sortBy)(t,(function(e){return e.xmap}));else{var c=(0,je.findKey)(this.props.featureNames,(function(e){return e===l}));(0,je.each)(this.props.explanations,(function(e,t){return e.xmap=e.features[c].value}));var f=(0,je.sortBy)(this.props.explanations,(function(e){return e.xmap})),d=(0,je.map)(f,(function(e){return e.xmap}));if("string"==typeof d[0])return void alert("Ordering by category names is not yet supported.");var p,h,g=(0,je.min)(d),v=((0,je.max)(d)-g)/100;t=[];for(var m=0;m<f.length;++m){var y=f[m];if(p&&!h&&y.xmap-p.xmap<=v||h&&y.xmap-h.xmap<=v){h||((h=(0,je.cloneDeep)(p)).count=1);var b,w=Ya(this.usedFeatures);try{for(w.s();!(b=w.n()).done;){var _=b.value;h.features[_].effect+=y.features[_].effect,h.features[_].value+=y.features[_].value}}catch(e){w.e(e)}finally{w.f()}h.count+=1}else if(p)if(h){var k,x=Ya(this.usedFeatures);try{for(x.s();!(k=x.n()).done;){var S=k.value;h.features[S].effect/=h.count,h.features[S].value/=h.count}}catch(e){x.e(e)}finally{x.f()}t.push(h),h=void 0}else t.push(p);p=y}p.xmap-t[t.length-1].xmap>v&&t.push(p)}this.currUsedFeatures=this.usedFeatures,this.currPosOrderedFeatures=this.posOrderedFeatures,this.currNegOrderedFeatures=this.negOrderedFeatures;var E=this.ylabel.node().value;if("model output value"!==E){var C=t;t=(0,je.cloneDeep)(t);for(var T=(0,je.findKey)(this.props.featureNames,(function(e){return e===E})),P=0;P<t.length;++P){var z=t[P].features[T];t[P].features={},t[P].features[T]=z,C[P].remapped_version=t[P]}this.currUsedFeatures=[T],this.currPosOrderedFeatures=[T],this.currNegOrderedFeatures=[T]}this.currExplanations=t,"identity"===this.props.link?this.invLinkFunction=function(e){return n.props.baseValue+e}:"logit"===this.props.link?this.invLinkFunction=function(e){return 1/(1+Math.exp(-(n.props.baseValue+e)))}:console.log("ERROR: Unrecognized link function: ",this.props.link),this.predValues=(0,je.map)(t,(function(e){return(0,je.sum)((0,je.map)(e.features,(function(e){return e.effect})))}));var M=this.wrapper.node().offsetWidth;if(0==M)return setTimeout((function(){return n.draw(t)}),500);this.svg.style("height",this.height+"px"),this.svg.style("width",M+"px");var N=(0,je.map)(t,(function(e){return e.xmap}));this.xscale.domain([(0,je.min)(N),(0,je.max)(N)]).range([this.leftOffset,M]).clamp(!0),this.xaxisElement.attr("transform","translate(0,"+this.topOffset+")").call(this.xaxis);for(var A=0;A<this.currExplanations.length;++A)this.currExplanations[A].xmapScaled=this.xscale(this.currExplanations[A].xmap);for(var O=t.length,L=0,F=0;F<O;++F){var D=t[F].features,R=(0,je.sum)((0,je.map)((0,je.filter)(D,(function(e){return e.effect>0})),(function(e){return e.effect})))||0,j=(0,je.sum)((0,je.map)((0,je.filter)(D,(function(e){return e.effect<0})),(function(e){return-e.effect})))||0;L=Math.max(L,2.2*Math.max(R,j))}this.yscale.domain([-L/2,L/2]).range([this.height-10,this.topOffset]),this.yaxisElement.attr("transform","translate("+this.leftOffset+",0)").call(this.yaxis);for(var U=0;U<O;++U){var I,$=t[U].features,B=-((0,je.sum)((0,je.map)((0,je.filter)($,(function(e){return e.effect<0})),(function(e){return-e.effect})))||0),H=void 0,W=Ya(this.currPosOrderedFeatures);try{for(W.s();!(I=W.n()).done;)$[H=I.value].posyTop=this.yscale(B),$[H].effect>0&&(B+=$[H].effect),$[H].posyBottom=this.yscale(B),$[H].ind=H}catch(e){W.e(e)}finally{W.f()}var V,q=B,Q=Ya(this.currNegOrderedFeatures);try{for(Q.s();!(V=Q.n()).done;)$[H=V.value].negyTop=this.yscale(B),$[H].effect<0&&(B-=$[H].effect),$[H].negyBottom=this.yscale(B)}catch(e){Q.e(e)}finally{Q.f()}t[U].joinPoint=q,t[U].joinPointy=this.yscale(q)}var Y=xn().x((function(e){return e[0]})).y((function(e){return e[1]})),G=this.mainGroup.selectAll(".force-bar-array-area-pos").data(this.currUsedFeatures);G.enter().append("path").attr("class","force-bar-array-area-pos").merge(G).attr("d",(function(e){var n=(0,je.map)((0,je.range)(O),(function(n){return[t[n].xmapScaled,t[n].features[e].posyTop]})),r=(0,je.map)((0,je.rangeRight)(O),(function(n){return[t[n].xmapScaled,t[n].features[e].posyBottom]}));return Y(n.concat(r))})).attr("fill",this.colors[0]),G.exit().remove();var K=this.mainGroup.selectAll(".force-bar-array-area-neg").data(this.currUsedFeatures);K.enter().append("path").attr("class","force-bar-array-area-neg").merge(K).attr("d",(function(e){var n=(0,je.map)((0,je.range)(O),(function(n){return[t[n].xmapScaled,t[n].features[e].negyTop]})),r=(0,je.map)((0,je.rangeRight)(O),(function(n){return[t[n].xmapScaled,t[n].features[e].negyBottom]}));return Y(n.concat(r))})).attr("fill",this.colors[1]),K.exit().remove();var X=this.mainGroup.selectAll(".force-bar-array-divider-pos").data(this.currUsedFeatures);X.enter().append("path").attr("class","force-bar-array-divider-pos").merge(X).attr("d",(function(e){var n=(0,je.map)((0,je.range)(O),(function(n){return[t[n].xmapScaled,t[n].features[e].posyBottom]}));return Y(n)})).attr("fill","none").attr("stroke-width",1).attr("stroke",(function(){return n.colors[0].brighter(1.2)})),X.exit().remove();var Z=this.mainGroup.selectAll(".force-bar-array-divider-neg").data(this.currUsedFeatures);Z.enter().append("path").attr("class","force-bar-array-divider-neg").merge(Z).attr("d",(function(e){var n=(0,je.map)((0,je.range)(O),(function(n){return[t[n].xmapScaled,t[n].features[e].negyTop]}));return Y(n)})).attr("fill","none").attr("stroke-width",1).attr("stroke",(function(){return n.colors[1].brighter(1.5)})),Z.exit().remove();for(var J=function(e,t,n,r,a){var i,o,u,l;"pos"===a?(i=e[n].features[t].posyBottom,o=e[n].features[t].posyTop):(i=e[n].features[t].negyBottom,o=e[n].features[t].negyTop);for(var s=n+1;s<=r;++s)"pos"===a?(u=e[s].features[t].posyBottom,l=e[s].features[t].posyTop):(u=e[s].features[t].negyBottom,l=e[s].features[t].negyTop),u>i&&(i=u),l<o&&(o=l);return{top:i,bottom:o}},ee=[],te=0,ne=["pos","neg"];te<ne.length;te++){var re,ae=ne[te],ie=Ya(this.currUsedFeatures);try{for(ie.s();!(re=ie.n()).done;)for(var oe=re.value,ue=0,le=0,se=0,ce={top:0,bottom:0},fe=void 0;le<O-1;){for(;se<100&&le<O-1;)++le,se=t[le].xmapScaled-t[ue].xmapScaled;for(ce=J(t,oe,ue,le,ae);ce.bottom-ce.top<20&&ue<le;)++ue,ce=J(t,oe,ue,le,ae);if(se=t[le].xmapScaled-t[ue].xmapScaled,ce.bottom-ce.top>=20&&se>=100){for(;le<O-1;){if(++le,!((fe=J(t,oe,ue,le,ae)).bottom-fe.top>20)){--le;break}ce=fe}se=t[le].xmapScaled-t[ue].xmapScaled,ee.push([(t[le].xmapScaled+t[ue].xmapScaled)/2,(ce.top+ce.bottom)/2,this.props.featureNames[oe]]);var de=t[le].xmapScaled;for(ue=le;de+100>t[ue].xmapScaled&&ue<O-1;)++ue;le=ue}}}catch(e){ie.e(e)}finally{ie.f()}}var pe=this.onTopGroup.selectAll(".force-bar-array-flabels").data(ee);pe.enter().append("text").attr("class","force-bar-array-flabels").merge(pe).attr("x",(function(e){return e[0]})).attr("y",(function(e){return e[1]+4})).text((function(e){return e[2]})),pe.exit().remove()}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.redraw)}},{key:"render",value:function(){var t=this;return e.createElement("div",{ref:function(e){return t.wrapper=tn(e)},style:{textAlign:"center"}},e.createElement("style",{dangerouslySetInnerHTML:{__html:"\n          .force-bar-array-wrapper {\n            text-align: center;\n          }\n          .force-bar-array-xaxis path {\n            fill: none;\n            opacity: 0.4;\n          }\n          .force-bar-array-xaxis .domain {\n            opacity: 0;\n          }\n          .force-bar-array-xaxis paths {\n            display: none;\n          }\n          .force-bar-array-yaxis path {\n            fill: none;\n            opacity: 0.4;\n          }\n          .force-bar-array-yaxis paths {\n            display: none;\n          }\n          .tick line {\n            stroke: #000;\n            stroke-width: 1px;\n            opacity: 0.4;\n          }\n          .tick text {\n            fill: #000;\n            opacity: 0.5;\n            font-size: 12px;\n            padding: 0px;\n          }\n          .force-bar-array-flabels {\n            font-size: 12px;\n            fill: #fff;\n            text-anchor: middle;\n          }\n          .additive-force-array-xlabel {\n            background: none;\n            border: 1px solid #ccc;\n            opacity: 0.5;\n            margin-bottom: 0px;\n            font-size: 12px;\n            font-family: arial;\n            margin-left: 80px;\n            max-width: 300px;\n          }\n          .additive-force-array-xlabel:focus {\n            outline: none;\n          }\n          .additive-force-array-ylabel {\n            position: relative;\n            top: 0px;\n            left: 0px;\n            transform: rotate(-90deg);\n            background: none;\n            border: 1px solid #ccc;\n            opacity: 0.5;\n            margin-bottom: 0px;\n            font-size: 12px;\n            font-family: arial;\n            max-width: 150px;\n          }\n          .additive-force-array-ylabel:focus {\n            outline: none;\n          }\n          .additive-force-array-hoverLine {\n            stroke-width: 1px;\n            stroke: #fff;\n            opacity: 1;\n          }"}}),e.createElement("select",{className:"additive-force-array-xlabel"}),e.createElement("div",{style:{height:"0px",textAlign:"left"}},e.createElement("select",{className:"additive-force-array-ylabel"})),e.createElement("svg",{ref:function(e){return t.svg=tn(e)},style:{userSelect:"none",display:"block",fontFamily:"arial",sansSerif:!0}}))}}],a&&Xa(r.prototype,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,a}(e.Component);ni.defaultProps={plot_cmap:"RdBu",ordering_keys:null,ordering_keys_time_format:null};const ri=ni;window.SHAP={SimpleListVisualizer:Qe,AdditiveForceVisualizer:Nn,AdditiveForceArrayVisualizer:ri,React:e,ReactDOM:n,ReactDom:{render:function(e,n){var r=t.createRoot(n);return r.render(e),r}}}})()})();
