#!/usr/bin/env python3
"""
Test individual app components to verify they work correctly
"""

import sys
import os
import pandas as pd

# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

def test_emission_calculator_columns():
    """Test that emission calculator produces correct column names"""
    print("🧪 Testing Emission Calculator Column Names...")
    
    try:
        from utils.emission_calculator import EmissionCalculator
        
        calculator = EmissionCalculator()
        
        # Test with sample data
        sample_data = pd.read_csv('data/combined_test_features.csv').head(3)
        results = calculator.predict_and_calculate_emissions(sample_data)
        
        print(f"✅ Generated {len(results)} predictions")
        print(f"✅ Columns: {list(results.columns)}")
        
        # Check for expected columns
        expected_cols = ['scope1_total', 'scope2_total', 'scope3_total', 'total_emissions']
        missing_cols = [col for col in expected_cols if col not in results.columns]
        
        if missing_cols:
            print(f"❌ Missing columns: {missing_cols}")
            return False
        else:
            print("✅ All expected columns present!")
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_historical_data_columns():
    """Test that historical data has correct column names"""
    print("🧪 Testing Historical Data Column Names...")
    
    try:
        from utils.historical_data import HistoricalDataManager
        
        manager = HistoricalDataManager()
        historical_data = manager.get_historical_data()
        
        print(f"✅ Loaded {len(historical_data)} historical records")
        print(f"✅ Columns: {list(historical_data.columns)}")
        
        # Check for expected columns
        expected_cols = ['scope1_emissions', 'scope2_emissions', 'scope3_emissions', 'total_emissions']
        missing_cols = [col for col in expected_cols if col not in historical_data.columns]
        
        if missing_cols:
            print(f"❌ Missing columns: {missing_cols}")
            return False
        else:
            print("✅ All expected columns present!")
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_sample_prediction_flow():
    """Test the complete prediction flow"""
    print("🧪 Testing Complete Prediction Flow...")
    
    try:
        from utils.emission_calculator import EmissionCalculator
        
        calculator = EmissionCalculator()
        
        # Create sample input data
        sample_input = pd.DataFrame({
            'datacenter_id': ['DC_001', 'DC_002'],
            'location_country': ['Australia', 'India'],
            'date': ['2025-01-01', '2025-01-02']
        })
        
        # Generate predictions
        results = calculator.predict_and_calculate_emissions(sample_input)
        
        print(f"✅ Generated predictions for {len(results)} records")
        print("✅ Sample results:")
        print(results[['datacenter_id', 'scope1_total', 'scope2_total', 'scope3_total', 'total_emissions']].head())
        
        # Verify data types and values
        numeric_cols = ['scope1_total', 'scope2_total', 'scope3_total', 'total_emissions']
        for col in numeric_cols:
            if col in results.columns:
                if results[col].dtype in ['float64', 'int64'] and results[col].notna().all():
                    print(f"✅ {col}: Valid numeric data")
                else:
                    print(f"❌ {col}: Invalid data type or contains NaN")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all component tests"""
    print("🚀 Testing ESG App Components")
    print("=" * 50)
    
    tests = [
        ("Historical Data Columns", test_historical_data_columns),
        ("Emission Calculator Columns", test_emission_calculator_columns),
        ("Complete Prediction Flow", test_sample_prediction_flow),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} Test...")
        results[test_name] = test_func()
        print("-" * 30)
    
    # Summary
    print("\n📊 Test Results Summary:")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All component tests passed! App should work correctly.")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
