#!/usr/bin/env python3
"""
Test script for ESG Emission Prediction System
This script tests the core functionality of the system
"""

import sys
import os
import pandas as pd
import numpy as np

# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

def test_data_preprocessing():
    """Test data preprocessing pipeline"""
    print("🧪 Testing Data Preprocessing...")
    
    try:
        from utils.data_preprocessing import ESGDataPreprocessor
        
        preprocessor = ESGDataPreprocessor()
        
        # Test loading training data
        df1, df2, df3 = preprocessor.load_training_data()
        print(f" Training data loaded: Scope1={len(df1)}, Scope2={len(df2)}, Scope3={len(df3)}")
        
        # Test preprocessing steps
        processed_dfs = preprocessor.preprocess_training_data()
        print(" Data preprocessing completed successfully")
        
        return True
        
    except Exception as e:
        print(f" Data preprocessing test failed: {e}")
        return False

def test_model_training():
    """Test model training pipeline"""
    print(" Testing Model Training...")
    
    try:
        from utils.model_training import ESGModelTrainer
        
        trainer = ESGModelTrainer()
        
        # Train models (this will take some time)
        print(" Training models... (this may take a few minutes)")
        models = trainer.train_all_models()
        
        # Save models
        trainer.save_models()
        print(" Model training and saving completed successfully")
        
        return True
        
    except Exception as e:
        print(f" Model training test failed: {e}")
        return False

def test_emission_calculator():
    """Test emission calculator"""
    print(" Testing Emission Calculator...")
    
    try:
        from utils.emission_calculator import EmissionCalculator
        
        calculator = EmissionCalculator()
        
        # Test with sample data
        sample_data = pd.read_csv('data/sample_test_features.csv').head(5)
        
        # Test prediction and calculation
        results = calculator.predict_and_calculate_emissions(sample_data)
        print(f" Emission calculation completed: {len(results)} predictions generated")
        
        return True
        
    except Exception as e:
        print(f" Emission calculator test failed: {e}")
        return False

def test_historical_data():
    """Test historical data manager"""
    print(" Testing Historical Data Manager...")
    
    try:
        from utils.historical_data import HistoricalDataManager
        
        manager = HistoricalDataManager()
        
        # Test data retrieval
        historical_data = manager.get_historical_data()
        print(f" Historical data loaded: {len(historical_data)} records")
        
        # Test aggregations
        daily_agg = manager.get_daily_aggregates()
        datacenter_summary = manager.get_datacenter_summary()
        
        print(f" Data aggregations completed: {len(daily_agg)} daily records, {len(datacenter_summary)} datacenter summaries")
        
        return True
        
    except Exception as e:
        print(f" Historical data test failed: {e}")
        return False

def test_system_integration():
    """Test full system integration"""
    print(" Testing System Integration...")
    
    try:
        # Test that all components can be imported together
        from utils.data_preprocessing import ESGDataPreprocessor
        from utils.model_training import ESGModelTrainer
        from utils.emission_calculator import EmissionCalculator
        from utils.historical_data import HistoricalDataManager
        
        print(" All modules imported successfully")
        
        # Test basic initialization
        preprocessor = ESGDataPreprocessor()
        trainer = ESGModelTrainer()
        calculator = EmissionCalculator()
        manager = HistoricalDataManager()
        
        print(" All components initialized successfully")
        
        return True
        
    except Exception as e:
        print(f" System integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print(" Starting ESG System Tests")
    print("=" * 50)
    
    tests = [
        ("Data Preprocessing", test_data_preprocessing),
        ("Historical Data", test_historical_data),
        ("System Integration", test_system_integration),
        ("Model Training", test_model_training),
        ("Emission Calculator", test_emission_calculator),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n Running {test_name} Test...")
        results[test_name] = test_func()
        print("-" * 30)
    
    # Summary
    print("\n Test Results Summary:")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = " PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("All tests passed! System is ready to use.")
        print("\n To run the application:")
        print("streamlit run app.py")
    else:
        print("  Some tests failed. Please check the errors above.")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
