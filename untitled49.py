# -*- coding: utf-8 -*-
"""Untitled49.ipynb

Automatically generated by <PERSON>b.

Original file is located at
    https://colab.research.google.com/drive/1SaVbWn-5tmW3qyT7nwhDWr9oDlizI1aI
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

pd.set_option('display.max_columns', None)

df1 = pd.read_csv('/content/scope1_synthetic_2023_2024_v2.csv')
df2 = pd.read_csv('/content/scope2_synthetic_2023_2024_daily.csv')
df3 = pd.read_csv('/content/scope3_synthetic_2023_2024.csv')

df1.head()

df2.head()

df3.head()

df1.isna().sum()

df2.isna().sum()

df3.isna().sum()

df1.hist(figsize=(10, 10))
plt.tight_layout()
plt.show()

df2.hist(figsize=(10, 10))
plt.tight_layout()
plt.show()

df3.hist(figsize=(10, 10))
plt.tight_layout()
plt.show()

for col in df1.columns:
  if df1[col].isnull().any():
    if df1[col].dtype == 'object':
      df1[col] = df1[col].fillna('unknown')
    else:
      median_val = df1[col].median()
      df1[col] = df1[col].fillna(median_val)

df1.isna().sum()

for col in df2.columns:
  if df2[col].isnull().any():
    if df2[col].dtype == 'object':
      df2[col] = df2[col].fillna('unknown')
    else:
      median_val = df2[col].median()
      df2[col] = df2[col].fillna(median_val)

df2.isna().sum()

for col in df3.columns:
  if df3[col].isnull().any():
    if df3[col].dtype == 'object':
      df3[col] = df3[col].fillna('unknown')
    else:
      median_val = df3[col].median()
      df3[col] = df3[col].fillna(median_val)

df3.isna().sum()

df1.head()

for df in [df1, df2, df3]:
  df['date'] = pd.to_datetime(df['date'])
  df['year'] = df['date'].dt.year
  df['month'] = df['date'].dt.month
  df['day'] = df['date'].dt.day
  df['dayofweek'] = df['date'].dt.dayofweek   # Monday=0, Sunday=6
  df['weekofyear'] = df['date'].dt.isocalendar().week.astype(int)
  df['quarter'] = df['date'].dt.quarter

import numpy as np

for df in [df1, df2, df3]:
  df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
  df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)

  df['day_sin'] = np.sin(2 * np.pi * df['day'] / 31)
  df['day_cos'] = np.cos(2 * np.pi * df['day'] / 31)

  df['dayofweek_sin'] = np.sin(2 * np.pi * df['dayofweek'] / 7)
  df['dayofweek_cos'] = np.cos(2 * np.pi * df['dayofweek'] / 7)

for df in [df1, df2, df3]:
  df = df.drop(columns='date', errors='ignore')

unique_countries = df1['location_country'].unique()
print("The unique countries in the dataset are:")
for country in unique_countries:
  print(country)

df1.head()

df1.columns.tolist()

!pip install holidays
import holidays

def is_holiday(row):
    country = row['location_country']
    date = row['date']  # Use 'date' column instead of 'week_start'
    try:
        country_holidays = holidays.CountryHoliday(country, years=date.year)
        return 1 if date in country_holidays else 0
    except KeyError:
        return 0 # Return 0 if the country is not supported by the holidays library

df1['isholiday'] = df1.apply(is_holiday, axis=1)
df2['isholiday'] = df2.apply(is_holiday, axis=1)
df3['isholiday'] = df3.apply(is_holiday, axis=1)

display(df1.head())
display(df2.head())
display(df3.head())

df2.head()

categorical_cols_df1 = df1.select_dtypes(include=['object', 'category']).columns.tolist()
if 'datacenter_id' in categorical_cols_df1:
  categorical_cols_df1.remove('datacenter_id')
df1 = pd.get_dummies(df1, columns=categorical_cols_df1)
print("df1 one-hot encoded.")

categorical_cols_df2 = df2.select_dtypes(include=['object', 'category']).columns.tolist()
if 'datacenter_id' in categorical_cols_df2:
  categorical_cols_df2.remove('datacenter_id')
df2 = pd.get_dummies(df2, columns=categorical_cols_df2)
print("df2 one-hot encoded.")

categorical_cols_df3 = df3.select_dtypes(include=['object', 'category']).columns.tolist()
if 'datacenter_id' in categorical_cols_df3:
  categorical_cols_df3.remove('datacenter_id')
df3 = pd.get_dummies(df3, columns=categorical_cols_df3)
print("df3 one-hot encoded.")

df1.head()

from sklearn.preprocessing import StandardScaler

exclude_cols = [
    'datacenter_id',
    'date',
    'year',
    'month',
    'day',
    'dayofweek',
    'weekofyear',
    'quarter',
    'month_sin',
    'month_cos',
    'day_sin',
    'day_cos',
    'dayofweek_sin',
    'dayofweek_cos',
    'isholiday',
    'diesel_fuel_consumption_l_per_day',
    'natural_gas_consumption_m3_per_day',
    'refrigerant_leak_volume_kg_per_year',
    'fire_suppression_discharges_per_year',
    'vehicle_fuel_consumption_l_per_day',
    'total_energy_consumption_kwh_per_day',
    'grid_emission_factor_kgco2perkwh',
    'business_travel_emissions_kgco2',
    'commuting_emissions_kgco2',
    'waste_volume_kg',
    'waste_treatment_emissions_kgco2',
    'product_units_sold',
    'use_phase_emissions_kgco2',
    'end_of_life_emissions_kgco2',
    'purchase_volume_kg',
    'purchased_goods_emissions_kgco2'
]

numerical_cols_df1 = df1.select_dtypes(include=np.number).columns.tolist()
cols_to_scale_df1 = [col for col in numerical_cols_df1 if col not in exclude_cols]

numerical_cols_df2 = df2.select_dtypes(include=np.number).columns.tolist()
cols_to_scale_df2 = [col for col in numerical_cols_df2 if col not in exclude_cols]

numerical_cols_df3 = df3.select_dtypes(include=np.number).columns.tolist()
cols_to_scale_df3 = [col for col in numerical_cols_df3 if col not in exclude_cols]

print("Columns to scale in df1:", cols_to_scale_df1)
print("Columns to scale in df2:", cols_to_scale_df2)
print("Columns to scale in df3:", cols_to_scale_df3)

scaler = StandardScaler()

df1[cols_to_scale_df1] = scaler.fit_transform(df1[cols_to_scale_df1])
df2[cols_to_scale_df2] = scaler.fit_transform(df2[cols_to_scale_df2])
df3[cols_to_scale_df3] = scaler.fit_transform(df3[cols_to_scale_df3])

print("\nScaled DataFrames:")
display(df1.head())
display(df2.head())
display(df3.head())

def time_split_datacenter(df, train_ratio=0.8):
    train_list = []
    test_list = []
    for datacenter in df['datacenter_id'].unique():
        datacenter_df = df[df['datacenter_id'] == datacenter].sort_values(by='date')
        split_index = int(len(datacenter_df) * train_ratio)
        train_list.append(datacenter_df.iloc[:split_index])
        test_list.append(datacenter_df.iloc[split_index:])

    train_df = pd.concat(train_list)
    test_df = pd.concat(test_list)
    return train_df, test_df

# Apply the time split to each dataframe
train_df1, test_df1 = time_split_datacenter(df1)
train_df2, test_df2 = time_split_datacenter(df2)
train_df3, test_df3 = time_split_datacenter(df3)

print("Shape of train_df1:", train_df1.shape)
print("Shape of test_df1:", test_df1.shape)
print("Shape of train_df2:", train_df2.shape)
print("Shape of test_df2:", test_df2.shape)
print("Shape of train_df3:", train_df3.shape)
print("Shape of test_df3:", test_df3.shape)

for df in [df1, df2, df3]:
  bool_cols = df.select_dtypes(include='bool').columns
  for col in bool_cols:
    df[col] = df[col].astype(int)

print("Boolean columns converted to integers.")

df1.head()

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.metrics import mean_squared_error

# Mapping target features (drivers) per emission component
target_features_map_df1 = {
    'diesel_fuel_consumption_l_per_day': [
        'diesel_generator_hours_per_day',
        'generator_capacity_kW',
        'generator_efficiency_pct',
        'ambient_temperature_C',
        'IT_load_kW',
        'climate_zone_Cold',
        'climate_zone_Temperate',
        'climate_zone_Tropical'
    ],
    'natural_gas_consumption_m3_per_day': [
        'boiler_efficiency_pct',
        'ambient_temperature_C',
        'IT_load_kW',
        'climate_zone_Cold',
        'climate_zone_Temperate',
        'climate_zone_Tropical'
    ],
    'refrigerant_leak_volume_kg_per_year': [
        'chiller_type_VRF',
        'chiller_type_Chiller',
        'chiller_type_Packaged',
        'ambient_temperature_C',
        'facility_age_years',
        'refrigerant_type_R-410A',
        'refrigerant_type_HFC-134a',
        'refrigerant_type_R-1234yf',
        'IT_load_kW'
    ],
    'fire_suppression_discharges_per_year': [
        'fire_suppression_type_FM-200',
        'fire_suppression_type_Novec',
        'facility_age_years'
    ],
    'vehicle_fuel_consumption_l_per_day': [
        'fleet_vehicle_count',
        'avg_km_per_vehicle_per_day',
        'fuel_efficiency_kmpl',
        'vehicle_fuel_type_hybrid',
        'vehicle_fuel_type_diesel',
        'vehicle_fuel_type_petrol'
    ]
}

# Add temporal features
cyclic_date_features = [
    'month_sin', 'month_cos',
    'day_sin', 'day_cos',
    'dayofweek_sin', 'dayofweek_cos',
    'weekofyear', 'quarter', 'year', 'isholiday'
]

# Add date features to all models
for target in target_features_map_df1:
    target_features_map_df1[target].extend(cyclic_date_features)


# Initialize
models_df1 = {}
predictions_df1 = pd.DataFrame(index=test_df1.index)

# Model training loop
for target, features in target_features_map_df1.items():
    print(f"🔧 Training model for target: {target}")

    # Train-test split
    X_train = train_df1[features]
    y_train = train_df1[target]
    X_test = test_df1[features]
    y_test = test_df1[target]

    # XGBoost Regressor
    model = xgb.XGBRegressor(
        objective='reg:squarederror',
        n_estimators=1000,
        learning_rate=0.05,
        random_state=42,
        n_jobs=-1
    )

    model.fit(X_train, y_train)
    predictions_df1[target] = model.predict(X_test)
    models_df1[target] = model

    # RMSE
    rmse = np.sqrt(mean_squared_error(y_test, predictions_df1[target]))
    print(f"RMSE for {target}: {rmse:.4f}")

# import xgboost as xgb
# from sklearn.metrics import mean_squared_error

# target_features_map_df1 = {
#     'diesel_fuel_consumption_l_per_day': [
#         'diesel_generator_hours_per_day',
#         'generator_capacity_kW',
#         'generator_efficiency_pct',
#         'annual_generator_tests_count',
#         'ambient_temperature_C',
#         'IT_load_kW',
#         'climate_zone_Cold',
#         'climate_zone_Temperate',
#         'climate_zone_Tropical'
#     ],
#     'natural_gas_consumption_m3_per_day': [
#         'boiler_efficiency_pct',
#         'ambient_temperature_C',
#         'IT_load_kW',
#         'climate_zone_Cold',
#         'climate_zone_Temperate',
#         'climate_zone_Tropical'
#     ],
#     'refrigerant_leak_volume_kg_per_year': [
#         'chiller_type_VRF',
#         'chiller_type_Chiller',
#         'chiller_type_Packaged',
#         'ambient_temperature_C',
#         'facility_age_years',
#         'refrigerant_type_R-410A',
#         'refrigerant_type_HFC-134a',
#         'refrigerant_type_R-1234yf',
#         'IT_load_kW'
#     ],
#     'fire_suppression_discharges_per_year': [
#         'fire_suppression_type_FM-200',
#         'fire_suppression_type_Novec'
#     ],
#     'vehicle_fuel_consumption_l_per_day': [
#         'fleet_vehicle_count',
#         'avg_km_per_vehicle_per_day',
#         'fuel_efficiency_kmpl',
#         'vehicle_fuel_type_hybrid',
#         'vehicle_fuel_type_diesel',
#         'vehicle_fuel_type_petrol'
#     ]
# }

# cyclic_date_features = [
#     'month_sin', 'month_cos',
#     'day_sin', 'day_cos',
#     'dayofweek_sin', 'dayofweek_cos',
#     'weekofyear', 'quarter', 'year', 'isholiday'
# ]

# for target in target_features_map_df1:
#     target_features_map_df1[target].extend(cyclic_date_features)

# models_df1 = {}
# predictions_df1 = pd.DataFrame(index=test_df1.index)

# for target, features in target_features_map_df1.items():
#     print(f"Training model for target: {target}")
#     X_train = train_df1[features]
#     y_train = train_df1[target]
#     X_test = test_df1[features]
#     y_test = test_df1[target]

#     model = xgb.XGBRegressor(objective='reg:squarederror', n_estimators=1000, learning_rate=0.05, random_state=42, n_jobs=-1)
#     model.fit(X_train, y_train)

#     predictions_df1[target] = model.predict(X_test)
#     models_df1[target] = model

#     rmse = np.sqrt(mean_squared_error(y_test, predictions_df1[target]))
#     print(f"RMSE for {target}: {rmse:.4f}")

# print("\nTraining complete for df1.")

from sklearn.metrics import r2_score

print("R2 scores for df1 predictions:")
for target in target_features_map_df1:
    r2 = r2_score(test_df1[target], predictions_df1[target])
    print(f"R2 for {target}: {r2:.4f}")

target_features_map_df2 = {
    'total_energy_consumption_kwh_per_day': [
        'it_load_kw',
        'power_usage_effectiveness',
        'cooling_system_power_draw_kW',
        'UPS_loss_factor',
        'ambient_temperature_C',
        'location_country_Australia',
        'location_country_India',
        'location_country_UK',
        'climate_zone_Cold',
        'climate_zone_Temperate',
        'climate_zone_Tropical',
    ],
    'on_site_solar_generation_kWh_per_day': [
        'solar_capacity_kW',
        'sunlight_hours_per_day',
        'solar_panel_efficiency_pct'
    ],
    'grid_emission_factor_kgco2perkwh': [
        'location_country_Australia',
        'location_country_India',
        'location_country_UK',
        'climate_zone_Cold',
        'climate_zone_Temperate',
        'climate_zone_Tropical',
    ]
}

cyclic_date_features = [
    'month_sin', 'month_cos',
    'day_sin', 'day_cos',
    'dayofweek_sin', 'dayofweek_cos',
    'weekofyear', 'quarter', 'year', 'isholiday'
]

for target in target_features_map_df2:
    target_features_map_df2[target].extend(cyclic_date_features)

models_df2 = {}
predictions_df2 = pd.DataFrame(index=test_df2.index)

for target, features in target_features_map_df2.items():
    print(f"Training XGBoost model for: {target} using relevant features.")

    X_train_relevant = train_df2[features]
    y_train_relevant = train_df2[target]
    X_test_relevant = test_df2[features]
    y_test_relevant = test_df2[target]

    model = xgb.XGBRegressor(objective='reg:squarederror', n_estimators=100, random_state=42)
    model.fit(X_train_relevant, y_train_relevant)
    predictions = model.predict(X_test_relevant)
    models_df2[target] = model
    predictions_df2[target] = predictions

    rmse = np.sqrt(mean_squared_error(y_test_relevant, predictions))
    r2 = r2_score(y_test_relevant, predictions)

    print(f"RMSE for {target}: {rmse:.4f}")
    print(f"R2 for {target}: {r2:.4f}")

print("\nTraining complete for df2.")

df3.head()

target_features_map_df3 = {
    'business_travel_emissions_kgco2': [
        'employee_count',
        'travel_km_per_trip',
        'travel_mode_air',
        'travel_mode_car',
        'travel_mode_rail'
    ],
    'commuting_emissions_kgco2': [
        'employee_count',
        'travel_km_per_trip',
        'travel_mode_air',
        'travel_mode_car',
        'travel_mode_rail'
    ],
    'waste_treatment_emissions_kgco2': [
        'waste_volume_kg',
        'waste_type_e-waste',
        'waste_type_mixed',
        'waste_type_organic',
        'waste_type_plastic'
    ],
    'use_phase_emissions_kgco2': [
        'product_units_sold',
        'product_use_duration_yrs',
    ],
    'end_of_life_emissions_kgco2': [
        'product_units_sold',
        'product_use_duration_yrs',
    ],
    'purchased_goods_emissions_kgco2': [
        'purchase_volume_kg'
    ]
}

cyclic_date_features = [
    'month_sin', 'month_cos',
    'day_sin', 'day_cos',
    'dayofweek_sin', 'dayofweek_cos',
    'weekofyear', 'quarter', 'year', 'isholiday'
]

for target in target_features_map_df3:
    target_features_map_df3[target].extend(cyclic_date_features)

models_df3 = {}
predictions_df3 = pd.DataFrame(index=test_df3.index)

for target, features in target_features_map_df3.items():
    print(f"Training XGBoost model for: {target} using relevant features.")

    X_train_relevant = train_df3[features]
    y_train_relevant = train_df3[target]
    X_test_relevant = test_df3[features]
    y_test_relevant = test_df3[target]

    model = xgb.XGBRegressor(objective='reg:squarederror', n_estimators=100, random_state=42)
    model.fit(X_train_relevant, y_train_relevant)
    predictions = model.predict(X_test_relevant)
    models_df3[target] = model
    predictions_df3[target] = predictions

    rmse = np.sqrt(mean_squared_error(y_test_relevant, predictions))
    r2 = r2_score(y_test_relevant, predictions)

    print(f"RMSE for {target}: {rmse:.4f}")
    print(f"R2 for {target}: {r2:.4f}")

print("\nTraining complete for df3.")

!pip install shap

import matplotlib.pyplot as plt
import shap

def shap_explanation(model, X_test, feature_names):

    explainer = shap.TreeExplainer(model)
    shap_values = explainer.shap_values(X_test)
    shap.summary_plot(shap_values, X_test, feature_names=feature_names)

for target, model in models_df1.items():
    print(f"Generating SHAP plot for: {target}")
    relevant_features = target_features_map_df1[target]
    X_test_relevant = test_df1[relevant_features]
    shap_explanation(model, X_test_relevant, relevant_features)

for target, model in models_df2.items():
    print(f"Generating SHAP plot for: {target}")
    relevant_features = target_features_map_df2[target]
    X_test_relevant = test_df2[relevant_features]
    shap_explanation(model, X_test_relevant, relevant_features)

for target, model in models_df3.items():
    print(f"Generating SHAP plot for: {target}")
    relevant_features = target_features_map_df3[target]
    X_test_relevant = test_df3[relevant_features]
    shap_explanation(model, X_test_relevant, relevant_features)