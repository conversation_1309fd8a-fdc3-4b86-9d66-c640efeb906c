import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import sys
import os
import shap
import matplotlib.pyplot as plt

# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

try:
    from utils.emission_calculator import EmissionCalculator
    from utils.historical_data import HistoricalDataManager
    from utils.model_training import ESGModelTrainer
    from utils.data_preprocessing import ESGDataPreprocessor
except ImportError as e:
    st.error(f"Error importing modules: {e}")
    st.error("Please ensure all utility modules are properly installed and accessible.")
    st.stop()

# Page configuration
st.set_page_config(
    page_title="ESG Emission Prediction Dashboard",
    page_icon="",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'uploaded_data' not in st.session_state:
    st.session_state.uploaded_data = None
if 'predictions' not in st.session_state:
    st.session_state.predictions = None
if 'driver_predictions' not in st.session_state:
    st.session_state.driver_predictions = None
if 'models_trained' not in st.session_state:
    st.session_state.models_trained = False

# Initialize components
@st.cache_resource
def initialize_components():
    try:
        calculator = EmissionCalculator()
        historical_manager = HistoricalDataManager()
        model_trainer = ESGModelTrainer()
        preprocessor = ESGDataPreprocessor()

        # Load models if they exist
        if os.path.exists('models/scope1_models.pkl'):
            try:
                model_trainer.load_models()
                print("✅ Models loaded in model_trainer")
            except Exception as e:
                print(f"⚠️ Error loading models in model_trainer: {e}")

        return calculator, historical_manager, model_trainer, preprocessor
    except Exception as e:
        st.error(f"Error initializing components: {e}")
        st.error("This might be because models haven't been trained yet. Please train models first.")
        return None, None, None, None

calculator, historical_manager, model_trainer, preprocessor = initialize_components()

# Sidebar navigation
st.sidebar.title(" ESG Dashboard")

# Model training section in sidebar
st.sidebar.markdown("---")
st.sidebar.subheader(" Model Management")

if st.sidebar.button("Train Models", type="primary"):
    if model_trainer is not None:
        with st.spinner("Training models... This may take a few minutes."):
            try:
                models = model_trainer.train_all_models()
                model_trainer.save_models()
                st.session_state.models_trained = True
                st.sidebar.success("Models trained and saved successfully!")
                st.rerun()
            except Exception as e:
                st.sidebar.error(f" Error training models: {e}")
    else:
        st.sidebar.error("Model trainer not initialized")

# Check if models exist
models_exist = os.path.exists('models/scope1_models.pkl')
if models_exist:
    st.sidebar.success(" Trained models found")
else:
    st.sidebar.warning(" No trained models found. Please train models first.")

st.sidebar.markdown("---")
page = st.sidebar.selectbox(
    "Select Page",
    ["Reporting Dashboard", "Prediction & Analysis", "DiCE Counterfactual"]
)

# Main content based on selected page
if page == "Reporting Dashboard":
    st.title("ESG Emission Reporting Dashboard")
    st.markdown("Historical emission data and trends analysis")
    st.markdown("---")

    if historical_manager is None:
        st.error("Historical data manager not initialized")
        st.stop()

    # Time period selection for historical data
    col1, col2 = st.columns(2)

    with col1:
        hist_start_date = st.date_input(
            "Start Date",
            datetime(2023, 1, 1),
            key="hist_start"
        )

    with col2:
        hist_end_date = st.date_input(
            "End Date",
            datetime(2024, 12, 31),
            key="hist_end"
        )

    try:
        # Get historical data
        historical_data = historical_manager.get_historical_data(hist_start_date, hist_end_date)

        if not historical_data.empty:
            # Company-wide totals
            st.header("Company-Wide Historical Emissions")

            # Calculate totals
            total_scope1 = historical_data['scope1_emissions'].sum()
            total_scope2 = historical_data['scope2_emissions'].sum()
            total_scope3 = historical_data['scope3_emissions'].sum()
            total_emissions = historical_data['total_emissions'].sum()

            # Display metrics
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric(
                    "Scope 1 Emissions",
                    f"{total_scope1:,.0f} kg CO₂e"
                )

            with col2:
                st.metric(
                    "Scope 2 Emissions",
                    f"{total_scope2:,.0f} kg CO₂e"
                )

            with col3:
                st.metric(
                    "Scope 3 Emissions",
                    f"{total_scope3:,.0f} kg CO₂e"
                )

            with col4:
                st.metric(
                    "Total Emissions",
                    f"{total_emissions:,.0f} kg CO₂e"
                )

            # Historical trends
            st.header("Historical Emission Trends")

            # Daily aggregation
            daily_trends = historical_data.groupby('date').agg({
                'scope1_emissions': 'sum',
                'scope2_emissions': 'sum',
                'scope3_emissions': 'sum',
                'total_emissions': 'sum'
            }).reset_index()

            # Create line chart
            fig = go.Figure()

            fig.add_trace(go.Scatter(
                x=daily_trends['date'],
                y=daily_trends['scope1_emissions'],
                mode='lines',
                name='Scope 1',
                line=dict(color='#FF6B6B', width=2)
            ))

            fig.add_trace(go.Scatter(
                x=daily_trends['date'],
                y=daily_trends['scope2_emissions'],
                mode='lines',
                name='Scope 2',
                line=dict(color='#4ECDC4', width=2)
            ))

            fig.add_trace(go.Scatter(
                x=daily_trends['date'],
                y=daily_trends['scope3_emissions'],
                mode='lines',
                name='Scope 3',
                line=dict(color='#45B7D1', width=2)
            ))

            fig.update_layout(
                title="Historical Daily Emissions by Scope",
                xaxis_title="Date",
                yaxis_title="Emissions (kg CO₂e)",
                hovermode='x unified',
                height=500,
                showlegend=True
            )

            st.plotly_chart(fig, use_container_width=True)

            # Datacenter breakdown
            st.header("Datacenter-wise Historical Breakdown")

            # Aggregate by datacenter
            dc_summary = historical_data.groupby(['datacenter_id', 'location_country']).agg({
                'scope1_emissions': 'sum',
                'scope2_emissions': 'sum',
                'scope3_emissions': 'sum',
                'total_emissions': 'sum'
            }).reset_index()

            # Format numbers
            for col in ['scope1_emissions', 'scope2_emissions', 'scope3_emissions', 'total_emissions']:
                dc_summary[col] = dc_summary[col].round(0).astype(int)

            st.dataframe(
                dc_summary,
                column_config={
                    "datacenter_id": "Datacenter ID",
                    "location_country": "Country",
                    "scope1_emissions": st.column_config.NumberColumn(
                        "Scope 1 (kg CO₂e)",
                        format="%d"
                    ),
                    "scope2_emissions": st.column_config.NumberColumn(
                        "Scope 2 (kg CO₂e)",
                        format="%d"
                    ),
                    "scope3_emissions": st.column_config.NumberColumn(
                        "Scope 3 (kg CO₂e)",
                        format="%d"
                    ),
                    "total_emissions": st.column_config.NumberColumn(
                        "Total (kg CO₂e)",
                        format="%d"
                    )
                },
                use_container_width=True
            )

            # Scope distribution pie chart
            st.header("Historical Emission Distribution by Scope")

            scope_totals = {
                'Scope 1': total_scope1,
                'Scope 2': total_scope2,
                'Scope 3': total_scope3
            }

            fig_pie = px.pie(
                values=list(scope_totals.values()),
                names=list(scope_totals.keys()),
                title="Total Historical Emissions Distribution",
                color_discrete_map={
                    'Scope 1': '#FF6B6B',
                    'Scope 2': '#4ECDC4',
                    'Scope 3': '#45B7D1'
                }
            )

            st.plotly_chart(fig_pie, use_container_width=True)

        else:
            st.warning("No historical data found for the selected date range.")

    except Exception as e:
        st.error(f"Error loading historical data: {str(e)}")
elif page == "Prediction & Analysis":
    st.title(" ESG Emission Prediction & Analysis")
    st.markdown("Upload data, generate predictions, and perform scenario analysis with SHAP interpretability")
    st.markdown("---")

    # Check if models are available
    if not models_exist:
        st.error(" No trained models found. Please train models first using the sidebar.")
        st.stop()

    # File upload section
    st.header(" Data Upload")

    col1, col2 = st.columns([2, 1])

    with col1:
        uploaded_file = st.file_uploader(
            "Upload CSV file for predictions",
            type=['csv'],
            help="Upload a CSV file with datacenter attributes for emission predictions"
        )

    with col2:
        # Download sample template
        if st.button(" Download Sample Template"):
            try:
                # Use the combined test features as template
                sample_df = pd.read_csv('data/combined_test_features.csv').head(5)
                csv = sample_df.to_csv(index=False)
                st.download_button(
                    label="Download CSV Template",
                    data=csv,
                    file_name="esg_prediction_template.csv",
                    mime="text/csv"
                )
            except Exception as e:
                st.error(f"Error loading template: {e}")

    # Process uploaded file
    if uploaded_file is not None:
        try:
            uploaded_df = pd.read_csv(uploaded_file)
            st.session_state.uploaded_data = uploaded_df
            st.success(f" File uploaded successfully! {len(uploaded_df)} rows loaded.")

            # Show data preview
            with st.expander(" Data Preview"):
                st.dataframe(uploaded_df.head(), use_container_width=True)

                # Show data info
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Rows", len(uploaded_df))
                with col2:
                    st.metric("Columns", len(uploaded_df.columns))
                with col3:
                    st.metric("Datacenters", uploaded_df['datacenter_id'].nunique() if 'datacenter_id' in uploaded_df.columns else 0)

        except Exception as e:
            st.error(f" Error loading file: {str(e)}")

    # Prediction section
    if st.session_state.uploaded_data is not None:
        st.header("Generate Predictions")

        # Time period selection
        col1, col2, col3 = st.columns(3)

        with col1:
            prediction_period = st.selectbox(
                "Prediction Period",
                ["Next Month", "Next Quarter", "Next Year", "Custom Range"]
            )

        with col2:
            if prediction_period == "Custom Range":
                start_date = st.date_input("Start Date", datetime.now())
            else:
                start_date = datetime.now()

        with col3:
            if prediction_period == "Custom Range":
                end_date = st.date_input("End Date", datetime.now() + timedelta(days=30))
            else:
                if prediction_period == "Next Month":
                    end_date = start_date + timedelta(days=30)
                elif prediction_period == "Next Quarter":
                    end_date = start_date + timedelta(days=90)
                else:  # Next Year
                    end_date = start_date + timedelta(days=365)

        if st.button(" Generate Predictions", type="primary"):
            if calculator is None:
                st.error(" Calculator not initialized")
            else:
                with st.spinner(" Generating predictions..."):
                    try:
                        # First predict drivers, then calculate emissions
                        predictions_df = calculator.generate_time_series_predictions(
                            st.session_state.uploaded_data, start_date, end_date
                        )
                        st.session_state.predictions = predictions_df
                        st.success(" Predictions generated successfully!")
                    except Exception as e:
                        st.error(f" Error generating predictions: {str(e)}")
                        st.exception(e)

        # Display predictions if available
        if st.session_state.predictions is not None:
            predictions_df = st.session_state.predictions

            # Prediction results
            st.header("Prediction Results")

            # Calculate totals
            total_scope1 = predictions_df['scope1_total'].sum()
            total_scope2 = predictions_df['scope2_total'].sum()
            total_scope3 = predictions_df['scope3_total'].sum()
            total_emissions = predictions_df['total_emissions'].sum()

            # Display metrics
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric(
                    "Scope 1 Emissions",
                    f"{total_scope1:,.0f} kg CO₂e"
                )

            with col2:
                st.metric(
                    "Scope 2 Emissions",
                    f"{total_scope2:,.0f} kg CO₂e"
                )

            with col3:
                st.metric(
                    "Scope 3 Emissions",
                    f"{total_scope3:,.0f} kg CO₂e"
                )

            with col4:
                st.metric(
                    "Total Emissions",
                    f"{total_emissions:,.0f} kg CO₂e"
                )

            # Prediction visualization
            st.header("Prediction Trends")

            # Daily aggregation
            daily_trends = predictions_df.groupby('date').agg({
                'scope1_total': 'sum',
                'scope2_total': 'sum',
                'scope3_total': 'sum',
                'total_emissions': 'sum'
            }).reset_index()

            # Create line chart
            fig = go.Figure()

            fig.add_trace(go.Scatter(
                x=daily_trends['date'],
                y=daily_trends['scope1_total'],
                mode='lines+markers',
                name='Scope 1',
                line=dict(color='#FF6B6B', width=2)
            ))

            fig.add_trace(go.Scatter(
                x=daily_trends['date'],
                y=daily_trends['scope2_total'],
                mode='lines+markers',
                name='Scope 2',
                line=dict(color='#4ECDC4', width=2)
            ))

            fig.add_trace(go.Scatter(
                x=daily_trends['date'],
                y=daily_trends['scope3_total'],
                mode='lines+markers',
                name='Scope 3',
                line=dict(color='#45B7D1', width=2)
            ))

            fig.update_layout(
                title="Predicted Daily Emissions by Scope",
                xaxis_title="Date",
                yaxis_title="Emissions (kg CO₂e)",
                hovermode='x unified',
                height=500
            )

            st.plotly_chart(fig, use_container_width=True)

            # SHAP Analysis Section
            st.header(" SHAP Model Interpretability Analysis")
            st.markdown("Understanding which features drive the predictions for each emission scope")

            # SHAP analysis for each scope
            if model_trainer is not None and st.session_state.uploaded_data is not None:
                try:
                    # Save uploaded data temporarily for preprocessing
                    temp_file = 'sample_test_features.csv'
                    st.session_state.uploaded_data.to_csv(temp_file, index=False)

                    # Process data for SHAP analysis
                    processed_data = preprocessor.preprocess_test_data(temp_file)

                    # Clean up temp file
                    if os.path.exists(temp_file):
                        os.remove(temp_file)

                    # Select scope for SHAP analysis
                    scope_choice = st.selectbox(
                        "Select Scope for SHAP Analysis",
                        ["Scope 1", "Scope 2", "Scope 3"]
                    )

                    scope_map = {"Scope 1": "scope1", "Scope 2": "scope2", "Scope 3": "scope3"}
                    selected_scope = scope_map[scope_choice]

                    if selected_scope in model_trainer.models and model_trainer.models[selected_scope]:
                        # Select target for SHAP analysis
                        available_targets = list(model_trainer.models[selected_scope].keys())
                        selected_target = st.selectbox(
                            f"Select {scope_choice} Driver for SHAP Analysis",
                            available_targets
                        )

                        if st.button(f" Generate SHAP Analysis for {selected_target}"):
                            with st.spinner("Generating SHAP explanations..."):
                                try:
                                    model = model_trainer.models[selected_scope][selected_target]

                                    # Get feature names from target_features_map
                                    if selected_scope == 'scope1':
                                        feature_names = model_trainer.target_features_map_scope1[selected_target]
                                    elif selected_scope == 'scope2':
                                        feature_names = model_trainer.target_features_map_scope2[selected_target]
                                    else:  # scope3
                                        feature_names = model_trainer.target_features_map_scope3[selected_target]

                                    # Get available features
                                    available_features = [f for f in feature_names if f in processed_data.columns]

                                    if len(available_features) > 0:
                                        X_sample = processed_data[available_features].head(100)  # Use first 100 rows for speed

                                        # Create SHAP explainer
                                        explainer = shap.TreeExplainer(model)
                                        shap_values = explainer.shap_values(X_sample)

                                        # SHAP Summary Plot
                                        st.subheader(f"SHAP Summary Plot - {selected_target}")
                                        fig_shap, ax = plt.subplots(figsize=(10, 6))
                                        shap.summary_plot(shap_values, X_sample, feature_names=available_features, show=False)
                                        st.pyplot(fig_shap)

                                        # SHAP Feature Importance
                                        st.subheader("Feature Importance")
                                        feature_importance = np.abs(shap_values).mean(0)
                                        importance_df = pd.DataFrame({
                                            'Feature': available_features,
                                            'Importance': feature_importance
                                        }).sort_values('Importance', ascending=False)

                                        fig_importance = px.bar(
                                            importance_df.head(10),
                                            x='Importance',
                                            y='Feature',
                                            orientation='h',
                                            title=f"Top 10 Most Important Features for {selected_target}"
                                        )
                                        st.plotly_chart(fig_importance, use_container_width=True)

                                        # SHAP Waterfall Plot for first prediction
                                        #st.subheader("SHAP Waterfall Plot (First Prediction)")
                                        #fig_waterfall, ax = plt.subplots(figsize=(10, 6))
                                        #shap.waterfall_plot(explainer.expected_value, shap_values[0], X_sample.iloc[0], feature_names=available_features, show=False)
                                        #st.pyplot(fig_waterfall)

                                    else:
                                        st.error("No matching features found for SHAP analysis")

                                except Exception as e:
                                    st.error(f"Error generating SHAP analysis: {e}")
                                    st.exception(e)
                    else:
                        st.warning(f"No trained models found for {scope_choice}")

                except Exception as e:
                    st.error(f"Error in SHAP analysis setup: {e}")
            else:
                st.info("Upload data and generate predictions first to see SHAP analysis")

        # Point Prediction Section
        st.header("Point Prediction Analysis")
        st.markdown("Modify driver parameters to see immediate emission impact")

        if st.session_state.uploaded_data is not None:
            # Datacenter selection
            datacenters = st.session_state.uploaded_data['datacenter_id'].unique()
            selected_dc = st.selectbox("Select Datacenter", datacenters)

            # Get baseline data for selected datacenter
            baseline_data = st.session_state.uploaded_data[
                st.session_state.uploaded_data['datacenter_id'] == selected_dc
            ].copy()

            if not baseline_data.empty:
                st.subheader("Modify Driver Parameters")

                # Create columns for parameter modification
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.markdown("**Scope 1 Driver Parameters**")
                    st.markdown("*Adjust input parameters that drive Scope 1 emissions*")

                    # Generator hours (driver parameter)
                    baseline_gen_hours = float(baseline_data.get('diesel_generator_hours_per_day', [8]).iloc[0] if 'diesel_generator_hours_per_day' in baseline_data.columns else 8)
                    new_gen_hours = st.slider(
                        "Generator Hours/day",
                        min_value=0.0,
                        max_value=24.0,
                        value=baseline_gen_hours,
                        step=0.5,
                        key="gen_hours_slider"
                    )

                    # Generator capacity (driver parameter)
                    baseline_gen_capacity = float(baseline_data.get('generator_capacity_kW', [400]).iloc[0] if 'generator_capacity_kW' in baseline_data.columns else 400)
                    new_gen_capacity = st.slider(
                        "Generator Capacity (kW)",
                        min_value=100.0,
                        max_value=1000.0,
                        value=baseline_gen_capacity,
                        step=10.0,
                        key="gen_capacity_slider"
                    )

                    # Generator efficiency (driver parameter)
                    baseline_gen_efficiency = float(baseline_data.get('generator_efficiency_pct', [85]).iloc[0] if 'generator_efficiency_pct' in baseline_data.columns else 85)
                    new_gen_efficiency = st.slider(
                        "Generator Efficiency (%)",
                        min_value=70.0,
                        max_value=95.0,
                        value=baseline_gen_efficiency,
                        step=1.0,
                        key="gen_efficiency_slider"
                    )

                    # Boiler efficiency (driver parameter)
                    baseline_boiler_efficiency = float(baseline_data.get('boiler_efficiency_pct', [75]).iloc[0] if 'boiler_efficiency_pct' in baseline_data.columns else 75)
                    new_boiler_efficiency = st.slider(
                        "Boiler Efficiency (%)",
                        min_value=60.0,
                        max_value=90.0,
                        value=baseline_boiler_efficiency,
                        step=1.0,
                        key="boiler_efficiency_slider"
                    )

                with col2:
                    st.markdown("**Scope 2 Driver Parameters**")
                    st.markdown("*Adjust input parameters that drive Scope 2 emissions*")

                    # IT Load (driver parameter)
                    baseline_it_load = float(baseline_data.get('IT_load_kW', [300]).iloc[0] if 'IT_load_kW' in baseline_data.columns else 300)
                    new_it_load = st.slider(
                        "IT Load (kW)",
                        min_value=100.0,
                        max_value=1000.0,
                        value=baseline_it_load,
                        step=10.0,
                        key="it_load_slider"
                    )

                    # Power Usage Effectiveness (driver parameter)
                    baseline_pue = float(baseline_data.get('power_usage_effectiveness', [1.5]).iloc[0] if 'power_usage_effectiveness' in baseline_data.columns else 1.5)
                    new_pue = st.slider(
                        "Power Usage Effectiveness (PUE)",
                        min_value=1.1,
                        max_value=2.0,
                        value=baseline_pue,
                        step=0.05,
                        key="pue_slider"
                    )

                    # Solar capacity (driver parameter)
                    baseline_solar_capacity = float(baseline_data.get('solar_capacity_kW', [100]).iloc[0] if 'solar_capacity_kW' in baseline_data.columns else 100)
                    new_solar_capacity = st.slider(
                        "Solar Capacity (kW)",
                        min_value=0.0,
                        max_value=500.0,
                        value=baseline_solar_capacity,
                        step=10.0,
                        key="solar_capacity_slider"
                    )

                    # Sunlight hours (driver parameter)
                    baseline_sunlight_hours = float(baseline_data.get('sunlight_hours_per_day', [6]).iloc[0] if 'sunlight_hours_per_day' in baseline_data.columns else 6)
                    new_sunlight_hours = st.slider(
                        "Sunlight Hours/day",
                        min_value=2.0,
                        max_value=12.0,
                        value=baseline_sunlight_hours,
                        step=0.5,
                        key="sunlight_hours_slider"
                    )

                with col3:
                    st.markdown("**Scope 3 Driver Parameters**")
                    st.markdown("*Adjust input parameters that drive Scope 3 emissions*")

                    # Employee count (driver parameter)
                    baseline_employees = float(baseline_data.get('employee_count', [100]).iloc[0] if 'employee_count' in baseline_data.columns else 100)
                    new_employees = st.slider(
                        "Employee Count",
                        min_value=10.0,
                        max_value=1000.0,
                        value=baseline_employees,
                        step=5.0,
                        key="employees_slider"
                    )

                    # Travel km per trip (driver parameter)
                    baseline_travel_km = float(baseline_data.get('travel_km_per_trip', [500]).iloc[0] if 'travel_km_per_trip' in baseline_data.columns else 500)
                    new_travel_km = st.slider(
                        "Travel km/trip",
                        min_value=50.0,
                        max_value=2000.0,
                        value=baseline_travel_km,
                        step=25.0,
                        key="travel_km_slider"
                    )

                    # Waste volume (driver parameter)
                    baseline_waste = float(baseline_data.get('waste_volume_kg', [200]).iloc[0] if 'waste_volume_kg' in baseline_data.columns else 200)
                    new_waste = st.slider(
                        "Waste Volume (kg)",
                        min_value=50.0,
                        max_value=1000.0,
                        value=baseline_waste,
                        step=10.0,
                        key="waste_slider"
                    )

                    # Purchase volume (driver parameter)
                    baseline_purchase = float(baseline_data.get('purchase_volume_kg', [1500]).iloc[0] if 'purchase_volume_kg' in baseline_data.columns else 1500)
                    new_purchase = st.slider(
                        "Purchase Volume (kg)",
                        min_value=500.0,
                        max_value=5000.0,
                        value=baseline_purchase,
                        step=50.0,
                        key="purchase_slider"
                    )

                # Real-time calculation
                if st.button("Calculate Point Prediction", type="primary"):
                    if calculator is None:
                        st.error("Calculator not initialized")
                    else:
                        with st.spinner("Calculating emissions..."):
                            try:
                                # Create modified data with DRIVER PARAMETERS (not target variables)
                                scenario_data = baseline_data.copy()

                                # Set Scope 1 driver parameters
                                scenario_data['diesel_generator_hours_per_day'] = new_gen_hours
                                scenario_data['generator_capacity_kW'] = new_gen_capacity
                                scenario_data['generator_efficiency_pct'] = new_gen_efficiency
                                scenario_data['boiler_efficiency_pct'] = new_boiler_efficiency

                                # Set Scope 2 driver parameters
                                scenario_data['IT_load_kW'] = new_it_load
                                scenario_data['power_usage_effectiveness'] = new_pue
                                scenario_data['solar_capacity_kW'] = new_solar_capacity
                                scenario_data['sunlight_hours_per_day'] = new_sunlight_hours

                                # Set Scope 3 driver parameters
                                scenario_data['employee_count'] = new_employees
                                scenario_data['travel_km_per_trip'] = new_travel_km
                                scenario_data['waste_volume_kg'] = new_waste
                                scenario_data['purchase_volume_kg'] = new_purchase

                                # Calculate emissions using driver-based approach
                                baseline_emissions = calculator.predict_and_calculate_emissions(baseline_data)
                                scenario_emissions = calculator.predict_and_calculate_emissions(scenario_data)

                                # Display results
                                st.header("Point Prediction Results")

                                col1, col2, col3 = st.columns(3)

                                with col1:
                                    st.metric(
                                        "Scope 1 Change",
                                        f"{scenario_emissions['scope1_total'].iloc[0]:,.0f} kg CO₂e",
                                        delta=f"{scenario_emissions['scope1_total'].iloc[0] - baseline_emissions['scope1_total'].iloc[0]:,.0f}"
                                    )

                                with col2:
                                    st.metric(
                                        "Scope 2 Change",
                                        f"{scenario_emissions['scope2_total'].iloc[0]:,.0f} kg CO₂e",
                                        delta=f"{scenario_emissions['scope2_total'].iloc[0] - baseline_emissions['scope2_total'].iloc[0]:,.0f}"
                                    )

                                with col3:
                                    st.metric(
                                        "Scope 3 Change",
                                        f"{scenario_emissions['scope3_total'].iloc[0]:,.0f} kg CO₂e",
                                        delta=f"{scenario_emissions['scope3_total'].iloc[0] - baseline_emissions['scope3_total'].iloc[0]:,.0f}"
                                    )

                                # Total comparison
                                baseline_total = (baseline_emissions['scope1_total'].iloc[0] +
                                                baseline_emissions['scope2_total'].iloc[0] +
                                                baseline_emissions['scope3_total'].iloc[0])
                                scenario_total = (scenario_emissions['scope1_total'].iloc[0] +
                                                scenario_emissions['scope2_total'].iloc[0] +
                                                scenario_emissions['scope3_total'].iloc[0])

                                st.metric(
                                    "Total Daily Emissions",
                                    f"{scenario_total:,.0f} kg CO₂e",
                                    delta=f"{scenario_total - baseline_total:,.0f} kg CO₂e"
                                )

                                # Comparison chart
                                comparison_df = pd.DataFrame({
                                    'Scope': ['Scope 1', 'Scope 2', 'Scope 3'],
                                    'Baseline': [
                                        baseline_emissions['scope1_total'].iloc[0],
                                        baseline_emissions['scope2_total'].iloc[0],
                                        baseline_emissions['scope3_total'].iloc[0]
                                    ],
                                    'Scenario': [
                                        scenario_emissions['scope1_total'].iloc[0],
                                        scenario_emissions['scope2_total'].iloc[0],
                                        scenario_emissions['scope3_total'].iloc[0]
                                    ]
                                })

                                fig = go.Figure(data=[
                                    go.Bar(name='Baseline', x=comparison_df['Scope'], y=comparison_df['Baseline'], marker_color='#FF6B6B'),
                                    go.Bar(name='Scenario', x=comparison_df['Scope'], y=comparison_df['Scenario'], marker_color='#45B7D1')
                                ])

                                fig.update_layout(
                                    title="Baseline vs Scenario Emissions by Scope",
                                    xaxis_title="Emission Scope",
                                    yaxis_title="Daily Emissions (kg CO₂e)",
                                    barmode='group',
                                    height=400
                                )

                                st.plotly_chart(fig, use_container_width=True)

                            except Exception as e:
                                st.error(f"Error calculating emissions: {str(e)}")
                                st.exception(e)
            else:
                st.warning("No data found for selected datacenter")
        else:
            st.info("Please upload data first to perform point predictions.")

    else:
        st.info("Please upload a CSV file to start predictions and analysis.")


elif page == "DiCE Counterfactual":
    st.title("DiCE Counterfactual Analysis")
    st.markdown("Set emission reduction targets and discover optimal driver parameter changes for each scope")
    st.markdown("---")

    # Check if models are available
    if not models_exist:
        st.error(" No trained models found. Please train models first using the sidebar.")
        st.stop()

    if st.session_state.uploaded_data is not None:
        # Datacenter selection
        datacenters = st.session_state.uploaded_data['datacenter_id'].unique()
        selected_dc = st.selectbox("Select Datacenter for Analysis", datacenters, key="dice_dc")

        # Get baseline data for selected datacenter
        baseline_data = st.session_state.uploaded_data[
            st.session_state.uploaded_data['datacenter_id'] == selected_dc
        ].copy()

        if not baseline_data.empty:
            # Target setting
            st.header("Set Reduction Targets")
            st.markdown("Define your emission reduction goals for each scope")

            col1, col2, col3 = st.columns(3)

            with col1:
                st.markdown("**Scope 1 (Direct Emissions)**")
                scope1_reduction = st.slider(
                    "Scope 1 Reduction Target (%)",
                    min_value=0,
                    max_value=50,
                    value=10,
                    step=5,
                    key="scope1_target",
                    help="Direct emissions from owned/controlled sources"
                )

            with col2:
                st.markdown("**Scope 2 (Electricity)**")
                scope2_reduction = st.slider(
                    "Scope 2 Reduction Target (%)",
                    min_value=0,
                    max_value=50,
                    value=15,
                    step=5,
                    key="scope2_target",
                    help="Indirect emissions from purchased electricity"
                )

            with col3:
                st.markdown("**Scope 3 (Value Chain)**")
                scope3_reduction = st.slider(
                    "Scope 3 Reduction Target (%)",
                    min_value=0,
                    max_value=50,
                    value=20,
                    step=5,
                    key="scope3_target",
                    help="Indirect emissions from value chain activities"
                )

            if st.button("Generate DiCE Recommendations", type="primary"):
                if calculator is None:
                    st.error("Calculator not initialized")
                else:
                    with st.spinner("Generating counterfactual recommendations..."):
                        try:
                            # Import DiCE counterfactual utility
                            from utils.dice_counterfactual import ESGDiceCounterfactual

                            # Initialize DiCE analyzer
                            dice_analyzer = ESGDiceCounterfactual()

                            # Get actual parameter recommendations using DiCE
                            recommendations, baseline_emissions, baseline_total, target_total = dice_analyzer.get_parameter_recommendations(
                                baseline_data, scope1_reduction, scope2_reduction, scope3_reduction
                            )

                            if baseline_emissions is not None:
                                baseline_scope1 = baseline_emissions['scope1_total'].iloc[0]
                                baseline_scope2 = baseline_emissions['scope2_total'].iloc[0]
                                baseline_scope3 = baseline_emissions['scope3_total'].iloc[0]

                                # Calculate target emissions
                                target_scope1 = baseline_scope1 * (1 - scope1_reduction/100)
                                target_scope2 = baseline_scope2 * (1 - scope2_reduction/100)
                                target_scope3 = baseline_scope3 * (1 - scope3_reduction/100)
                            else:
                                st.error("Could not calculate baseline emissions")
                                st.stop()

                            st.header("🎯 DiCE Counterfactual Recommendations")
                            st.markdown("AI-generated parameter changes to achieve your reduction targets")

                            if recommendations:
                                # Display top recommendations
                                st.subheader("📊 Key Parameter Changes")

                                # Sort recommendations by absolute change percentage
                                sorted_recs = sorted(recommendations.items(),
                                                   key=lambda x: abs(x[1]['change_pct']),
                                                   reverse=True)[:6]

                                col1, col2 = st.columns(2)

                                for i, (param, rec) in enumerate(sorted_recs):
                                    with col1 if i % 2 == 0 else col2:
                                        # Clean parameter name for display
                                        display_name = param.replace('_', ' ').title()

                                        st.metric(
                                            display_name,
                                            f"{rec['recommended']:.2f}",
                                            f"{rec['change']:+.2f} ({rec['change_pct']:+.1f}%)"
                                        )

                                # Detailed recommendations by scope
                                st.subheader("🔍 Detailed Scope-wise Recommendations")

                                scope1_params = ['diesel_generator_hours_per_day', 'generator_capacity_kW',
                                               'generator_efficiency_pct', 'boiler_efficiency_pct']
                                scope2_params = ['power_usage_effectiveness', 'solar_capacity_kW',
                                               'sunlight_hours_per_day', 'cooling_system_power_draw_kW']
                                scope3_params = ['employee_count', 'travel_km_per_trip',
                                               'waste_volume_kg', 'purchase_volume_kg']

                                col1, col2, col3 = st.columns(3)

                                with col1:
                                    st.markdown("**🏭 Scope 1 Parameters**")
                                    for param in scope1_params:
                                        if param in recommendations:
                                            rec = recommendations[param]
                                            st.write(f"**{param.replace('_', ' ').title()}**")
                                            st.write(f"Current: {rec['baseline']:.2f}")
                                            st.write(f"Recommended: {rec['recommended']:.2f}")
                                            st.write(f"Change: {rec['change_pct']:+.1f}%")
                                            st.write("---")

                                with col2:
                                    st.markdown("**⚡ Scope 2 Parameters**")
                                    for param in scope2_params:
                                        if param in recommendations:
                                            rec = recommendations[param]
                                            st.write(f"**{param.replace('_', ' ').title()}**")
                                            st.write(f"Current: {rec['baseline']:.2f}")
                                            st.write(f"Recommended: {rec['recommended']:.2f}")
                                            st.write(f"Change: {rec['change_pct']:+.1f}%")
                                            st.write("---")

                                with col3:
                                    st.markdown("**🌍 Scope 3 Parameters**")
                                    for param in scope3_params:
                                        if param in recommendations:
                                            rec = recommendations[param]
                                            st.write(f"**{param.replace('_', ' ').title()}**")
                                            st.write(f"Current: {rec['baseline']:.2f}")
                                            st.write(f"Recommended: {rec['recommended']:.2f}")
                                            st.write(f"Change: {rec['change_pct']:+.1f}%")
                                            st.write("---")
                            else:
                                st.warning("Could not generate DiCE recommendations. This may be due to insufficient training data or model limitations.")

                            with col2:
                                st.subheader("⚡ Scope 2 Driver Changes")

                                if scope2_reduction > 0:
                                    st.markdown("**Recommended changes to reduce Scope 2 emissions:**")

                                    # Show changeable driver parameters (inputs), not targets
                                    current_pue = baseline_data.get('power_usage_effectiveness', [1.5]).iloc[0] if 'power_usage_effectiveness' in baseline_data.columns else 1.5
                                    current_solar_capacity = baseline_data.get('solar_capacity_kW', [100]).iloc[0] if 'solar_capacity_kW' in baseline_data.columns else 100
                                    current_cooling_power = baseline_data.get('cooling_system_power_draw_kW', [150]).iloc[0] if 'cooling_system_power_draw_kW' in baseline_data.columns else 150

                                    # Calculate recommended changes
                                    new_pue = max(1.1, current_pue * (1 - scope2_reduction * 0.4 / 100))  # 40% improvement in PUE
                                    new_solar_capacity = current_solar_capacity * (1 + scope2_reduction * 0.5 / 100)  # 50% increase in solar
                                    new_cooling_power = current_cooling_power * (1 - scope2_reduction * 0.3 / 100)  # 30% reduction in cooling

                                    st.metric(
                                        "Power Usage Effectiveness (PUE)",
                                        f"{new_pue:.2f}",
                                        f"{new_pue - current_pue:.2f}"
                                    )

                                    st.metric(
                                        "Solar Capacity",
                                        f"{new_solar_capacity:.0f} kW",
                                        f"+{new_solar_capacity - current_solar_capacity:.0f} kW"
                                    )

                                    st.metric(
                                        "Cooling System Power",
                                        f"{new_cooling_power:.0f} kW",
                                        f"{new_cooling_power - current_cooling_power:.0f} kW"
                                    )

                                else:
                                    st.info("No Scope 2 reduction target set")

                            with col3:
                                st.subheader(" Scope 3 Driver Changes")

                                if scope3_reduction > 0:
                                    st.markdown("**Recommended changes to reduce Scope 3 emissions:**")

                                    # Show changeable driver parameters (inputs), not targets
                                    current_travel_km = baseline_data.get('travel_km_per_trip', [500]).iloc[0] if 'travel_km_per_trip' in baseline_data.columns else 500
                                    current_waste_volume = baseline_data.get('waste_volume_kg', [200]).iloc[0] if 'waste_volume_kg' in baseline_data.columns else 200
                                    current_purchase_volume = baseline_data.get('purchase_volume_kg', [1000]).iloc[0] if 'purchase_volume_kg' in baseline_data.columns else 1000
                                    current_employee_count = baseline_data.get('employee_count', [100]).iloc[0] if 'employee_count' in baseline_data.columns else 100

                                    # Calculate recommended changes
                                    new_travel_km = current_travel_km * (1 - scope3_reduction * 0.4 / 100)  # 40% reduction in travel distance
                                    new_waste_volume = current_waste_volume * (1 - scope3_reduction * 0.3 / 100)  # 30% reduction in waste
                                    new_purchase_volume = current_purchase_volume * (1 - scope3_reduction * 0.3 / 100)  # 30% reduction in purchases

                                    st.metric(
                                        "Travel Distance per Trip",
                                        f"{new_travel_km:.0f} km",
                                        f"{new_travel_km - current_travel_km:.0f} km"
                                    )

                                    st.metric(
                                        "Waste Volume",
                                        f"{new_waste_volume:.0f} kg",
                                        f"{new_waste_volume - current_waste_volume:.0f} kg"
                                    )

                                    st.metric(
                                        "Purchase Volume",
                                        f"{new_purchase_volume:.0f} kg",
                                        f"{new_purchase_volume - current_purchase_volume:.0f} kg"
                                    )
                                else:
                                    st.info("No Scope 3 reduction target set")

                            # Show potential impact
                            st.header("Projected Impact")

                            col1, col2, col3 = st.columns(3)

                            with col1:
                                st.metric(
                                    "Current Total Emissions",
                                    f"{baseline_scope1 + baseline_scope2 + baseline_scope3:,.0f} kg CO₂e"
                                )

                            with col2:
                                st.metric(
                                    "Target Emissions",
                                    f"{target_scope1 + target_scope2 + target_scope3:,.0f} kg CO₂e"
                                )

                            with col3:
                                total_reduction = (baseline_scope1 + baseline_scope2 + baseline_scope3) - (target_scope1 + target_scope2 + target_scope3)
                                st.metric(
                                    "Total Reduction",
                                    f"{total_reduction:,.0f} kg CO₂e",
                                    delta=f"-{(total_reduction/(baseline_scope1 + baseline_scope2 + baseline_scope3))*100:.1f}%"
                                )

                            # Visualization
                            comparison_df = pd.DataFrame({
                                'Scope': ['Scope 1', 'Scope 2', 'Scope 3'],
                                'Current': [baseline_scope1, baseline_scope2, baseline_scope3],
                                'Target': [target_scope1, target_scope2, target_scope3]
                            })

                            fig = go.Figure(data=[
                                go.Bar(name='Current', x=comparison_df['Scope'], y=comparison_df['Current'], marker_color='#FF6B6B'),
                                go.Bar(name='Target', x=comparison_df['Scope'], y=comparison_df['Target'], marker_color='#45B7D1')
                            ])

                            fig.update_layout(
                                title="Current vs Target Emissions by Scope",
                                xaxis_title="Emission Scope",
                                yaxis_title="Daily Emissions (kg CO₂e)",
                                barmode='group',
                                height=500
                            )

                            st.plotly_chart(fig, use_container_width=True)

                    

                        except Exception as e:
                            st.error(f" Error generating recommendations: {str(e)}")
                            st.exception(e)
        else:
            st.warning(" No data found for selected datacenter")

    else:
        st.info(" Please upload data on the Prediction & Analysis page first.")


