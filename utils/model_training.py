import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.metrics import mean_squared_error, r2_score
import pickle
import os
from .data_preprocessing import ESGDataPreprocessor

class ESGModelTrainer:
    """
    Model training pipeline for ESG emission prediction
    Based on the driver-based approach from untitled49.py
    """
    
    def __init__(self):
        self.models = {}
        self.feature_maps = {}
        self.preprocessor = ESGDataPreprocessor()
        
        # Define target features (drivers) for each scope based on untitled49.py
        self.target_features_map_scope1 = {
            'diesel_fuel_consumption_l_per_day': [
                'diesel_generator_hours_per_day', 'generator_capacity_kW', 'generator_efficiency_pct',
                'ambient_temperature_C', 'IT_load_kW', 'climate_zone_Cold', 'climate_zone_Temperate', 'climate_zone_Tropical'
            ],
            'natural_gas_consumption_m3_per_day': [
                'boiler_efficiency_pct', 'ambient_temperature_C', 'IT_load_kW',
                'climate_zone_Cold', 'climate_zone_Temperate', 'climate_zone_Tropical'
            ],
            'refrigerant_leak_volume_kg_per_year': [
                'chiller_type_VRF', 'chiller_type_Chiller', 'chiller_type_Packaged', 'ambient_temperature_C',
                'facility_age_years', 'refrigerant_type_R-410A', 'refrigerant_type_HFC-134a', 'refrigerant_type_R-1234yf', 'IT_load_kW'
            ],
            'fire_suppression_discharges_per_year': [
                'fire_suppression_type_FM-200', 'fire_suppression_type_Novec', 'facility_age_years'
            ],
            'vehicle_fuel_consumption_l_per_day': [
                'fleet_vehicle_count', 'avg_km_per_vehicle_per_day', 'fuel_efficiency_kmpl',
                'vehicle_fuel_type_hybrid', 'vehicle_fuel_type_diesel', 'vehicle_fuel_type_petrol'
            ]
        }
        
        self.target_features_map_scope2 = {
            'total_energy_consumption_kwh_per_day': [
                'it_load_kw', 'power_usage_effectiveness', 'cooling_system_power_draw_kW', 'UPS_loss_factor',
                'ambient_temperature_C', 'location_country_Australia', 'location_country_India', 'location_country_UK',
                'climate_zone_Cold', 'climate_zone_Temperate', 'climate_zone_Tropical'
            ],
            'on_site_solar_generation_kWh_per_day': [
                'solar_capacity_kW', 'sunlight_hours_per_day', 'solar_panel_efficiency_pct'
            ],
            'grid_emission_factor_kgco2perkwh': [
                'location_country_Australia', 'location_country_India', 'location_country_UK',
                'climate_zone_Cold', 'climate_zone_Temperate', 'climate_zone_Tropical'
            ]
        }
        
        self.target_features_map_scope3 = {
            'business_travel_emissions_kgco2': [
                'employee_count', 'travel_km_per_trip', 'travel_mode_air', 'travel_mode_car', 'travel_mode_rail'
            ],
            'commuting_emissions_kgco2': [
                'employee_count', 'travel_km_per_trip', 'travel_mode_air', 'travel_mode_car', 'travel_mode_rail'
            ],
            'waste_treatment_emissions_kgco2': [
                'waste_volume_kg', 'waste_type_e-waste', 'waste_type_mixed', 'waste_type_organic', 'waste_type_plastic'
            ],
            'use_phase_emissions_kgco2': [
                'product_units_sold', 'product_use_duration_yrs'
            ],
            'end_of_life_emissions_kgco2': [
                'product_units_sold', 'product_use_duration_yrs'
            ],
            'purchased_goods_emissions_kgco2': [
                'purchase_volume_kg'
            ]
        }
        
        # Add temporal features to all models
        self.cyclic_date_features = [
            'month_sin', 'month_cos', 'day_sin', 'day_cos', 'dayofweek_sin', 'dayofweek_cos',
            'weekofyear', 'quarter', 'year', 'isholiday'
        ]
        
        # Add temporal features to all target feature maps
        for target in self.target_features_map_scope1:
            self.target_features_map_scope1[target].extend(self.cyclic_date_features)
        
        for target in self.target_features_map_scope2:
            self.target_features_map_scope2[target].extend(self.cyclic_date_features)
            
        for target in self.target_features_map_scope3:
            self.target_features_map_scope3[target].extend(self.cyclic_date_features)
    
    def train_scope_models(self, df, target_features_map, scope_name):
        """Train XGBoost models for a specific scope"""
        print(f"\n=== Training {scope_name} Models ===")
        
        # Time-based split
        train_df, test_df = self.preprocessor.time_split_datacenter(df)
        
        scope_models = {}
        predictions = pd.DataFrame(index=test_df.index)
        
        for target, features in target_features_map.items():
            print(f"🔧 Training model for target: {target}")
            
            # Check if all features exist in the dataframe
            available_features = [f for f in features if f in df.columns]
            missing_features = [f for f in features if f not in df.columns]
            
            if missing_features:
                print(f" Missing features for {target}: {missing_features}")
                print(f"Using available features: {len(available_features)}/{len(features)}")
            
            if len(available_features) == 0:
                print(f"No features available for {target}, skipping...")
                continue
            
            # Train-test split
            X_train = train_df[available_features]
            y_train = train_df[target]
            X_test = test_df[available_features]
            y_test = test_df[target]
            
            # XGBoost Regressor
            model = xgb.XGBRegressor(
                objective='reg:squarederror',
                n_estimators=1000,
                learning_rate=0.05,
                random_state=42,
                n_jobs=-1
            )
            
            model.fit(X_train, y_train)
            predictions[target] = model.predict(X_test)
            scope_models[target] = model
            
            # Calculate metrics
            rmse = np.sqrt(mean_squared_error(y_test, predictions[target]))
            r2 = r2_score(y_test, predictions[target])
            
            print(f" RMSE for {target}: {rmse:.4f}")
            print(f" R2 for {target}: {r2:.4f}")
        
        return scope_models, predictions
    
    def train_all_models(self, data_dir='data'):
        """Train models for all scopes"""
        print(" Starting ESG Model Training Pipeline")
        
        # Preprocess training data
        processed_dfs = self.preprocessor.preprocess_training_data(data_dir)
        df1, df2, df3 = processed_dfs
        
        # Train models for each scope
        scope1_models, _ = self.train_scope_models(df1, self.target_features_map_scope1, "Scope 1")
        scope2_models, _ = self.train_scope_models(df2, self.target_features_map_scope2, "Scope 2")
        scope3_models, _ = self.train_scope_models(df3, self.target_features_map_scope3, "Scope 3")
        
        # Store all models
        self.models = {
            'scope1': scope1_models,
            'scope2': scope2_models,
            'scope3': scope3_models
        }
        
        # Store feature maps
        self.feature_maps = {
            'scope1': self.target_features_map_scope1,
            'scope2': self.target_features_map_scope2,
            'scope3': self.target_features_map_scope3
        }
        
        print(" Model training completed!")
        return self.models
    
    def save_models(self, models_dir='models'):
        """Save trained models to pickle files"""
        os.makedirs(models_dir, exist_ok=True)
        
        # Save individual scope models
        for scope, scope_models in self.models.items():
            filepath = os.path.join(models_dir, f'{scope}_models.pkl')
            with open(filepath, 'wb') as f:
                pickle.dump(scope_models, f)
            print(f" {scope} models saved to {filepath}")
        
        # Save feature maps
        feature_maps_path = os.path.join(models_dir, 'feature_maps.pkl')
        with open(feature_maps_path, 'wb') as f:
            pickle.dump(self.feature_maps, f)
        print(f" Feature maps saved to {feature_maps_path}")
        
        # Save scalers
        self.preprocessor.save_scalers(os.path.join(models_dir, 'scalers.pkl'))
    
    def load_models(self, models_dir='models'):
        """Load trained models from pickle files"""
        # Load individual scope models
        for scope in ['scope1', 'scope2', 'scope3']:
            filepath = os.path.join(models_dir, f'{scope}_models.pkl')
            if os.path.exists(filepath):
                with open(filepath, 'rb') as f:
                    self.models[scope] = pickle.load(f)
                print(f"{scope} models loaded from {filepath}")
        
        # Load feature maps
        feature_maps_path = os.path.join(models_dir, 'feature_maps.pkl')
        if os.path.exists(feature_maps_path):
            with open(feature_maps_path, 'rb') as f:
                self.feature_maps = pickle.load(f)
            print(f"Feature maps loaded from {feature_maps_path}")
        
        # Load scalers
        scalers_path = os.path.join(models_dir, 'scalers.pkl')
        if os.path.exists(scalers_path):
            self.preprocessor.load_scalers(scalers_path)
    
    def predict_drivers(self, input_data, scope):
        """Predict driver values for a given scope"""
        if scope not in self.models or not self.models[scope]:
            raise ValueError(f"Models for {scope} not loaded")
        
        predictions = {}
        scope_models = self.models[scope]
        
        for target, model in scope_models.items():
            # Get features for this target
            if scope in self.feature_maps:
                features = self.feature_maps[scope][target]
                available_features = [f for f in features if f in input_data.columns]
                
                if len(available_features) > 0:
                    X = input_data[available_features]
                    predictions[target] = model.predict(X)
                else:
                    print(f"  No features available for predicting {target}")
        
        return predictions
