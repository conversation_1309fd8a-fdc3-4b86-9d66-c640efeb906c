import pandas as pd
import numpy as np
import dice_ml
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
import pickle
import os
from .model_training import ESGModelTrainer
from .data_preprocessing import ESGDataPreprocessor
from .emission_calculator import EmissionCalculator

class ESGDiceCounterfactual:
    """
    DiCE counterfactual analysis for ESG emission reduction
    """
    
    def __init__(self, models_dir='models', data_dir='data'):
        self.models_dir = models_dir
        self.data_dir = data_dir
        self.model_trainer = ESGModelTrainer()
        self.preprocessor = ESGDataPreprocessor()
        self.calculator = EmissionCalculator(data_dir, models_dir)
        
        # Load trained models
        try:
            self.model_trainer.load_models(models_dir)
            print("✅ Models loaded for DiCE analysis")
        except Exception as e:
            print(f"⚠️ Error loading models: {e}")
    
    def prepare_dice_data(self, input_data):
        """Prepare data for DiCE analysis"""
        # Save temporarily for preprocessing with full path
        temp_file = os.path.join(self.data_dir, 'temp_dice_data.csv')
        input_data.to_csv(temp_file, index=False)

        # Preprocess data
        processed_data = self.preprocessor.preprocess_test_data('temp_dice_data.csv', self.data_dir)

        # Clean up
        if os.path.exists(temp_file):
            os.remove(temp_file)

        return processed_data
    
    def create_emission_model(self, training_data):
        """Create a simplified emission model for DiCE"""
        # Get driver features (continuous features for DiCE)
        continuous_features = [
            'diesel_generator_hours_per_day', 'generator_capacity_kW', 'generator_efficiency_pct',
            'boiler_efficiency_pct', 'ambient_temperature_C', 'IT_load_kW', 'facility_age_years',
            'power_usage_effectiveness', 'solar_capacity_kW', 'sunlight_hours_per_day',
            'solar_panel_efficiency_pct', 'cooling_system_power_draw_kW', 'UPS_loss_factor',
            'employee_count', 'travel_km_per_trip', 'waste_volume_kg', 'purchase_volume_kg'
        ]
        
        # Filter available features
        available_features = [f for f in continuous_features if f in training_data.columns]
        
        if len(available_features) == 0:
            raise ValueError("No continuous features available for DiCE analysis")
        
        # Calculate total emissions for training data
        emissions = self.calculator.predict_and_calculate_emissions(training_data)
        training_data_with_emissions = training_data.copy()
        training_data_with_emissions['total_emissions'] = emissions['total_emissions']
        
        # Create a simplified model for DiCE
        X = training_data_with_emissions[available_features]
        y = training_data_with_emissions['total_emissions']
        
        # Train a Random Forest model (DiCE works well with tree-based models)
        model = RandomForestRegressor(n_estimators=100, random_state=42)
        model.fit(X, y)
        
        return model, available_features, training_data_with_emissions
    
    def generate_counterfactuals(self, baseline_data, target_reduction_pct):
        """Generate counterfactual recommendations using DiCE"""
        try:
            # Load training data for DiCE model
            df1, df2, df3 = self.preprocessor.load_training_data()
            combined_training = pd.concat([df1.head(100), df2.head(100), df3.head(100)], ignore_index=True)
            
            # Create emission model for DiCE
            emission_model, continuous_features, training_data_with_emissions = self.create_emission_model(combined_training)
            
            # Calculate baseline emissions
            baseline_emissions = self.calculator.predict_and_calculate_emissions(baseline_data)
            baseline_total = baseline_emissions['total_emissions'].iloc[0]
            target_total = baseline_total * (1 - target_reduction_pct / 100)
            
            # Prepare DiCE data interface
            dice_data = dice_ml.Data(
                dataframe=training_data_with_emissions,
                continuous_features=continuous_features,
                outcome_name='total_emissions'
            )
            
            # Create DiCE model interface
            dice_model = dice_ml.Model(model=emission_model, backend="sklearn")
            
            # Create DiCE explainer
            dice_exp = dice_ml.Dice(dice_data, dice_model, method="random")
            
            # Prepare query instance (baseline data)
            baseline_processed = self.prepare_dice_data(baseline_data)
            query_features = [f for f in continuous_features if f in baseline_processed.columns]
            
            if len(query_features) == 0:
                raise ValueError("No matching features for DiCE analysis")
            
            query_instance = baseline_processed[query_features].head(1)
            
            # Generate counterfactuals
            counterfactuals = dice_exp.generate_counterfactuals(
                query_instance,
                total_CFs=3,
                desired_range=[target_total * 0.9, target_total * 1.1]
            )
            
            return counterfactuals, continuous_features, baseline_total, target_total
            
        except Exception as e:
            print(f"Error in DiCE analysis: {e}")
            return None, None, None, None
    
    def get_parameter_recommendations(self, baseline_data, scope1_reduction, scope2_reduction, scope3_reduction):
        """Get specific parameter recommendations for each scope"""
        recommendations = {}
        
        try:
            # Calculate baseline emissions
            baseline_emissions = self.calculator.predict_and_calculate_emissions(baseline_data)
            
            # Overall target reduction
            overall_reduction = (scope1_reduction + scope2_reduction + scope3_reduction) / 3
            
            # Generate counterfactuals
            counterfactuals, features, baseline_total, target_total = self.generate_counterfactuals(
                baseline_data, overall_reduction
            )
            
            if counterfactuals is not None:
                cf_df = counterfactuals.cf_examples_list[0].final_cfs_df
                
                # Calculate parameter changes
                baseline_processed = self.prepare_dice_data(baseline_data)
                
                for feature in features:
                    if feature in baseline_processed.columns and feature in cf_df.columns:
                        baseline_val = baseline_processed[feature].iloc[0]
                        cf_val = cf_df[feature].iloc[0]
                        change = cf_val - baseline_val
                        change_pct = (change / baseline_val * 100) if baseline_val != 0 else 0
                        
                        recommendations[feature] = {
                            'baseline': baseline_val,
                            'recommended': cf_val,
                            'change': change,
                            'change_pct': change_pct
                        }
            
            return recommendations, baseline_emissions, baseline_total, target_total
            
        except Exception as e:
            print(f"Error generating recommendations: {e}")
            return {}, None, None, None
