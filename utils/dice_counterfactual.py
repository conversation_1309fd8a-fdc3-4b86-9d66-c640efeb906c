import pandas as pd
import numpy as np
import dice_ml
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
import pickle
import os
from .model_training import ESGModelTrainer
from .data_preprocessing import ESGDataPreprocessor
from .emission_calculator import EmissionCalculator

class ESGDiceCounterfactual:
    """
    DiCE counterfactual analysis for ESG emission reduction
    """
    
    def __init__(self, models_dir='models', data_dir='data'):
        self.models_dir = models_dir
        self.data_dir = data_dir
        self.model_trainer = ESGModelTrainer()
        self.preprocessor = ESGDataPreprocessor()
        self.calculator = EmissionCalculator(data_dir, models_dir)
        
        # Load trained models
        try:
            self.model_trainer.load_models(models_dir)
            print(" Models loaded for DiCE analysis")
        except Exception as e:
            print(f" Error loading models: {e}")
    
    def prepare_dice_data(self, input_data):
        """Prepare data for DiCE analysis"""
        # Save temporarily for preprocessing with full path
        temp_file = os.path.join(self.data_dir, 'temp_dice_data.csv')
        input_data.to_csv(temp_file, index=False)

        # Preprocess data
        processed_data = self.preprocessor.preprocess_test_data('temp_dice_data.csv', self.data_dir)

        # Clean up
        if os.path.exists(temp_file):
            os.remove(temp_file)

        return processed_data
    
    def create_dice_wrapper_model(self, target_scope, target_variable):
        """Create a wrapper model for DiCE that uses existing trained models"""
        # Get the trained model for the specific target
        if target_scope not in self.model_trainer.models:
            raise ValueError(f"No models found for {target_scope}")

        if target_variable not in self.model_trainer.models[target_scope]:
            raise ValueError(f"No model found for {target_variable} in {target_scope}")

        # Get the trained XGBoost model
        trained_model = self.model_trainer.models[target_scope][target_variable]

        # Get feature names for this target
        if target_scope == 'scope1':
            feature_names = self.model_trainer.target_features_map_scope1[target_variable]
        elif target_scope == 'scope2':
            feature_names = self.model_trainer.target_features_map_scope2[target_variable]
        else:  # scope3
            feature_names = self.model_trainer.target_features_map_scope3[target_variable]

        # Filter to continuous features only (DiCE requirement)
        continuous_features = []
        for feature in feature_names:
            # Skip categorical encoded features
            if not any(cat in feature for cat in ['_Cold', '_Temperate', '_Tropical', '_Australia', '_India', '_UK',
                                                 '_VRF', '_Chiller', '_Packaged', '_R-410A', '_HFC-134a', '_R-1234yf',
                                                 '_FM-200', '_Novec', '_hybrid', '_diesel', '_petrol', '_air', '_car', '_rail',
                                                 '_e-waste', '_mixed', '_organic', '_plastic']):
                continuous_features.append(feature)

        return trained_model, continuous_features
    
    def generate_counterfactuals_for_target(self, baseline_data, target_scope, target_variable, target_reduction_pct):
        """Generate counterfactual recommendations for a specific target using existing trained models"""
        try:
            # Get the trained model and features for this target
            trained_model, continuous_features = self.create_dice_wrapper_model(target_scope, target_variable)

            # Load some training data for DiCE data interface
            df1, df2, df3 = self.preprocessor.load_training_data()
            if target_scope == 'scope1':
                training_sample = df1.head(200)
            elif target_scope == 'scope2':
                training_sample = df2.head(200)
            else:
                training_sample = df3.head(200)

            # Preprocess training sample
            training_processed = self.prepare_dice_data(training_sample)

            # Filter to available continuous features
            available_features = [f for f in continuous_features if f in training_processed.columns]

            if len(available_features) == 0:
                print(f"No continuous features available for {target_variable}")
                return None, None, None, None

            # Calculate baseline target value
            baseline_processed = self.prepare_dice_data(baseline_data)
            baseline_prediction = trained_model.predict(baseline_processed[available_features])[0]
            target_value = baseline_prediction * (1 - target_reduction_pct / 100)

            # Prepare DiCE data interface
            training_features_data = training_processed[available_features + [target_variable]]

            dice_data = dice_ml.Data(
                dataframe=training_features_data,
                continuous_features=available_features,
                outcome_name=target_variable
            )

            # Create DiCE model interface
            dice_model = dice_ml.Model(model=trained_model, backend="sklearn")

            # Create DiCE explainer
            dice_exp = dice_ml.Dice(dice_data, dice_model, method="random")

            # Prepare query instance
            query_instance = baseline_processed[available_features].head(1)

            # Generate counterfactuals
            counterfactuals = dice_exp.generate_counterfactuals(
                query_instance,
                total_CFs=2,
                desired_class=target_value  # Use desired_class for regression
            )

            return counterfactuals, available_features, baseline_prediction, target_value

        except Exception as e:
            print(f"Error in DiCE analysis for {target_variable}: {e}")
            return None, None, None, None
    
    def get_parameter_recommendations(self, baseline_data, scope1_reduction, scope2_reduction, scope3_reduction):
        """Get parameter recommendations using existing trained models"""
        recommendations = {}

        try:
            # Calculate baseline emissions using existing models
            baseline_emissions = self.calculator.predict_and_calculate_emissions(baseline_data)

            if baseline_emissions is None:
                print("Could not calculate baseline emissions")
                return {}, None, None, None

            # Define key targets for each scope to analyze
            scope_targets = {
                'scope1': ['diesel_fuel_consumption_l_per_day', 'natural_gas_consumption_m3_per_day'],
                'scope2': ['total_energy_consumption_kwh_per_day'],
                'scope3': ['business_travel_emissions_kgco2', 'waste_treatment_emissions_kgco2']
            }

            scope_reductions = {
                'scope1': scope1_reduction,
                'scope2': scope2_reduction,
                'scope3': scope3_reduction
            }

            # Generate recommendations for each key target
            for scope, targets in scope_targets.items():
                reduction_pct = scope_reductions[scope]
                if reduction_pct > 0:
                    for target in targets:
                        if target in self.model_trainer.models.get(scope, {}):
                            # Generate counterfactuals for this specific target
                            counterfactuals, features, baseline_val, target_val = self.generate_counterfactuals_for_target(
                                baseline_data, scope, target, reduction_pct
                            )

                            if counterfactuals is not None and len(counterfactuals.cf_examples_list) > 0:
                                cf_df = counterfactuals.cf_examples_list[0].final_cfs_df
                                baseline_processed = self.prepare_dice_data(baseline_data)

                                # Extract parameter changes
                                for feature in features:
                                    if feature in baseline_processed.columns and feature in cf_df.columns:
                                        baseline_feature_val = baseline_processed[feature].iloc[0]
                                        cf_feature_val = cf_df[feature].iloc[0]
                                        change = cf_feature_val - baseline_feature_val
                                        change_pct = (change / baseline_feature_val * 100) if baseline_feature_val != 0 else 0

                                        # Only include significant changes
                                        if abs(change_pct) > 1:  # More than 1% change
                                            recommendations[feature] = {
                                                'baseline': baseline_feature_val,
                                                'recommended': cf_feature_val,
                                                'change': change,
                                                'change_pct': change_pct,
                                                'target': target,
                                                'scope': scope
                                            }

            # Calculate total baseline and target emissions
            baseline_total = baseline_emissions['total_emissions'].iloc[0] if 'total_emissions' in baseline_emissions.columns else 0
            overall_reduction = (scope1_reduction + scope2_reduction + scope3_reduction) / 3
            target_total = baseline_total * (1 - overall_reduction / 100)

            return recommendations, baseline_emissions, baseline_total, target_total

        except Exception as e:
            print(f"Error generating recommendations: {e}")
            import traceback
            traceback.print_exc()
            return {}, None, None, None
