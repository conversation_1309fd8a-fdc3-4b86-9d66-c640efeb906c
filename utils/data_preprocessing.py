import pandas as pd
import numpy as np
import holidays
from sklearn.preprocessing import StandardScaler
from datetime import datetime
import pickle
import os

class ESGDataPreprocessor:
    """
    Data preprocessing pipeline for ESG emission prediction
    Based on the logic from untitled49.py
    """
    
    def __init__(self):
        self.scalers = {}
        self.exclude_cols = [
            'datacenter_id', 'date', 'year', 'month', 'day', 'dayofweek',
            'weekofyear', 'quarter', 'month_sin', 'month_cos', 'day_sin',
            'day_cos', 'dayofweek_sin', 'dayofweek_cos', 'isholiday',
            # Target variables (drivers)
            'diesel_fuel_consumption_l_per_day', 'natural_gas_consumption_m3_per_day',
            'refrigerant_leak_volume_kg_per_year', 'fire_suppression_discharges_per_year',
            'vehicle_fuel_consumption_l_per_day', 'total_energy_consumption_kwh_per_day',
            'grid_emission_factor_kgco2perkwh', 'business_travel_emissions_kgco2',
            'commuting_emissions_kgco2', 'waste_volume_kg', 'waste_treatment_emissions_kgco2',
            'product_units_sold', 'use_phase_emissions_kgco2', 'end_of_life_emissions_kgco2',
            'purchase_volume_kg', 'purchased_goods_emissions_kgco2'
        ]
    
    def load_training_data(self, data_dir='data'):
        """Load the three training datasets"""
        df1 = pd.read_csv(os.path.join(data_dir, 'scope1_train.csv'))
        df2 = pd.read_csv(os.path.join(data_dir, 'scope2_train.csv'))
        df3 = pd.read_csv(os.path.join(data_dir, 'scope3_train.csv'))
        return df1, df2, df3
    
    def handle_missing_values(self, df):
        """Handle missing values - fill categorical with 'unknown', numerical with median"""
        df_processed = df.copy()
        
        for col in df_processed.columns:
            if df_processed[col].isnull().any():
                if df_processed[col].dtype == 'object':
                    df_processed[col] = df_processed[col].fillna('unknown')
                else:
                    median_val = df_processed[col].median()
                    df_processed[col] = df_processed[col].fillna(median_val)
        
        return df_processed
    
    def create_temporal_features(self, df):
        """Create temporal features from date column"""
        df_processed = df.copy()
        
        # Convert date to datetime
        df_processed['date'] = pd.to_datetime(df_processed['date'])
        
        # Extract temporal features
        df_processed['year'] = df_processed['date'].dt.year
        df_processed['month'] = df_processed['date'].dt.month
        df_processed['day'] = df_processed['date'].dt.day
        df_processed['dayofweek'] = df_processed['date'].dt.dayofweek
        df_processed['weekofyear'] = df_processed['date'].dt.isocalendar().week.astype(int)
        df_processed['quarter'] = df_processed['date'].dt.quarter
        
        # Create cyclic features
        df_processed['month_sin'] = np.sin(2 * np.pi * df_processed['month'] / 12)
        df_processed['month_cos'] = np.cos(2 * np.pi * df_processed['month'] / 12)
        df_processed['day_sin'] = np.sin(2 * np.pi * df_processed['day'] / 31)
        df_processed['day_cos'] = np.cos(2 * np.pi * df_processed['day'] / 31)
        df_processed['dayofweek_sin'] = np.sin(2 * np.pi * df_processed['dayofweek'] / 7)
        df_processed['dayofweek_cos'] = np.cos(2 * np.pi * df_processed['dayofweek'] / 7)
        
        return df_processed
    
    def add_holiday_features(self, df):
        """Add holiday features based on country and date"""
        df_processed = df.copy()
        
        def is_holiday(row):
            country = row['location_country']
            date = row['date']
            try:
                country_holidays = holidays.CountryHoliday(country, years=date.year)
                return 1 if date in country_holidays else 0
            except KeyError:
                return 0
        
        df_processed['isholiday'] = df_processed.apply(is_holiday, axis=1)
        return df_processed
    
    def encode_categorical_features(self, df):
        """One-hot encode categorical features"""
        df_processed = df.copy()
        
        # Get categorical columns (excluding datacenter_id)
        categorical_cols = df_processed.select_dtypes(include=['object', 'category']).columns.tolist()
        if 'datacenter_id' in categorical_cols:
            categorical_cols.remove('datacenter_id')
        
        # One-hot encode
        df_processed = pd.get_dummies(df_processed, columns=categorical_cols)
        
        return df_processed
    
    def scale_numerical_features(self, df, fit_scaler=True, scaler_name='default'):
        """Scale numerical features"""
        df_processed = df.copy()
        
        # Get numerical columns to scale
        numerical_cols = df_processed.select_dtypes(include=np.number).columns.tolist()
        cols_to_scale = [col for col in numerical_cols if col not in self.exclude_cols]
        
        if fit_scaler:
            # Fit new scaler
            scaler = StandardScaler()
            df_processed[cols_to_scale] = scaler.fit_transform(df_processed[cols_to_scale])
            self.scalers[scaler_name] = scaler
        else:
            # Use existing scaler
            if scaler_name in self.scalers:
                df_processed[cols_to_scale] = self.scalers[scaler_name].transform(df_processed[cols_to_scale])
            else:
                raise ValueError(f"Scaler {scaler_name} not found. Fit scaler first.")
        
        return df_processed
    
    def convert_boolean_to_int(self, df):
        """Convert boolean columns to integers"""
        df_processed = df.copy()
        bool_cols = df_processed.select_dtypes(include='bool').columns
        for col in bool_cols:
            df_processed[col] = df_processed[col].astype(int)
        return df_processed
    
    def preprocess_training_data(self, data_dir='data'):
        """Complete preprocessing pipeline for training data"""
        print("Loading training data...")
        df1, df2, df3 = self.load_training_data(data_dir)
        
        processed_dfs = []
        
        for i, df in enumerate([df1, df2, df3], 1):
            print(f"Processing scope {i} data...")
            
            # Apply preprocessing steps
            df = self.handle_missing_values(df)
            df = self.create_temporal_features(df)
            df = self.add_holiday_features(df)
            df = self.encode_categorical_features(df)
            df = self.scale_numerical_features(df, fit_scaler=True, scaler_name=f'scope{i}')
            df = self.convert_boolean_to_int(df)
            
            processed_dfs.append(df)
        
        return processed_dfs
    
    def preprocess_test_data(self, test_file, data_dir='data'):
        """Preprocess test/upload data using fitted scalers"""
        print("Loading test data...")
        df = pd.read_csv(os.path.join(data_dir, test_file))
        
        print("Processing test data...")
        df = self.handle_missing_values(df)
        df = self.create_temporal_features(df)
        df = self.add_holiday_features(df)
        df = self.encode_categorical_features(df)
        
        # Note: We'll need to align columns with training data
        # This will be handled in the model training phase
        
        df = self.convert_boolean_to_int(df)
        
        return df
    
    def save_scalers(self, filepath='models/scalers.pkl'):
        """Save fitted scalers"""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'wb') as f:
            pickle.dump(self.scalers, f)
        print(f"Scalers saved to {filepath}")
    
    def load_scalers(self, filepath='models/scalers.pkl'):
        """Load fitted scalers"""
        with open(filepath, 'rb') as f:
            self.scalers = pickle.load(f)
        print(f"Scalers loaded from {filepath}")
    
    def time_split_datacenter(self, df, train_ratio=0.8):
        """Split data by time for each datacenter"""
        train_list = []
        test_list = []
        
        for datacenter in df['datacenter_id'].unique():
            datacenter_df = df[df['datacenter_id'] == datacenter].sort_values(by='date')
            split_index = int(len(datacenter_df) * train_ratio)
            train_list.append(datacenter_df.iloc[:split_index])
            test_list.append(datacenter_df.iloc[split_index:])
        
        train_df = pd.concat(train_list)
        test_df = pd.concat(test_list)
        
        return train_df, test_df
