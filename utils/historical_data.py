import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

class HistoricalDataManager:
    """
    Manager for historical emissions data
    Handles loading, processing, and aggregating historical emission data for dashboard
    """
    
    def __init__(self, data_dir='data'):
        self.data_dir = data_dir
        self.historical_data = None
        self.load_historical_data()
    
    def load_historical_data(self):
        """Load historical emissions data"""
        try:
            filepath = os.path.join(self.data_dir, 'historical_emissions.csv')
            if os.path.exists(filepath):
                self.historical_data = pd.read_csv(filepath)
                self.historical_data['date'] = pd.to_datetime(self.historical_data['date'])
                print("Historical emissions data loaded successfully")
            else:
                # Create synthetic historical data if file doesn't exist
                print(" Historical emissions file not found, creating synthetic data...")
                self.create_synthetic_historical_data()
        except Exception as e:
            print(f" Error loading historical data: {e}")
            self.create_synthetic_historical_data()
    
    def create_synthetic_historical_data(self):
        """Create synthetic historical data for demonstration"""
        print(" Creating synthetic historical emissions data...")
        
        # Date range for historical data
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2024, 12, 31)
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # Datacenter IDs
        datacenters = ['DC_001', 'DC_002', 'DC_003', 'DC_004', 'DC_005']
        countries = ['Australia', 'India', 'UK', 'Australia', 'India']
        
        historical_records = []
        
        for datacenter, country in zip(datacenters, countries):
            for date in date_range:
                # Generate synthetic emissions with some seasonality and randomness
                base_scope1 = 1000 + 200 * np.sin(2 * np.pi * date.dayofyear / 365) + np.random.normal(0, 100)
                base_scope2 = 1500 + 300 * np.sin(2 * np.pi * date.dayofyear / 365) + np.random.normal(0, 150)
                base_scope3 = 800 + 150 * np.sin(2 * np.pi * date.dayofyear / 365) + np.random.normal(0, 80)
                
                # Ensure non-negative values
                scope1_emissions = max(0, base_scope1)
                scope2_emissions = max(0, base_scope2)
                scope3_emissions = max(0, base_scope3)
                total_emissions = scope1_emissions + scope2_emissions + scope3_emissions
                
                record = {
                    'date': date,
                    'datacenter_id': datacenter,
                    'location_country': country,
                    'scope1_emissions': scope1_emissions,
                    'scope2_emissions': scope2_emissions,
                    'scope3_emissions': scope3_emissions,
                    'total_emissions': total_emissions
                }
                historical_records.append(record)
        
        self.historical_data = pd.DataFrame(historical_records)
        
        # Save synthetic data
        filepath = os.path.join(self.data_dir, 'historical_emissions.csv')
        self.historical_data.to_csv(filepath, index=False)
        print(f"Synthetic historical data saved to {filepath}")
    
    def get_historical_data(self, start_date=None, end_date=None, datacenter_id=None):
        """Get historical data with optional filtering"""
        if self.historical_data is None:
            return pd.DataFrame()
        
        filtered_data = self.historical_data.copy()
        
        # Filter by date range
        if start_date:
            filtered_data = filtered_data[filtered_data['date'] >= pd.to_datetime(start_date)]
        if end_date:
            filtered_data = filtered_data[filtered_data['date'] <= pd.to_datetime(end_date)]
        
        # Filter by datacenter
        if datacenter_id:
            filtered_data = filtered_data[filtered_data['datacenter_id'] == datacenter_id]
        
        return filtered_data
    
    def get_daily_aggregates(self, start_date=None, end_date=None):
        """Get daily aggregated emissions across all datacenters"""
        data = self.get_historical_data(start_date, end_date)
        
        if data.empty:
            return pd.DataFrame()
        
        daily_agg = data.groupby('date').agg({
            'scope1_emissions': 'sum',
            'scope2_emissions': 'sum',
            'scope3_emissions': 'sum',
            'total_emissions': 'sum'
        }).reset_index()
        
        return daily_agg
    
    def get_datacenter_summary(self, start_date=None, end_date=None):
        """Get summary statistics by datacenter"""
        data = self.get_historical_data(start_date, end_date)
        
        if data.empty:
            return pd.DataFrame()
        
        datacenter_summary = data.groupby(['datacenter_id', 'location_country']).agg({
            'scope1_emissions': ['sum', 'mean', 'std'],
            'scope2_emissions': ['sum', 'mean', 'std'],
            'scope3_emissions': ['sum', 'mean', 'std'],
            'total_emissions': ['sum', 'mean', 'std']
        }).round(2)
        
        # Flatten column names
        datacenter_summary.columns = ['_'.join(col).strip() for col in datacenter_summary.columns]
        datacenter_summary = datacenter_summary.reset_index()
        
        return datacenter_summary
    
    def get_monthly_trends(self, start_date=None, end_date=None):
        """Get monthly emission trends"""
        data = self.get_historical_data(start_date, end_date)
        
        if data.empty:
            return pd.DataFrame()
        
        data['year_month'] = data['date'].dt.to_period('M')
        
        monthly_trends = data.groupby('year_month').agg({
            'scope1_emissions': 'sum',
            'scope2_emissions': 'sum',
            'scope3_emissions': 'sum',
            'total_emissions': 'sum'
        }).reset_index()
        
        monthly_trends['year_month'] = monthly_trends['year_month'].astype(str)
        
        return monthly_trends
    
    def get_country_breakdown(self, start_date=None, end_date=None):
        """Get emissions breakdown by country"""
        data = self.get_historical_data(start_date, end_date)
        
        if data.empty:
            return pd.DataFrame()
        
        country_breakdown = data.groupby('location_country').agg({
            'scope1_emissions': 'sum',
            'scope2_emissions': 'sum',
            'scope3_emissions': 'sum',
            'total_emissions': 'sum'
        }).reset_index()
        
        return country_breakdown
    
    def get_emission_intensity_metrics(self, start_date=None, end_date=None):
        """Calculate emission intensity metrics"""
        data = self.get_historical_data(start_date, end_date)
        
        if data.empty:
            return {}
        
        total_emissions = data['total_emissions'].sum()
        total_days = len(data['date'].unique())
        total_datacenters = len(data['datacenter_id'].unique())
        
        metrics = {
            'total_emissions_kg_co2e': total_emissions,
            'avg_daily_emissions': total_emissions / total_days if total_days > 0 else 0,
            'avg_datacenter_emissions': total_emissions / total_datacenters if total_datacenters > 0 else 0,
            'scope1_percentage': (data['scope1_emissions'].sum() / total_emissions * 100) if total_emissions > 0 else 0,
            'scope2_percentage': (data['scope2_emissions'].sum() / total_emissions * 100) if total_emissions > 0 else 0,
            'scope3_percentage': (data['scope3_emissions'].sum() / total_emissions * 100) if total_emissions > 0 else 0
        }
        
        return metrics
    
    def get_top_emitting_datacenters(self, start_date=None, end_date=None, top_n=5):
        """Get top emitting datacenters"""
        data = self.get_historical_data(start_date, end_date)
        
        if data.empty:
            return pd.DataFrame()
        
        datacenter_totals = data.groupby(['datacenter_id', 'location_country']).agg({
            'total_emissions': 'sum'
        }).reset_index()
        
        top_datacenters = datacenter_totals.nlargest(top_n, 'total_emissions')
        
        return top_datacenters
    
    def get_emission_trends_analysis(self, start_date=None, end_date=None):
        """Analyze emission trends over time"""
        daily_data = self.get_daily_aggregates(start_date, end_date)
        
        if daily_data.empty or len(daily_data) < 2:
            return {}
        
        # Calculate trend metrics
        first_week = daily_data.head(7)['total_emissions'].mean()
        last_week = daily_data.tail(7)['total_emissions'].mean()
        
        trend_change = ((last_week - first_week) / first_week * 100) if first_week > 0 else 0
        
        analysis = {
            'trend_direction': 'increasing' if trend_change > 5 else 'decreasing' if trend_change < -5 else 'stable',
            'trend_change_percentage': trend_change,
            'peak_emission_date': daily_data.loc[daily_data['total_emissions'].idxmax(), 'date'],
            'peak_emission_value': daily_data['total_emissions'].max(),
            'lowest_emission_date': daily_data.loc[daily_data['total_emissions'].idxmin(), 'date'],
            'lowest_emission_value': daily_data['total_emissions'].min(),
            'average_daily_emission': daily_data['total_emissions'].mean(),
            'emission_volatility': daily_data['total_emissions'].std()
        }
        
        return analysis
